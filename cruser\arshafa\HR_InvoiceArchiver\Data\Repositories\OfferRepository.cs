using System.Collections.Generic;
using System.Linq;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Data.Repositories
{
    public class OfferRepository
    {
        private readonly DatabaseContext _context;
        public OfferRepository(DatabaseContext context)
        {
            _context = context;
        }

        public IEnumerable<Offer> GetAll() => _context.Offers.ToList();
        public Offer GetById(int id) => _context.Offers.FirstOrDefault(o => o.Id == id);
        public void Add(Offer offer)
        {
            _context.Offers.Add(offer);
            _context.SaveChanges();
        }
        public void Update(Offer offer)
        {
            _context.Offers.Update(offer);
            _context.SaveChanges();
        }
        public void Delete(int id)
        {
            var offer = _context.Offers.FirstOrDefault(o => o.Id == id);
            if (offer != null)
            {
                _context.Offers.Remove(offer);
                _context.SaveChanges();
            }
        }
        public IEnumerable<ScientificName> GetScientificNames()
        {
            try
            {
                return _context.ScientificNames.ToList();
            }
            catch (Exception)
            {
                // إذا كان الجدول غير موجود، قم بإنشاؤه وإرجاع قائمة فارغة
                try
                {
                    _context.Database.EnsureCreated();
                    return _context.ScientificNames.ToList();
                }
                catch
                {
                    return new List<ScientificName>();
                }
            }
        }
        public void AddScientificName(string name)
        {
            if (!_context.ScientificNames.Any(s => s.Name == name))
            {
                _context.ScientificNames.Add(new ScientificName { Name = name });
                _context.SaveChanges();
            }
        }
    }
} 