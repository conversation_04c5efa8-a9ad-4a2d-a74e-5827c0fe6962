using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// خدمة التخزين المؤقت المحسنة للأداء
    /// </summary>
    public interface ICacheService
    {
        Task<T?> GetAsync<T>(string key) where T : class;
        Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class;
        Task RemoveAsync(string key);
        Task ClearAsync();
        Task<BitmapImage?> GetImageAsync(string imagePath);
        Task SetImageAsync(string imagePath, BitmapImage image);
        void ClearExpiredItems();
        long GetCacheSize();
    }

    public class CacheService : ICacheService
    {
        private readonly ConcurrentDictionary<string, CacheItem> _cache = new();
        private readonly ConcurrentDictionary<string, BitmapImage> _imageCache = new();
        private readonly TimeSpan _defaultExpiry = TimeSpan.FromMinutes(30);
        private readonly long _maxCacheSize = 100 * 1024 * 1024; // 100 MB
        private long _currentCacheSize = 0;

        private class CacheItem
        {
            public object Value { get; set; } = null!;
            public DateTime ExpiryTime { get; set; }
            public long Size { get; set; }
        }

        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            await Task.CompletedTask; // للتوافق مع async pattern

            if (_cache.TryGetValue(key, out var item))
            {
                if (DateTime.UtcNow <= item.ExpiryTime)
                {
                    return item.Value as T;
                }
                else
                {
                    // إزالة العنصر المنتهي الصلاحية
                    _cache.TryRemove(key, out _);
                    Interlocked.Add(ref _currentCacheSize, -item.Size);
                }
            }

            return null;
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
        {
            await Task.CompletedTask; // للتوافق مع async pattern

            var expiryTime = DateTime.UtcNow.Add(expiry ?? _defaultExpiry);
            var size = EstimateObjectSize(value);

            // التحقق من حجم Cache
            if (_currentCacheSize + size > _maxCacheSize)
            {
                await ClearOldestItemsAsync();
            }

            var cacheItem = new CacheItem
            {
                Value = value,
                ExpiryTime = expiryTime,
                Size = size
            };

            if (_cache.TryAdd(key, cacheItem))
            {
                Interlocked.Add(ref _currentCacheSize, size);
            }
            else
            {
                // تحديث العنصر الموجود
                if (_cache.TryGetValue(key, out var existingItem))
                {
                    Interlocked.Add(ref _currentCacheSize, -existingItem.Size);
                }
                _cache[key] = cacheItem;
                Interlocked.Add(ref _currentCacheSize, size);
            }
        }

        public async Task RemoveAsync(string key)
        {
            await Task.CompletedTask;

            if (_cache.TryRemove(key, out var item))
            {
                Interlocked.Add(ref _currentCacheSize, -item.Size);
            }
        }

        public async Task ClearAsync()
        {
            await Task.CompletedTask;
            _cache.Clear();
            _imageCache.Clear();
            Interlocked.Exchange(ref _currentCacheSize, 0);
        }

        public async Task<BitmapImage?> GetImageAsync(string imagePath)
        {
            await Task.CompletedTask;

            if (_imageCache.TryGetValue(imagePath, out var cachedImage))
            {
                return cachedImage;
            }

            // تحميل الصورة إذا لم تكن موجودة في Cache
            if (File.Exists(imagePath))
            {
                try
                {
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(imagePath);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad; // تحسين الأداء
                    bitmap.EndInit();
                    bitmap.Freeze(); // تحسين الأداء في Threading

                    _imageCache[imagePath] = bitmap;
                    return bitmap;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error loading image {imagePath}: {ex.Message}");
                    return null;
                }
            }

            return null;
        }

        public async Task SetImageAsync(string imagePath, BitmapImage image)
        {
            await Task.CompletedTask;
            _imageCache[imagePath] = image;
        }

        public void ClearExpiredItems()
        {
            var now = DateTime.UtcNow;
            var expiredKeys = new List<string>();

            foreach (var kvp in _cache)
            {
                if (now > kvp.Value.ExpiryTime)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                if (_cache.TryRemove(key, out var item))
                {
                    Interlocked.Add(ref _currentCacheSize, -item.Size);
                }
            }
        }

        public long GetCacheSize()
        {
            return _currentCacheSize;
        }

        private async Task ClearOldestItemsAsync()
        {
            await Task.CompletedTask;

            var itemsToRemove = new List<KeyValuePair<string, CacheItem>>();
            
            // إزالة 25% من العناصر الأقدم
            var targetRemovalCount = Math.Max(1, _cache.Count / 4);
            
            foreach (var kvp in _cache.OrderBy(x => x.Value.ExpiryTime).Take(targetRemovalCount))
            {
                itemsToRemove.Add(kvp);
            }

            foreach (var item in itemsToRemove)
            {
                if (_cache.TryRemove(item.Key, out var removedItem))
                {
                    Interlocked.Add(ref _currentCacheSize, -removedItem.Size);
                }
            }
        }

        private long EstimateObjectSize(object obj)
        {
            // تقدير تقريبي لحجم الكائن
            return obj switch
            {
                string str => str.Length * 2, // Unicode characters
                byte[] bytes => bytes.Length,
                _ => 1024 // تقدير افتراضي 1KB
            };
        }
    }
}
