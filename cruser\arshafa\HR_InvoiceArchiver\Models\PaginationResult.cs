using System.Collections.Generic;

namespace HR_InvoiceArchiver.Models
{
    /// <summary>
    /// نتيجة الصفحات المقسمة لتحسين الأداء
    /// </summary>
    /// <typeparam name="T">نوع البيانات</typeparam>
    public class PaginationResult<T>
    {
        public IEnumerable<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }

    /// <summary>
    /// معايير الصفحات
    /// </summary>
    public class PaginationCriteria
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50; // افتراضي 50 عنصر
        public string? SearchTerm { get; set; }
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = true;
    }
}
