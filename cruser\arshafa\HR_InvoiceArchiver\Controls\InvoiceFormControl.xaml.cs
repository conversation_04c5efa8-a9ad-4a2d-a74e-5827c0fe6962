using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using MaterialDesignThemes.Wpf;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Win32;

namespace HR_InvoiceArchiver.Controls
{
    public partial class InvoiceFormControl : UserControl, INotifyPropertyChanged
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ISupplierService _supplierService;
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        
        private Invoice? _currentInvoice;
        private bool _isEditMode;
        
        // Binding Properties
        private string _invoiceNumber = string.Empty;
        private DateTime _invoiceDate = DateTime.Now;
        private DateTime? _dueDate;
        private string _description = string.Empty;
        private decimal _totalAmount;
        private InvoiceStatus _status = InvoiceStatus.Unpaid;
        private string _attachmentPath = string.Empty;
        private Supplier? _selectedSupplier;
        private ObservableCollection<Supplier> _suppliers = new();
        private ObservableCollection<InvoiceStatus> _statusOptions = new();
        
        // Payment Properties (Smart Logic)
        private string _receiptNumber = string.Empty;
        private DateTime _paymentDate = DateTime.Now;
        private PaymentMethod _paymentMethod = PaymentMethod.Cash;
        private PaymentStatus _paymentStatus = PaymentStatus.FullPayment;
        private decimal _paymentAmount;
        private decimal _discountAmount;
        private string _paymentNotes = string.Empty;
        private string _paymentAttachmentPath = string.Empty;
        private ObservableCollection<PaymentMethod> _paymentMethods = new();

        // UI State Management
        private bool _isUpdatingAutomatically = false;
        private DispatcherTimer? _autoCloseTimer;


        public event EventHandler<InvoiceFormEventArgs>? FormClosed;

        public InvoiceFormControl()
        {
            // Initialize services from DI container
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _paymentService = App.ServiceProvider.GetRequiredService<IPaymentService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
            
            InitializeComponent();
            DataContext = this;
            
            InitializeOptions();
            _ = LoadSuppliersAsync();
            
            // Start slide-in animation and center form
            Loaded += (s, e) =>
            {
                CenterFormOnScreen();
                StartSlideInAnimation();
            };
        }

        public InvoiceFormControl(Invoice invoice) : this()
        {
            _currentInvoice = invoice;
            _isEditMode = true;
            Loaded += (s, e) =>
            {
                FormTitleTextBlock.Text = "تعديل الفاتورة";
                LoadInvoiceData();
            };
        }

        #region Properties

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set => SetProperty(ref _invoiceNumber, value);
        }

        public DateTime InvoiceDate
        {
            get => _invoiceDate;
            set => SetProperty(ref _invoiceDate, value);
        }

        public DateTime? DueDate
        {
            get => _dueDate;
            set => SetProperty(ref _dueDate, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set => SetProperty(ref _totalAmount, value);
        }

        public InvoiceStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public string AttachmentPath
        {
            get => _attachmentPath;
            set => SetProperty(ref _attachmentPath, value);
        }

        public Supplier? SelectedSupplier
        {
            get => _selectedSupplier;
            set => SetProperty(ref _selectedSupplier, value);
        }

        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set => SetProperty(ref _suppliers, value);
        }

        public ObservableCollection<InvoiceStatus> StatusOptions
        {
            get => _statusOptions;
            set => SetProperty(ref _statusOptions, value);
        }

        // Payment Properties
        public string ReceiptNumber
        {
            get => _receiptNumber;
            set => SetProperty(ref _receiptNumber, value);
        }

        public DateTime PaymentDate
        {
            get => _paymentDate;
            set => SetProperty(ref _paymentDate, value);
        }

        public PaymentMethod PaymentMethod
        {
            get => _paymentMethod;
            set => SetProperty(ref _paymentMethod, value);
        }

        public PaymentStatus PaymentStatus
        {
            get => _paymentStatus;
            set => SetProperty(ref _paymentStatus, value);
        }

        public decimal PaymentAmount
        {
            get => _paymentAmount;
            set => SetProperty(ref _paymentAmount, value);
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set => SetProperty(ref _discountAmount, value);
        }

        public string PaymentNotes
        {
            get => _paymentNotes;
            set => SetProperty(ref _paymentNotes, value);
        }

        public string PaymentAttachmentPath
        {
            get => _paymentAttachmentPath;
            set => SetProperty(ref _paymentAttachmentPath, value);
        }

        public ObservableCollection<PaymentMethod> PaymentMethods
        {
            get => _paymentMethods;
            set => SetProperty(ref _paymentMethods, value);
        }

        #endregion

        #region Initialization

        private void InitializeOptions()
        {
            StatusOptions = new ObservableCollection<InvoiceStatus>
            {
                InvoiceStatus.Unpaid,
                InvoiceStatus.PartiallyPaid,
                InvoiceStatus.Paid
            };

            PaymentMethods = new ObservableCollection<PaymentMethod>
            {
                PaymentMethod.Cash,
                PaymentMethod.Check,
                PaymentMethod.BankTransfer,
                PaymentMethod.CreditCard
            };

            // Set default values
            Status = InvoiceStatus.Unpaid;
            InvoiceDate = DateTime.Now;

            // Set default selection for StatusComboBox
            Loaded += (s, e) =>
            {
                if (StatusComboBox.Items.Count > 0)
                {
                    StatusComboBox.SelectedIndex = 0; // Select "غير مسددة" by default
                }
            };
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierService.GetAllSuppliersAsync();
                await Dispatcher.InvokeAsync(() =>
                {
                    Suppliers = new ObservableCollection<Supplier>(suppliers);
                });
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تحميل قائمة الموردين: {ex.Message}");
            }
        }

        private void LoadInvoiceData()
        {
            if (_currentInvoice == null) return;

            InvoiceNumber = _currentInvoice.InvoiceNumber;
            InvoiceDate = _currentInvoice.InvoiceDate;
            DueDate = _currentInvoice.DueDate;
            Description = _currentInvoice.Description ?? string.Empty;
            TotalAmount = _currentInvoice.Amount;
            Status = _currentInvoice.Status;
            AttachmentPath = _currentInvoice.AttachmentPath ?? string.Empty;
            
            // Set payment amount to total amount if paid
            if (Status == InvoiceStatus.Paid)
            {
                PaymentAmount = TotalAmount;
                PaymentDate = InvoiceDate; // Default to invoice date
            }
            
            // Set selected supplier after suppliers are loaded
            if (_currentInvoice.SupplierId > 0)
            {
                Dispatcher.BeginInvoke(new Action(async () =>
                {
                    await Task.Delay(100); // Wait for suppliers to load
                    SelectedSupplier = Suppliers.FirstOrDefault(s => s.Id == _currentInvoice.SupplierId);
                }));
            }
        }

        #endregion

        #region Animations

        private void StartSlideInAnimation()
        {
            var storyboard = (Storyboard)Resources["SlideInAnimation"];
            storyboard.Begin();
        }

        private void StartSlideOutAnimation()
        {
            var storyboard = (Storyboard)Resources["SlideOutAnimation"];
            storyboard.Begin();
        }

        private void StartSaveSuccessAnimation()
        {
            var storyboard = (Storyboard)Resources["SaveSuccessAnimation"];
            storyboard.Begin();
        }

        private void SlideOutAnimation_Completed(object sender, EventArgs e)
        {
            FormClosed?.Invoke(this, new InvoiceFormEventArgs { Success = false });
        }

        #endregion

        #region Event Handlers

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (StatusComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
            {
                string statusTag = selectedItem.Tag.ToString() ?? "";

                // تحديث حالة الفاتورة الأساسية (هذا يحدد الحالة العامة للفاتورة)
                Status = statusTag switch
                {
                    "Unpaid" => InvoiceStatus.Unpaid,
                    "PartiallyPaid" => InvoiceStatus.PartiallyPaid,
                    "Paid" => InvoiceStatus.Paid,
                    "PaidWithDiscount" => InvoiceStatus.PaidWithDiscount,
                    _ => InvoiceStatus.Unpaid
                };
            }

            // إظهار حقول الدفعة المرفقة فقط للحالات المسددة
            // ملاحظة: هذا اختياري - يمكن إضافة دفعة مع الفاتورة أو تركها وإضافة الدفعات لاحقاً
            bool showPaymentFields = Status == InvoiceStatus.Paid ||
                                   Status == InvoiceStatus.PartiallyPaid ||
                                   Status == InvoiceStatus.PaidWithDiscount;

            PaymentInfoCard.Visibility = showPaymentFields ? Visibility.Visible : Visibility.Collapsed;
            PaymentAttachmentGrid.Visibility = showPaymentFields ? Visibility.Visible : Visibility.Collapsed;

            if (showPaymentFields)
            {
                // Set default payment date to invoice date
                PaymentDate = InvoiceDate;

                // Set payment amount based on status
                if (PaymentAmount == 0)
                {
                    switch (Status)
                    {
                        case InvoiceStatus.Paid:
                        case InvoiceStatus.PaidWithDiscount:
                            PaymentAmount = TotalAmount;
                            PaymentStatus = PaymentStatus.FullPayment;
                            break;
                        case InvoiceStatus.PartiallyPaid:
                            PaymentAmount = TotalAmount * 0.5m; // Default to 50%
                            PaymentStatus = PaymentStatus.PartialPayment;
                            break;
                    }
                }

                // Show status guidance
                ShowStatusGuidance();
            }
            else
            {
                // Hide status indicator when payment fields are hidden
                // StatusIndicatorPanel.Visibility = Visibility.Collapsed;
            }
        }

        private void BackgroundOverlay_MouseDown(object sender, MouseButtonEventArgs e)
        {
            // Close form when clicking outside the card
            if (e.Source == sender)
            {
                StartSlideOutAnimation();
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            StartSlideOutAnimation();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            StartSlideOutAnimation();
        }

        private void BrowseAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار مرفق الفاتورة",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات الصور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|ملفات Word (*.doc;*.docx)|*.doc;*.docx|ملفات Excel (*.xls;*.xlsx)|*.xls;*.xlsx",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                AttachmentPath = openFileDialog.FileName;
            }
        }

        private void BrowsePaymentAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار مرفق الوصل",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات الصور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                PaymentAttachmentPath = openFileDialog.FileName;
            }
        }

        private void PaymentStatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PaymentStatusComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Content != null)
            {
                string status = selectedItem.Content.ToString() ?? "";

                // إظهار/إخفاء حقل الخصم حسب نوع الدفعة المختارة
                if (status == "دفعة مع خصم")
                {
                    DiscountAmountTextBox.Visibility = Visibility.Visible;
                }
                else
                {
                    DiscountAmountTextBox.Visibility = Visibility.Collapsed;
                    DiscountAmount = 0;
                    DiscountAmountTextBox.Text = "";
                }

                // إظهار إرشادات لنوع الدفعة المختارة
                ShowPaymentStatusGuidance(status);
            }
        }

        private void PaymentMethodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PaymentMethodComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
            {
                string methodTag = selectedItem.Tag.ToString() ?? "";

                // تحديث طريقة الدفع حسب الاختيار
                PaymentMethod = methodTag switch
                {
                    "Cash" => PaymentMethod.Cash,
                    "CreditCard" => PaymentMethod.CreditCard,
                    _ => PaymentMethod.Cash
                };
            }
        }

        private void PaymentAmountTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only numbers and decimal point
            e.Handled = !IsTextAllowed(e.Text);
        }

        private void PaymentAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingAutomatically) return;

            if (sender is TextBox textBox && decimal.TryParse(textBox.Text, out decimal paymentAmount))
            {
                PaymentAmount = paymentAmount;
                ValidatePaymentAmount(paymentAmount);
            }
        }

        private void DiscountAmountTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only numbers and decimal point
            e.Handled = !IsTextAllowed(e.Text);
        }

        private void DiscountAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingAutomatically) return;

            if (sender is TextBox textBox && decimal.TryParse(textBox.Text, out decimal discount))
            {
                DiscountAmount = discount;

                // Calculate payment amount automatically
                decimal calculatedPaymentAmount = TotalAmount - discount;
                if (calculatedPaymentAmount >= 0)
                {
                    _isUpdatingAutomatically = true;
                    PaymentAmount = calculatedPaymentAmount;
                    PaymentAmountTextBox.Text = calculatedPaymentAmount.ToString("F0");
                    _isUpdatingAutomatically = false;

                    // Update status guidance
                    ShowPaymentStatusIndicator($"✅ تسديد وبخصم - المبلغ المدفوع: {calculatedPaymentAmount:F0} د.ع", "#9C27B0", "CheckCircle");
                }
            }
        }

        // private void AddAnotherButton_Click(object sender, RoutedEventArgs e)
        // {
        //     // Stop auto-close timer if running
        //     _autoCloseTimer?.Stop();

        //     // Reset form for new entry
        //     ResetForm();

        //     // Hide success message
        //     HideSuccessToast();
        // }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                SaveButton.IsEnabled = false;

                // Enhanced save button with icon
                var saveContent = new StackPanel { Orientation = Orientation.Horizontal };
                saveContent.Children.Add(new PackIcon
                {
                    Kind = PackIconKind.Loading,
                    Width = 16,
                    Height = 16,
                    Margin = new Thickness(0, 0, 6, 0)
                });
                saveContent.Children.Add(new TextBlock { Text = "جاري الحفظ..." });
                SaveButton.Content = saveContent;

                var invoice = CreateInvoiceFromInput();

                if (_isEditMode && _currentInvoice != null)
                {
                    invoice.Id = _currentInvoice.Id;
                    await _invoiceService.UpdateInvoiceAsync(invoice);
                }
                else
                {
                    await _invoiceService.CreateInvoiceAsync(invoice);
                }

                // Enhanced payment handling for all paid statuses
                if ((Status == InvoiceStatus.Paid || Status == InvoiceStatus.PartiallyPaid || Status == InvoiceStatus.PaidWithDiscount)
                    && !string.IsNullOrWhiteSpace(ReceiptNumber))
                {
                    var payment = CreatePaymentFromInput(invoice.Id);
                    await _paymentService.CreatePaymentAsync(payment);
                }

                // Start success animation
                StartSaveSuccessAnimation();

                // Show enhanced success toast with auto-close and add another option
                string successMessage = _isEditMode ? "تم تحديث الفاتورة بنجاح!" : "تم حفظ الفاتورة بنجاح!";
                ShowSuccessToastWithAutoClose(successMessage, !_isEditMode);

                // Always invoke FormClosed event to refresh the invoices list
                await Task.Delay(2000);
                FormClosed?.Invoke(this, new InvoiceFormEventArgs { Success = true, Invoice = invoice });

                // Don't auto-close if not in edit mode (allow adding more invoices)
                if (!_isEditMode)
                {
                    // Reset form for adding another invoice
                    ResetFormForNewInvoice();
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في الحفظ", $"فشل في حفظ الفاتورة: {ex.Message}");

                // Show validation error in form
                ShowValidationError($"فشل في الحفظ: {ex.Message}");
            }
            finally
            {
                SaveButton.IsEnabled = true;

                // Reset save button content
                var resetContent = new StackPanel { Orientation = Orientation.Horizontal };
                resetContent.Children.Add(new PackIcon
                {
                    Kind = PackIconKind.ContentSave,
                    Width = 18,
                    Height = 18,
                    Margin = new Thickness(0, 0, 8, 0)
                });
                resetContent.Children.Add(new TextBlock { Text = "حفظ الفاتورة" });
                SaveButton.Content = resetContent;
            }
        }

        #endregion

        #region Validation and Data Creation

        private bool ValidateInput()
        {
            HideValidationError();

            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(InvoiceNumber))
                errors.Add("رقم الفاتورة مطلوب");

            if (SelectedSupplier == null)
                errors.Add("يجب اختيار المورد");

            if (TotalAmount <= 0)
                errors.Add("المبلغ الإجمالي يجب أن يكون أكبر من صفر");

            if (InvoiceDate > DateTime.Now.AddDays(1))
                errors.Add("تاريخ الفاتورة لا يمكن أن يكون في المستقبل");

            if (DueDate.HasValue && DueDate < InvoiceDate)
                errors.Add("تاريخ الاستحقاق لا يمكن أن يكون قبل تاريخ الفاتورة");

            // Enhanced validation for payment fields
            if (Status == InvoiceStatus.Paid || Status == InvoiceStatus.PartiallyPaid || Status == InvoiceStatus.PaidWithDiscount)
            {
                if (string.IsNullOrWhiteSpace(ReceiptNumber))
                    errors.Add("رقم الوصل مطلوب عند تحديد حالة التسديد");

                if (PaymentAmount <= 0)
                    errors.Add("مبلغ الدفع يجب أن يكون أكبر من صفر");

                if (PaymentDate > DateTime.Now.AddDays(1))
                    errors.Add("تاريخ الدفع لا يمكن أن يكون في المستقبل");

                // Specific validation based on payment status
                var currentSelection = PaymentStatusComboBox.SelectedItem as ComboBoxItem;
                string currentStatus = currentSelection?.Content?.ToString() ?? "";

                switch (currentStatus)
                {
                    case "تسديد جزئي":
                        if (PaymentAmount >= TotalAmount)
                            errors.Add("مبلغ التسديد الجزئي يجب أن يكون أقل من المبلغ الإجمالي");
                        break;

                    case "تسديد كامل":
                        if (PaymentAmount < TotalAmount)
                            errors.Add("مبلغ التسديد الكامل يجب أن يساوي المبلغ الإجمالي على الأقل");
                        break;

                    case "تسديد وبخصم":
                        if (PaymentAmount >= TotalAmount)
                            errors.Add("مبلغ الدفع مع الخصم يجب أن يكون أقل من المبلغ الإجمالي");
                        if (DiscountAmount <= 0)
                            errors.Add("مبلغ الخصم يجب أن يكون أكبر من صفر");
                        if (PaymentAmount + DiscountAmount != TotalAmount)
                            errors.Add("مجموع المبلغ المدفوع والخصم يجب أن يساوي المبلغ الإجمالي");
                        break;
                }
            }

            if (errors.Any())
            {
                ShowValidationError(string.Join("\n", errors));
                return false;
            }

            return true;
        }

        private Invoice CreateInvoiceFromInput()
        {
            return new Invoice
            {
                InvoiceNumber = InvoiceNumber.Trim(),
                InvoiceDate = InvoiceDate,
                DueDate = DueDate,
                Description = Description?.Trim(),
                Amount = TotalAmount,
                Status = Status,
                PaidAmount = GetCalculatedPaidAmount(),
                SupplierId = SelectedSupplier!.Id,
                Supplier = SelectedSupplier,
                AttachmentPath = string.IsNullOrWhiteSpace(AttachmentPath) ? null : AttachmentPath.Trim(),
                CreatedDate = _isEditMode ? _currentInvoice!.CreatedDate : DateTime.Now,
                UpdatedDate = DateTime.Now
            };
        }

        private decimal GetCalculatedPaidAmount()
        {
            return Status switch
            {
                InvoiceStatus.Paid => TotalAmount,
                InvoiceStatus.PartiallyPaid => PaymentAmount,
                InvoiceStatus.PaidWithDiscount => PaymentAmount,
                _ => 0
            };
        }

        private void ShowValidationError(string message)
        {
            // ValidationMessageTextBlock.Text = message;
            // ValidationMessageBorder.Visibility = Visibility.Visible;
        }

        private void HideValidationError()
        {
            // ValidationMessageBorder.Visibility = Visibility.Collapsed;
        }

        private void ResetForm()
        {
            // Reset basic fields
            InvoiceNumber = string.Empty;
            Description = string.Empty;
            TotalAmount = 0;
            InvoiceDate = DateTime.Now;
            DueDate = null;
            Status = InvoiceStatus.Unpaid;
            SelectedSupplier = null;
            AttachmentPath = string.Empty;

            // Reset payment fields
            ReceiptNumber = string.Empty;
            PaymentAmount = 0;
            DiscountAmount = 0;
            PaymentDate = DateTime.Now;
            PaymentMethod = PaymentMethod.Cash;
            PaymentStatus = PaymentStatus.FullPayment;
            PaymentNotes = string.Empty;
            PaymentAttachmentPath = string.Empty;

            // Reset UI state
            PaymentInfoCard.Visibility = Visibility.Collapsed;
            PaymentAttachmentGrid.Visibility = Visibility.Collapsed;
            // StatusIndicatorPanel.Visibility = Visibility.Collapsed;
            // PaymentStatusIndicatorPanel.Visibility = Visibility.Collapsed;
            DiscountAmountTextBox.Visibility = Visibility.Collapsed;
            // AddAnotherButton.Visibility = Visibility.Collapsed;
            HideValidationError();

            // Reset ComboBox selections
            StatusComboBox.SelectedIndex = -1;
            PaymentStatusComboBox.SelectedIndex = -1;

            // تعيين الاختيار الافتراضي لطريقة الدفع (نقدي)
            if (PaymentMethodComboBox.Items.Count > 0)
            {
                PaymentMethodComboBox.SelectedIndex = 0; // نقدي
            }
        }

        private Payment CreatePaymentFromInput(int invoiceId)
        {
            return new Payment
            {
                ReceiptNumber = ReceiptNumber.Trim(),
                InvoiceId = invoiceId,
                Amount = PaymentAmount,
                PaymentDate = PaymentDate,
                Method = PaymentMethod,
                Notes = PaymentNotes?.Trim(),
                AttachmentPath = string.IsNullOrWhiteSpace(PaymentAttachmentPath) ? null : PaymentAttachmentPath.Trim(),
                CreatedDate = DateTime.Now
            };
        }

        private void ShowSuccessToast()
        {
            string message = _isEditMode ? "تم تحديث الفاتورة بنجاح وحفظ جميع التغييرات" : "تم إنشاء الفاتورة بنجاح وإضافتها إلى النظام";

            // Create and show enhanced success notification
            var successNotification = new SuccessNotification();

            // Find the parent window to add the notification
            var parentWindow = Window.GetWindow(this);
            if (parentWindow != null && parentWindow.Content is Grid parentGrid)
            {
                // Add notification to the top of the parent grid
                Grid.SetRowSpan(successNotification, parentGrid.RowDefinitions.Count > 0 ? parentGrid.RowDefinitions.Count : 1);
                Grid.SetColumnSpan(successNotification, parentGrid.ColumnDefinitions.Count > 0 ? parentGrid.ColumnDefinitions.Count : 1);
                parentGrid.Children.Add(successNotification);

                // Handle notification events
                successNotification.NotificationClosed += (s, e) => parentGrid.Children.Remove(successNotification);

                successNotification.ViewInvoiceRequested += (s, e) =>
                {
                    // Handle view invoice request
                    parentGrid.Children.Remove(successNotification);
                    // You can add logic here to navigate to invoice details
                };

                successNotification.AddAnotherRequested += (s, e) =>
                {
                    // Handle add another invoice request
                    parentGrid.Children.Remove(successNotification);
                    ResetForm();
                };

                // Show the enhanced success message with actions
                successNotification.ShowSuccessMessage(message, showActions: true);
            }
            else
            {
                // Fallback to regular toast service
                _toastService.ShowSuccess("نجحت العملية!", message);
            }
        }



        #endregion

        #region Helper Methods

        private void ShowStatusGuidance()
        {
            string message = Status switch
            {
                InvoiceStatus.Unpaid => "💡 الفاتورة غير مسددة - لا تحتاج لمعلومات دفع",
                InvoiceStatus.PartiallyPaid => "💡 تسديد جزئي - أدخل المبلغ المدفوع",
                InvoiceStatus.Paid => "💡 تسديد كامل - سيتم تسديد المبلغ بالكامل",
                InvoiceStatus.PaidWithDiscount => "💡 تسديد وبخصم - أدخل المبلغ المدفوع والخصم",
                _ => "اختر حالة الفاتورة المناسبة"
            };

            ShowStatusIndicator(message, "#2196F3", "Information");
        }

        private void ShowPaymentStatusGuidance(string status)
        {
            string message = status switch
            {
                "دفعة جزئية" => $"💡 أدخل مبلغاً أقل من {TotalAmount:F0} د.ع للدفعة الجزئية",
                "دفعة كاملة" => $"💡 سيتم تسديد {TotalAmount:F0} د.ع بالكامل في هذه الدفعة",
                "دفعة مع خصم" => "💡 أدخل المبلغ المدفوع ومبلغ الخصم للدفعة",
                _ => "اختر نوع الدفعة المرفقة مع الفاتورة (اختياري)"
            };

            ShowPaymentStatusIndicator(message, "#2196F3", "Information");
        }

        private void ShowStatusIndicator(string message, string color, string iconKind)
        {
            // StatusIndicatorText.Text = message;
            // StatusIndicatorPanel.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E3F2FD"));
            // StatusIndicatorPanel.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color));
            // StatusIndicatorIcon.Kind = (PackIconKind)Enum.Parse(typeof(PackIconKind), iconKind);
            // StatusIndicatorIcon.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color));
            // StatusIndicatorPanel.Visibility = Visibility.Visible;
        }

        private void ShowPaymentStatusIndicator(string message, string color, string iconKind)
        {
            // PaymentStatusIndicatorText.Text = message;
            // PaymentStatusIndicatorPanel.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E3F2FD"));
            // PaymentStatusIndicatorPanel.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color));
            // PaymentStatusIndicatorIcon.Kind = (PackIconKind)Enum.Parse(typeof(PackIconKind), iconKind);
            // PaymentStatusIndicatorIcon.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color));
            // PaymentStatusIndicatorPanel.Visibility = Visibility.Visible;
        }

        private void ValidatePaymentAmount(decimal paymentAmount)
        {
            if (TotalAmount <= 0) return;

            var currentSelection = PaymentStatusComboBox.SelectedItem as ComboBoxItem;
            string currentStatus = currentSelection?.Content?.ToString() ?? "";

            switch (currentStatus)
            {
                case "تسديد جزئي":
                    if (paymentAmount >= TotalAmount)
                    {
                        ShowPaymentStatusIndicator("⚠️ المبلغ كبير للتسديد الجزئي - اختر 'تسديد كامل'", "#FF9800", "AlertCircle");
                    }
                    else if (paymentAmount > 0)
                    {
                        decimal remaining = TotalAmount - paymentAmount;
                        ShowPaymentStatusIndicator($"✅ تسديد جزئي - المتبقي: {remaining:F0} د.ع", "#FF9800", "CheckCircle");
                    }
                    break;

                case "تسديد كامل":
                    if (paymentAmount < TotalAmount)
                    {
                        ShowPaymentStatusIndicator("⚠️ المبلغ أقل من المطلوب - اختر 'تسديد جزئي'", "#FF9800", "AlertCircle");
                    }
                    else if (paymentAmount >= TotalAmount)
                    {
                        ShowPaymentStatusIndicator("✅ تم تسديد الفاتورة بالكامل", "#4CAF50", "CheckCircle");
                    }
                    break;

                case "تسديد وبخصم":
                    decimal potentialDiscount = TotalAmount - paymentAmount;
                    if (potentialDiscount > 0)
                    {
                        decimal discountPercentage = (potentialDiscount / TotalAmount) * 100;
                        ShowPaymentStatusIndicator($"✅ تسديد وبخصم {discountPercentage:F1}% - خصم: {potentialDiscount:F0} د.ع", "#9C27B0", "CheckCircle");

                        // Update discount field automatically
                        _isUpdatingAutomatically = true;
                        DiscountAmount = potentialDiscount;
                        DiscountAmountTextBox.Text = potentialDiscount.ToString("F0");
                        _isUpdatingAutomatically = false;
                    }
                    else
                    {
                        ShowPaymentStatusIndicator("⚠️ لا يمكن أن يكون المبلغ أكبر من قيمة الفاتورة", "#FF5722", "AlertCircle");
                    }
                    break;
            }
        }

        private static bool IsTextAllowed(string text)
        {
            return text.All(c => char.IsDigit(c) || c == '.');
        }

        private void ShowSuccessToast(string message)
        {
            // SuccessToastText.Text = message;
            // SuccessToast.Visibility = Visibility.Visible;

            // var storyboard = (Storyboard)FindResource("SuccessToastAnimation");
            // storyboard.Begin();
        }

        private void HideSuccessToast()
        {
            // var storyboard = (Storyboard)FindResource("HideToastAnimation");
            // storyboard.Begin();
        }

        private void HideToastAnimation_Completed(object sender, EventArgs e)
        {
            // SuccessToast.Visibility = Visibility.Collapsed;
        }

        private void ShowSuccessToastWithAutoClose(string message, bool showAddAnother = false)
        {
            ShowSuccessToast(message);

            if (showAddAnother)
            {
                // AddAnotherButton.Visibility = Visibility.Visible;
            }

            // Start auto-close timer (4 seconds)
            _autoCloseTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(4)
            };
            _autoCloseTimer.Tick += (s, e) =>
            {
                _autoCloseTimer.Stop();
                StartSlideOutAnimation();
            };
            _autoCloseTimer.Start();
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// تحسين عرض النموذج في وسط الشاشة - مطابق لنافذة الدفعة
        /// </summary>
        private void CenterFormOnScreen()
        {
            try
            {
                // تحسين حجم النموذج بناءً على حجم الشاشة - مطابق لنافذة الدفعة
                var screenWidth = SystemParameters.PrimaryScreenWidth;
                var screenHeight = SystemParameters.PrimaryScreenHeight;

                // تعيين حجم مناسب للنموذج - نفس إعدادات نافذة الدفعة
                if (screenWidth >= 1920 && screenHeight >= 1080)
                {
                    MaxWidth = 800;
                    MaxHeight = 900;
                }
                else if (screenWidth >= 1366 && screenHeight >= 768)
                {
                    MaxWidth = 700;
                    MaxHeight = 800;
                }
                else
                {
                    MaxWidth = 600;
                    MaxHeight = 700;
                }

                // ضمان الحد الأدنى - مطابق لنافذة الدفعة
                MinWidth = 550;
                MinHeight = 600;
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، استخدم القيم الافتراضية
                System.Diagnostics.Debug.WriteLine($"Error centering form: {ex.Message}");
            }
        }

        private void ResetFormForNewInvoice()
        {
            try
            {
                // Reset all form fields to default values
                InvoiceNumber = string.Empty;
                InvoiceDate = DateTime.Now;
                DueDate = null;
                Description = string.Empty;
                TotalAmount = 0;
                PaymentAmount = 0;
                Status = InvoiceStatus.Unpaid;
                SelectedSupplier = null;
                AttachmentPath = string.Empty;
                PaymentDate = DateTime.Now;
                ReceiptNumber = string.Empty;
                PaymentNotes = string.Empty;
                PaymentAttachmentPath = string.Empty;
                PaymentMethod = PaymentMethod.Cash;

                // Reset UI elements
                Dispatcher.Invoke(() =>
                {
                    if (StatusComboBox.Items.Count > 0)
                    {
                        StatusComboBox.SelectedIndex = 0; // Select "غير مسددة" by default
                    }

                    if (SupplierComboBox.Items.Count > 0)
                    {
                        SupplierComboBox.SelectedIndex = -1; // No supplier selected
                    }

                    // Clear validation errors
                    ClearValidationErrors();

                    // Focus on invoice number field
                    InvoiceNumberTextBox?.Focus();
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error resetting form: {ex.Message}");
            }
        }

        private void ClearValidationErrors()
        {
            try
            {
                // Clear any validation error messages
                // Note: Add validation error clearing logic here if needed
                System.Diagnostics.Debug.WriteLine("Validation errors cleared");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing validation errors: {ex.Message}");
            }
        }

        #endregion
    }

    public class InvoiceFormEventArgs : EventArgs
    {
        public bool Success { get; set; }
        public Invoice? Invoice { get; set; }
    }
}
