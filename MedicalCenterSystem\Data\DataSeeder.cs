using MedicalCenterSystem.Models;

namespace MedicalCenterSystem.Data
{
    public static class DataSeeder
    {
        public static async Task SeedAsync(ApplicationDbContext context)
        {
            // Check if data already exists
            if (context.Doctors.Any())
                return;

            // Seed Doctors
            var doctors = new List<Doctor>
            {
                new Doctor { FullName = "د. أحمد محمد", Specialty = "طب عام", IsActive = true },
                new Doctor { FullName = "د. فاطمة علي", Specialty = "أطفال", IsActive = true },
                new Doctor { FullName = "د. محمد حسن", Specialty = "باطنية", IsActive = true },
                new Doctor { FullName = "د. سارة أحمد", Specialty = "نساء وولادة", IsActive = true },
                new Doctor { FullName = "د. عمر خالد", Specialty = "جراحة عامة", IsActive = true }
            };

            context.Doctors.AddRange(doctors);
            await context.SaveChangesAsync();

            // Seed Medical Services
            var medicalServices = new List<MedicalService>
            {
                new MedicalService { ServiceName = "كشفية", ServiceType = "Direct", IsCenterService = true, DefaultPrice = 50000 },
                new MedicalService { ServiceName = "فحص شامل", ServiceType = "Direct", IsCenterService = true, DefaultPrice = 100000 },
                new MedicalService { ServiceName = "تحليل دم", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 75000 },
                new MedicalService { ServiceName = "أشعة سينية", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 125000 },
                new MedicalService { ServiceName = "سونار", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 150000 },
                new MedicalService { ServiceName = "رنين مغناطيسي", ServiceType = "Referral", IsCenterService = false, DefaultPrice = 500000 },
                new MedicalService { ServiceName = "تخطيط قلب", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 80000 },
                new MedicalService { ServiceName = "تحليل بول", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 25000 }
            };

            context.MedicalServices.AddRange(medicalServices);
            await context.SaveChangesAsync();

            // Seed Doctor Services (linking doctors with services and percentages)
            var doctorServices = new List<DoctorService>
            {
                // د. أحمد محمد - طب عام
                new DoctorService { DoctorId = 1, MedicalServiceId = 1, LinkType = "Direct", HasPercentage = true, Percentage = 60 },
                new DoctorService { DoctorId = 1, MedicalServiceId = 3, LinkType = "Referral", HasPercentage = true, Percentage = 10 },
                new DoctorService { DoctorId = 1, MedicalServiceId = 4, LinkType = "Referral", HasPercentage = true, Percentage = 15 },
                
                // د. فاطمة علي - أطفال
                new DoctorService { DoctorId = 2, MedicalServiceId = 1, LinkType = "Direct", HasPercentage = true, Percentage = 65 },
                new DoctorService { DoctorId = 2, MedicalServiceId = 3, LinkType = "Referral", HasPercentage = true, Percentage = 12 },
                new DoctorService { DoctorId = 2, MedicalServiceId = 5, LinkType = "Referral", HasPercentage = true, Percentage = 20 },
                
                // د. محمد حسن - باطنية
                new DoctorService { DoctorId = 3, MedicalServiceId = 1, LinkType = "Direct", HasPercentage = true, Percentage = 70 },
                new DoctorService { DoctorId = 3, MedicalServiceId = 2, LinkType = "Direct", HasPercentage = true, Percentage = 50 },
                new DoctorService { DoctorId = 3, MedicalServiceId = 7, LinkType = "Referral", HasPercentage = true, Percentage = 25 },
                
                // د. سارة أحمد - نساء وولادة
                new DoctorService { DoctorId = 4, MedicalServiceId = 1, LinkType = "Direct", HasPercentage = true, Percentage = 65 },
                new DoctorService { DoctorId = 4, MedicalServiceId = 5, LinkType = "Referral", HasPercentage = true, Percentage = 30 },
                new DoctorService { DoctorId = 4, MedicalServiceId = 6, LinkType = "Referral", HasPercentage = true, Percentage = 20 },
                
                // د. عمر خالد - جراحة عامة
                new DoctorService { DoctorId = 5, MedicalServiceId = 1, LinkType = "Direct", HasPercentage = true, Percentage = 75 },
                new DoctorService { DoctorId = 5, MedicalServiceId = 2, LinkType = "Direct", HasPercentage = true, Percentage = 60 },
                new DoctorService { DoctorId = 5, MedicalServiceId = 4, LinkType = "Referral", HasPercentage = true, Percentage = 20 }
            };

            context.DoctorServices.AddRange(doctorServices);
            await context.SaveChangesAsync();

            // Seed some sample patient visits
            var patientVisits = new List<PatientVisit>
            {
                new PatientVisit 
                { 
                    VisitDate = DateTime.Today, 
                    DoctorId = 1, 
                    VisitNumber = 1, 
                    PatientName = "علي محمد أحمد", 
                    Age = 35, 
                    Province = "بغداد", 
                    BookingStaff = "سارة", 
                    VisitCountLabel = "أولى", 
                    PhoneNumber = "07901234567",
                    Diagnosis = "فحص دوري"
                },
                new PatientVisit 
                { 
                    VisitDate = DateTime.Today, 
                    DoctorId = 2, 
                    VisitNumber = 1, 
                    PatientName = "فاطمة حسن علي", 
                    Age = 8, 
                    Province = "البصرة", 
                    BookingStaff = "أحمد", 
                    VisitCountLabel = "ثانية", 
                    PhoneNumber = "07801234567",
                    Diagnosis = "متابعة نمو"
                },
                new PatientVisit 
                { 
                    VisitDate = DateTime.Today, 
                    DoctorId = 1, 
                    VisitNumber = 2, 
                    PatientName = "محمد عبدالله", 
                    Age = 45, 
                    Province = "أربيل", 
                    BookingStaff = "سارة", 
                    VisitCountLabel = "أولى", 
                    PhoneNumber = "***********",
                    Diagnosis = "ألم في المعدة"
                }
            };

            context.PatientVisits.AddRange(patientVisits);
            await context.SaveChangesAsync();

            // Seed User Accounts
            var userAccounts = new List<UserAccount>
            {
                new UserAccount 
                { 
                    Username = "admin", 
                    HashedPassword = "admin123", // In real app, this should be hashed
                    Role = "Admin", 
                    IsActive = true 
                },
                new UserAccount 
                { 
                    Username = "reception", 
                    HashedPassword = "reception123", 
                    Role = "Reception", 
                    IsActive = true 
                },
                new UserAccount 
                { 
                    Username = "cashier", 
                    HashedPassword = "cashier123", 
                    Role = "Cashier", 
                    IsActive = true 
                }
            };

            context.UserAccounts.AddRange(userAccounts);
            await context.SaveChangesAsync();
        }
    }
}
