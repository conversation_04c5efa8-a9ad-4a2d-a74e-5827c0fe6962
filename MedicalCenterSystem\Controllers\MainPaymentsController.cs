using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MedicalCenterSystem.Data;
using MedicalCenterSystem.Models;

namespace MedicalCenterSystem.Controllers
{
    public class MainPaymentsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public MainPaymentsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: MainPayments
        public async Task<IActionResult> Index()
        {
            var mainPayments = await _context.MainPayments
                .Include(m => m.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .OrderByDescending(m => m.PaymentDate)
                .ToListAsync();
            return View(mainPayments);
        }

        // GET: MainPayments/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var mainPayment = await _context.MainPayments
                .Include(m => m.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .FirstOrDefaultAsync(m => m.MainPaymentId == id);
            if (mainPayment == null)
            {
                return NotFound();
            }

            return View(mainPayment);
        }

        // GET: MainPayments/Create
        public IActionResult Create(int? patientVisitId)
        {
            if (patientVisitId.HasValue)
            {
                var patientVisit = _context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .FirstOrDefault(pv => pv.PatientVisitId == patientVisitId.Value);
                
                if (patientVisit != null)
                {
                    ViewBag.PatientVisit = patientVisit;
                    var model = new MainPayment
                    {
                        PatientVisitId = patientVisitId.Value,
                        PaymentDate = DateTime.Now
                    };
                    return View(model);
                }
            }

            ViewData["PatientVisitId"] = new SelectList(
                _context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .Where(pv => pv.MainPayment == null)
                    .Select(pv => new { 
                        pv.PatientVisitId, 
                        DisplayText = $"{pv.PatientName} - {pv.Doctor.FullName} - {pv.VisitDate:yyyy-MM-dd} - رقم {pv.VisitNumber}" 
                    }),
                "PatientVisitId", "DisplayText");
            
            return View(new MainPayment { PaymentDate = DateTime.Now });
        }

        // POST: MainPayments/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("MainPaymentId,PatientVisitId,ConsultationFee,ExamFee,DoctorShare,CenterShare,CashierName,Notes,PaymentDate")] MainPayment mainPayment)
        {
            if (ModelState.IsValid)
            {
                _context.Add(mainPayment);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }

            ViewData["PatientVisitId"] = new SelectList(
                _context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .Where(pv => pv.MainPayment == null)
                    .Select(pv => new { 
                        pv.PatientVisitId, 
                        DisplayText = $"{pv.PatientName} - {pv.Doctor.FullName} - {pv.VisitDate:yyyy-MM-dd} - رقم {pv.VisitNumber}" 
                    }),
                "PatientVisitId", "DisplayText", mainPayment.PatientVisitId);
            
            return View(mainPayment);
        }

        // GET: MainPayments/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var mainPayment = await _context.MainPayments.FindAsync(id);
            if (mainPayment == null)
            {
                return NotFound();
            }

            ViewData["PatientVisitId"] = new SelectList(
                _context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .Select(pv => new { 
                        pv.PatientVisitId, 
                        DisplayText = $"{pv.PatientName} - {pv.Doctor.FullName} - {pv.VisitDate:yyyy-MM-dd} - رقم {pv.VisitNumber}" 
                    }),
                "PatientVisitId", "DisplayText", mainPayment.PatientVisitId);
            
            return View(mainPayment);
        }

        // POST: MainPayments/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("MainPaymentId,PatientVisitId,ConsultationFee,ExamFee,DoctorShare,CenterShare,CashierName,Notes,PaymentDate")] MainPayment mainPayment)
        {
            if (id != mainPayment.MainPaymentId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(mainPayment);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!MainPaymentExists(mainPayment.MainPaymentId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewData["PatientVisitId"] = new SelectList(
                _context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .Select(pv => new { 
                        pv.PatientVisitId, 
                        DisplayText = $"{pv.PatientName} - {pv.Doctor.FullName} - {pv.VisitDate:yyyy-MM-dd} - رقم {pv.VisitNumber}" 
                    }),
                "PatientVisitId", "DisplayText", mainPayment.PatientVisitId);
            
            return View(mainPayment);
        }

        // GET: MainPayments/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var mainPayment = await _context.MainPayments
                .Include(m => m.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .FirstOrDefaultAsync(m => m.MainPaymentId == id);
            if (mainPayment == null)
            {
                return NotFound();
            }

            return View(mainPayment);
        }

        // POST: MainPayments/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var mainPayment = await _context.MainPayments.FindAsync(id);
            if (mainPayment != null)
            {
                _context.MainPayments.Remove(mainPayment);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool MainPaymentExists(int id)
        {
            return _context.MainPayments.Any(e => e.MainPaymentId == id);
        }
    }
}
