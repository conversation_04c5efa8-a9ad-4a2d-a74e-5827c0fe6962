using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Controls.Dashboard
{
    public partial class StatisticsCardsControl : UserControl
    {
        public event EventHandler<string>? CardClicked;
        
        private readonly INavigationService? _navigationService;

        public StatisticsCardsControl()
        {
            InitializeComponent();
        }

        public StatisticsCardsControl(INavigationService navigationService) : this()
        {
            _navigationService = navigationService;
        }

        public async Task UpdateStatistics(DashboardStatistics statistics)
        {
            if (statistics == null) return;

            try
            {
                // Update with smooth animation
                await AnimateStatisticUpdate(TotalInvoicesText, statistics.TotalInvoices.ToString("N0"));
                await AnimateStatisticUpdate(TotalAmountText, $"{statistics.TotalAmount:N0} د.ع");
                await AnimateStatisticUpdate(PaidAmountText, $"{statistics.PaidAmount:N0} د.ع");
                await AnimateStatisticUpdate(OutstandingAmountText, $"{statistics.OutstandingAmount:N0} د.ع");
                await AnimateStatisticUpdate(OverdueInvoicesText, statistics.OverdueInvoices.ToString("N0"));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating statistics: {ex.Message}");
            }
        }

        private async Task AnimateStatisticUpdate(TextBlock textBlock, string newValue)
        {
            if (textBlock.Text == newValue) return;

            try
            {
                // Simple fade animation
                var fadeOut = new DoubleAnimation(1, 0.3, TimeSpan.FromMilliseconds(150));
                textBlock.BeginAnimation(OpacityProperty, fadeOut);

                await Task.Delay(150);

                textBlock.Text = newValue;

                var fadeIn = new DoubleAnimation(0.3, 1, TimeSpan.FromMilliseconds(150));
                textBlock.BeginAnimation(OpacityProperty, fadeIn);
            }
            catch (Exception ex)
            {
                // Fallback to direct update
                textBlock.Text = newValue;
                System.Diagnostics.Debug.WriteLine($"Animation error: {ex.Message}");
            }
        }

        private void TotalInvoicesCard_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                CardClicked?.Invoke(this, "TotalInvoices");
                _navigationService?.NavigateTo(typeof(Pages.InvoicesPage), "جميع الفواتير");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Navigation error: {ex.Message}");
            }
        }

        // Quick update method without animation for better performance
        public void UpdateStatisticsQuick(DashboardStatistics statistics)
        {
            if (statistics == null) return;

            try
            {
                TotalInvoicesText.Text = statistics.TotalInvoices.ToString("N0");
                TotalAmountText.Text = $"{statistics.TotalAmount:N0} د.ع";
                PaidAmountText.Text = $"{statistics.PaidAmount:N0} د.ع";
                OutstandingAmountText.Text = $"{statistics.OutstandingAmount:N0} د.ع";
                OverdueInvoicesText.Text = statistics.OverdueInvoices.ToString("N0");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in quick update: {ex.Message}");
            }
        }
    }
}
