using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Controls;

namespace HR_InvoiceArchiver.Pages
{
    public partial class OffersPage : UserControl
    {
        private readonly OfferService _offerService;
        private ObservableCollection<Offer> _offers = new();
        private ObservableCollection<Offer> _filteredOffers = new();

        public OffersPage(OfferService offerService)
        {
            InitializeComponent();
            _offerService = offerService;
            OfferFormControl offerForm = new OfferFormControl(_offerService);
            // إذا كان هناك عنصر باسم OfferFormControl في XAML، اربطه هنا:
            this.OfferFormControlHost.Content = offerForm;
            LoadOffers();
            SetupFilters();
        }

        private void LoadOffers()
        {
            var offers = _offerService.GetAllOffers().ToList();
            _offers = new ObservableCollection<Offer>(offers);
            _filteredOffers = new ObservableCollection<Offer>(offers);
            OffersDataGrid.ItemsSource = _filteredOffers;
        }

        private void SetupFilters()
        {
            // تعبئة الفلاتر بالقيم الفريدة
            FilterScientificNameComboBox.ItemsSource = _offers.Select(o => o.ScientificName).Distinct().ToList();
            FilterOfficeComboBox.ItemsSource = _offers.Select(o => o.ScientificOffice).Distinct().ToList();
            FilterRepComboBox.ItemsSource = _offers.Select(o => o.RepresentativeName).Distinct().ToList();
        }

        private void SearchAndFilter()
        {
            var search = SearchTextBox.Text?.Trim() ?? "";
            var scientificName = FilterScientificNameComboBox.SelectedItem as string;
            var office = FilterOfficeComboBox.SelectedItem as string;
            var rep = FilterRepComboBox.SelectedItem as string;
            var sort = (SortComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();

            var filtered = _offers.Where(o =>
                (string.IsNullOrEmpty(search) || o.ScientificOffice.Contains(search) || o.RepresentativeName.Contains(search) || o.ScientificName.Contains(search) || o.TradeName.Contains(search)) &&
                (string.IsNullOrEmpty(scientificName) || o.ScientificName == scientificName) &&
                (string.IsNullOrEmpty(office) || o.ScientificOffice == office) &&
                (string.IsNullOrEmpty(rep) || o.RepresentativeName == rep)
            );

            if (sort == "السعر الأقل")
                filtered = filtered.OrderBy(o => o.Price);
            else if (sort == "السعر الأعلى")
                filtered = filtered.OrderByDescending(o => o.Price);
            else if (sort == "أفضل عرض")
                filtered = GetBestOffers(filtered.ToList());

            _filteredOffers = new ObservableCollection<Offer>(filtered);
            OffersDataGrid.ItemsSource = _filteredOffers;
        }

        private List<Offer> GetBestOffers(List<Offer> offers)
        {
            // أفضل عرض لكل مادة علمية: أقل سعر + أعلى بونص
            return offers
                .GroupBy(o => o.ScientificName)
                .Select(g => g.OrderBy(o => o.Price).ThenByDescending(o => o.BonusOrDiscount).First())
                .ToList();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e) => SearchAndFilter();
        private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e) => SearchAndFilter();
        private void SortComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e) => SearchAndFilter();
    }
} 