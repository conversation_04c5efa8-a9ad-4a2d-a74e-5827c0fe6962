using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Services;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Linq;

namespace HR_InvoiceArchiver.Tests.Services
{
    public class LoggingServiceTests : IDisposable
    {
        private readonly LoggingService _loggingService;
        private readonly string _testLogDirectory;

        public LoggingServiceTests()
        {
            _testLogDirectory = Path.Combine(Path.GetTempPath(), "HR_InvoiceArchiver_Tests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testLogDirectory);
            _loggingService = new LoggingService(_testLogDirectory);
        }

        [Fact]
        public async Task LogAsync_ShouldCreateLogEntry()
        {
            // Arrange
            var message = "Test log message";
            var level = LogLevel.Information;
            var category = LogCategory.General;

            // Act
            await _loggingService.LogAsync(level, category, message);

            // Assert
            // انتظار قليل للتأكد من كتابة السجل
            await Task.Delay(100);
            
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogInformationAsync_ShouldLogWithCorrectLevel()
        {
            // Arrange
            var message = "Information message";

            // Act
            await _loggingService.LogInformationAsync(message);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogErrorAsync_ShouldLogWithException()
        {
            // Arrange
            var message = "Error message";
            var exception = new InvalidOperationException("Test exception");

            // Act
            await _loggingService.LogErrorAsync(message, exception);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogPerformanceAsync_ShouldLogPerformanceData()
        {
            // Arrange
            var operation = "TestOperation";
            var duration = TimeSpan.FromMilliseconds(500);

            // Act
            await _loggingService.LogPerformanceAsync(operation, duration);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogSecurityEventAsync_ShouldLogSecurityEvent()
        {
            // Arrange
            var eventType = "LoginAttempt";
            var description = "Failed login attempt";
            var userId = "user123";

            // Act
            await _loggingService.LogSecurityEventAsync(eventType, description, userId);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogDatabaseOperationAsync_ShouldLogDatabaseOperation()
        {
            // Arrange
            var operation = "INSERT";
            var tableName = "Invoices";
            var success = true;
            var duration = TimeSpan.FromMilliseconds(100);

            // Act
            await _loggingService.LogDatabaseOperationAsync(operation, tableName, success, duration);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task SearchLogsAsync_ShouldReturnFilteredResults()
        {
            // Arrange
            await _loggingService.LogInformationAsync("Test message 1");
            await _loggingService.LogErrorAsync("Test error message");
            await _loggingService.LogWarningAsync("Test warning message");
            await Task.Delay(200); // انتظار كتابة السجلات

            var criteria = new LogSearchCriteria
            {
                MinLevel = LogLevel.Warning,
                PageSize = 10
            };

            // Act
            var result = await _loggingService.SearchLogsAsync(criteria);

            // Assert
            result.Should().NotBeNull();
            result.Entries.Should().NotBeEmpty();
        }

        [Fact]
        public async Task GetLogStatisticsAsync_ShouldReturnStatistics()
        {
            // Arrange
            await _loggingService.LogInformationAsync("Info message");
            await _loggingService.LogErrorAsync("Error message");
            await _loggingService.LogWarningAsync("Warning message");
            await Task.Delay(200);

            // Act
            var statistics = await _loggingService.GetLogStatisticsAsync();

            // Assert
            statistics.Should().NotBeNull();
            statistics.Should().NotBeEmpty();
        }

        [Fact]
        public async Task GetRecentLogsAsync_ShouldReturnRecentLogs()
        {
            // Arrange
            await _loggingService.LogInformationAsync("Recent message 1");
            await _loggingService.LogInformationAsync("Recent message 2");
            await Task.Delay(200);

            // Act
            var recentLogs = await _loggingService.GetRecentLogsAsync(10);

            // Assert
            recentLogs.Should().NotBeNull();
        }

        [Fact]
        public void BeginScope_ShouldReturnValidScope()
        {
            // Arrange
            var operationName = "TestOperation";

            // Act
            using var scope = _loggingService.BeginScope(operationName);

            // Assert
            scope.Should().NotBeNull();
            scope.Should().BeAssignableTo<IDisposable>();
        }

        [Fact]
        public async Task ExportLogsAsync_ShouldCreateExportFile()
        {
            // Arrange
            await _loggingService.LogInformationAsync("Export test message");
            await Task.Delay(200);

            var criteria = new LogSearchCriteria
            {
                PageSize = 100
            };

            // Act
            var exportPath = await _loggingService.ExportLogsAsync(criteria, "csv");

            // Assert
            exportPath.Should().NotBeNullOrEmpty();
            File.Exists(exportPath).Should().BeTrue();
        }

        [Fact]
        public async Task CleanupOldLogsAsync_ShouldRemoveOldLogFiles()
        {
            // Arrange
            var oldLogFile = Path.Combine(_testLogDirectory, "old-log.log");
            await File.WriteAllTextAsync(oldLogFile, "Old log content");
            
            // تعديل تاريخ الإنشاء ليكون قديماً
            File.SetCreationTime(oldLogFile, DateTime.Now.AddDays(-10));

            // Act
            await _loggingService.CleanupOldLogsAsync(TimeSpan.FromDays(5));

            // Assert
            File.Exists(oldLogFile).Should().BeFalse();
        }

        [Theory]
        [InlineData(LogLevel.Trace)]
        [InlineData(LogLevel.Debug)]
        [InlineData(LogLevel.Information)]
        [InlineData(LogLevel.Warning)]
        [InlineData(LogLevel.Error)]
        [InlineData(LogLevel.Critical)]
        public async Task LogAsync_WithDifferentLevels_ShouldLogCorrectly(LogLevel level)
        {
            // Arrange
            var message = $"Test message for level {level}";

            // Act
            await _loggingService.LogAsync(level, LogCategory.General, message);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Theory]
        [InlineData(LogCategory.General)]
        [InlineData(LogCategory.Database)]
        [InlineData(LogCategory.UI)]
        [InlineData(LogCategory.Business)]
        [InlineData(LogCategory.Security)]
        [InlineData(LogCategory.Performance)]
        [InlineData(LogCategory.CloudSync)]
        public async Task LogAsync_WithDifferentCategories_ShouldLogCorrectly(LogCategory category)
        {
            // Arrange
            var message = $"Test message for category {category}";

            // Act
            await _loggingService.LogAsync(LogLevel.Information, category, message);

            // Assert
            await Task.Delay(100);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        [Fact]
        public async Task LogScope_ShouldLogStartAndEnd()
        {
            // Arrange
            var operationName = "ScopeTestOperation";

            // Act
            using (var scope = _loggingService.BeginScope(operationName))
            {
                await Task.Delay(50); // محاكاة عملية
            }

            // Assert
            await Task.Delay(200);
            var logFiles = Directory.GetFiles(_testLogDirectory, "*.log");
            logFiles.Should().NotBeEmpty();
        }

        public void Dispose()
        {
            _loggingService?.Dispose();
            
            // تنظيف مجلد الاختبار
            if (Directory.Exists(_testLogDirectory))
            {
                try
                {
                    Directory.Delete(_testLogDirectory, true);
                }
                catch
                {
                    // تجاهل أخطاء التنظيف
                }
            }
        }
    }
}
