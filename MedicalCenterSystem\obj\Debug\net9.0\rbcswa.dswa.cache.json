{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["wQdrHXgrorHudDHLF6DdyApA281hzthtODqFzleXkUM=", "wMawqWFWYibdV+lBRifMcmovxjtq5qx5C75C7bMywi8=", "3zgQMa/84lu0GtNRqhhzDyFMY+RuebKPRVlkUT4IZho=", "hMWmWGG8yeNiIWiTL3s2OBInJPMQpdPJizGQJrBIO2o=", "Ewc/U/ujEKWqZ4zrbeqJhFH54A1XzGwIgWFv5Cd7WIw=", "S2ES9c/l1kEFVhYZGNmYZnTiZ2ps+H2dmBrXWJqk+v8=", "mFzNjkAxqPx2R/tigMoyfGAsYC/41lchbdSL7lqdXg0=", "Iqypy8Uf9qM2oQOGz01Ogpx9t1TjMymyFdtEgDMY//w=", "8z9lL6h+fqT1RCxX41tljV4mpi6IKxl6R5J8XlutG5I=", "i4W9Ee1aAbes5lXsbfyxsQebR1ApOrkzOol9ZwpsDME=", "wogKjBMQvNUDiN6v3k5uf7PqS0T/O3LPua+bmskK2Dg=", "yJ0hbRwCoBf+IskjLLe2XL2As76KQtDmNOdcuimHUvg=", "aXeOmwJC7C+0Aupyrm4KicVbGSUjPelA26Rq9VDOC/I=", "YIHb/JAcHq5HeRlwjfcVv7bwwN3TXjoSfgfc6mqGkzY=", "yV4DRqHp6b5P4SAr+RLzeGYB63dEgt5X2FzcvwOMbDc=", "HtJZTIQgjNK4PeyOtZsRnKh/JyOycjEz28b0q5J6cMg=", "CrGBklBo94v6E4Sy6HP5Q5p8+3Kyh314nwQn3shJQ38=", "ueD1mY4YjTWKBkJ63Bm1Xg6jJGHVboVdu23VNv6jCCc=", "tl5hhufK+xL9FM5WmAohciESb86wjhTqFZ/MsNfUZOs=", "VVoI6NLOLWn8fyKTpQn8b6Ggr9GK93prw8O1LA7RcU8=", "E4FgREqGq43W4dAMHIK3S0Dg0R5LOVZItJQXx1rGtfo=", "k+Pl65dKk1oJg0ousLpkETzqKv7S+rBepeiIBIqC8/I=", "btGrm0pjPn9XirhtpL9Ku9nnBEcI6wPlWesMnMt6Z08=", "nnfcCG5rQCtQvY3Qn0c9lqu0bJuBDx3DSg4zEochYck=", "dOGBbiuih4xIJbHmSDPGaG35dR/0IXH+E9waUTXd3oY=", "95BYaOpYWBtOEOE6k604lloH0bMS8G6DHUUUW+Iu1Ac=", "WAZP5njCNYOB4XJlbZcORi2NxIYPlJByzOKREHn8sns=", "4yV/BvQZIQgsilsq5cuIHCm70eWtlGPlQ48/eXwxJas=", "FA3kQZk4AEPSg3oa9Fs/BOCnZ7FQA2wICwUg/4N4300=", "d61LVfkTBrS4rIjXXj1G2A5NVd9IFIhpdJ0CeZ26MFs=", "zPPd4xOYNDLL4ywTOEoHV9TUGZO94v6OCpTiSVdZMr0=", "rq4vKdiLVWk2/zTb0VN7Kmntls+YUIoHqkO2EQ3xwOY=", "qYx8QEagTv3zLZoltvITrcfAKJ2iGPDxYjszt5BLdUk=", "zowsTUbl/D4H+12gjyYXDSI+GXQuPx52pnTjG5cQ66Q=", "MFqoMvEAno/Qx2qdVGmqgcHAMIemNP2eLGUTuAibnkU=", "RQUbF66XP1CMdqtWSw2dya+wVc3Mr/lQ2KQ+FBMFz7I=", "YB4Wh0zckesBQ9cAnGubIx/SqeSYF09d3V25GiWXt44=", "l6tmJX4mOxcqyhQARM7s0zeL/RtHw6n592vYaSljA6U=", "W+f8HxPg0vXjrXtym2022yI+0RlW6M3AHA7FDpILNEI=", "ey38+CI04rSAuS/Y++5YqBQDfOCUPqWIUq+X0yIHA7A=", "mOYFw8UkSD72vbVKb/r/LuYm48+oUYV2wz39GM9od/k=", "moQ7y8Qsd8fi7QCq511Ke5/ojC2kc3qb4jMslHmwPAA=", "ndapbZRx6DGExrMIZjUn5NYD3Ae8Tj64ppZgM2CVesI=", "QCI3ervEuo2zTlJ88KhGA+k3NBNdWCWJbd26EZ5ppDw=", "+UnIdp6uZmMybuo4Qsz2f4cHSA6HBXO9nxaFJtnqEv0=", "W4y4eYKrFDNNUAsathJA57EIxWnJEMI1GOZ2TYdwUFE=", "NXWQ6DTQvSkmLgnELZ3QpYlDIpwmcZgdYHZo2uFawEw=", "4XdRe+9/ZoAMqAdkmHjVHyPvNroLJEnEzw5mItnuUOU=", "cnAI5NBNykTBwAFmj3gow3wi/xBiqJ9pgcSRg5FtT3k=", "7lLwUN6AVSOYTnMgIvTMiHv2KJqfX9DcEIb/r8V4YbI=", "4auONULAGgLEZ3XKpKuPD2vNIBPGWGGhYM+IkK6D2DI=", "x2DRF9PM29cPIJz/Vh93WET9ZkTxuTFL1DeV4P30UTc=", "MITxjJ0hXASiFheYoOE0nvNJ5NxYxZ+3A+x+nkSVADg=", "wnf22mL5ZZ0n++iH9lb4RWnhbwdV0wuv444DHKQYRxw=", "UXaYsa3ADrTK1OplC/l8kGyK36vsS+b6VF1Vv6ZgrHU=", "N2q7TGfoGJyDrWQRUOwLnJbhcD2p7aBN5qHJK9PAaAE=", "9YQ43+inLYPMIZci1oiFKmmojf/llYiWA8gNOhPwXtQ=", "Vg9oVfahSFenaZngI7eyDrzNkWSWn8BjW7ldMCLHyX0=", "ISmWiLqNOvBu7fXn4k7NFKLo9bP5byH4U5ZMpjMFn+8=", "rGgnnz4LmaFyt0jr6dFLxT/iL0x5dO3F6JlI7y973+Y=", "0DEbEsdgahmyQN8JFc2WeAMFm3VoQCKpYuia4iRG2nM=", "dAdFnTlOPJ6hHH2Zl7pNVOVa7iKxJ7tMXHYOKfdU7BA=", "T6Ac19IN5R/DC/3bKsoIsJpGaPF/mnxDRVyuVBDqTBI=", "YzLbMs0/85CuMCSBUXQWY4xb7Kp6MLmsrKBoVAGlflE="], "CachedAssets": {"wQdrHXgrorHudDHLF6DdyApA281hzthtODqFzleXkUM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\3jl4y146nl-b9sayid5wm.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "css/site#[.{fingerprint=b9sayid5wm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-20T18:46:05.7187367+00:00"}, "wMawqWFWYibdV+lBRifMcmovxjtq5qx5C75C7bMywi8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\86s8omr731-61n19gt1b8.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-20T18:46:05.7278135+00:00"}, "3zgQMa/84lu0GtNRqhhzDyFMY+RuebKPRVlkUT4IZho=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\9et5ilxzp0-xtxxf3hu2r.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-20T18:46:05.7177373+00:00"}, "hMWmWGG8yeNiIWiTL3s2OBInJPMQpdPJizGQJrBIO2o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\iwiv8x0hzp-bqjiyaj88i.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-20T18:46:05.7318144+00:00"}, "Ewc/U/ujEKWqZ4zrbeqJhFH54A1XzGwIgWFv5Cd7WIw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\8ywcw6zuj3-c2jlpeoesf.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-20T18:46:05.7207382+00:00"}, "S2ES9c/l1kEFVhYZGNmYZnTiZ2ps+H2dmBrXWJqk+v8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\s0cznl8snj-erw9l3u2r3.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-20T18:46:05.7328145+00:00"}, "mFzNjkAxqPx2R/tigMoyfGAsYC/41lchbdSL7lqdXg0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\p9zqzvqeqr-aexeepp0ev.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-20T18:46:05.7218087+00:00"}, "Iqypy8Uf9qM2oQOGz01Ogpx9t1TjMymyFdtEgDMY//w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\6calh2aeyi-d7shbmvgxk.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-20T18:46:05.7338139+00:00"}, "8z9lL6h+fqT1RCxX41tljV4mpi6IKxl6R5J8XlutG5I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\481qq90aue-ausgxo2sd3.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-20T18:46:05.7187367+00:00"}, "i4W9Ee1aAbes5lXsbfyxsQebR1ApOrkzOol9ZwpsDME=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\dx1yz1mp0d-k8d9w2qqmf.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-20T18:46:05.7328145+00:00"}, "wogKjBMQvNUDiN6v3k5uf7PqS0T/O3LPua+bmskK2Dg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\6t6bqjximv-cosvhxvwiu.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-20T18:46:05.7177373+00:00"}, "yJ0hbRwCoBf+IskjLLe2XL2As76KQtDmNOdcuimHUvg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\8sz6nr2w18-ub07r2b239.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-20T18:46:05.7318144+00:00"}, "aXeOmwJC7C+0Aupyrm4KicVbGSUjPelA26Rq9VDOC/I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\9kn869zrgd-fvhpjtyr6v.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-20T18:46:05.723814+00:00"}, "YIHb/JAcHq5HeRlwjfcVv7bwwN3TXjoSfgfc6mqGkzY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\6858xemjey-b7pk76d08c.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-20T18:46:05.7348138+00:00"}, "yV4DRqHp6b5P4SAr+RLzeGYB63dEgt5X2FzcvwOMbDc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\eatsux4qqo-fsbi9cje9m.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-20T18:46:05.7177373+00:00"}, "HtJZTIQgjNK4PeyOtZsRnKh/JyOycjEz28b0q5J6cMg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\g14ect6vnv-rzd6atqjts.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-20T18:46:05.7318144+00:00"}, "CrGBklBo94v6E4Sy6HP5Q5p8+3Kyh314nwQn3shJQ38=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\4pkjpahwn6-ee0r1s7dh0.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-20T18:46:05.7218087+00:00"}, "ueD1mY4YjTWKBkJ63Bm1Xg6jJGHVboVdu23VNv6jCCc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\swvqvbpieu-dxx9fxp4il.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-20T18:46:05.7348138+00:00"}, "tl5hhufK+xL9FM5WmAohciESb86wjhTqFZ/MsNfUZOs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\9sk82wpiwg-jd9uben2k1.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-20T18:46:05.7207382+00:00"}, "VVoI6NLOLWn8fyKTpQn8b6Ggr9GK93prw8O1LA7RcU8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\nv7ppclwai-khv3u5hwcm.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-20T18:46:05.7348138+00:00"}, "E4FgREqGq43W4dAMHIK3S0Dg0R5LOVZItJQXx1rGtfo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\b4m5kz4xhw-r4e9w2rdcm.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-20T18:46:05.7207382+00:00"}, "k+Pl65dKk1oJg0ousLpkETzqKv7S+rBepeiIBIqC8/I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\thfeshc0b4-lcd1t2u6c8.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-20T18:46:05.7348138+00:00"}, "btGrm0pjPn9XirhtpL9Ku9nnBEcI6wPlWesMnMt6Z08=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\17ozu19me7-c2oey78nd0.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-20T18:46:05.7187367+00:00"}, "nnfcCG5rQCtQvY3Qn0c9lqu0bJuBDx3DSg4zEochYck=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\twi6bp6xk0-tdbxkamptv.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-20T18:46:05.7338139+00:00"}, "dOGBbiuih4xIJbHmSDPGaG35dR/0IXH+E9waUTXd3oY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\goj4qom71l-j5mq2jizvt.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-20T18:46:05.7268134+00:00"}, "95BYaOpYWBtOEOE6k604lloH0bMS8G6DHUUUW+Iu1Ac=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\1kpzbveq29-06098lyss8.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-20T18:46:05.7358141+00:00"}, "WAZP5njCNYOB4XJlbZcORi2NxIYPlJByzOKREHn8sns=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\9re4wy64px-nvvlpmu67g.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-20T18:46:05.723814+00:00"}, "4yV/BvQZIQgsilsq5cuIHCm70eWtlGPlQ48/eXwxJas=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\rrhlthn37z-s35ty4nyc5.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-20T18:46:05.7368124+00:00"}, "FA3kQZk4AEPSg3oa9Fs/BOCnZ7FQA2wICwUg/4N4300=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\w02pow6gp4-pj5nd1wqec.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-20T18:46:05.7308136+00:00"}, "d61LVfkTBrS4rIjXXj1G2A5NVd9IFIhpdJ0CeZ26MFs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\wws63fiels-46ein0sx1k.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-20T18:46:05.738812+00:00"}, "zPPd4xOYNDLL4ywTOEoHV9TUGZO94v6OCpTiSVdZMr0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\ni7645a7s0-v0zj4ognzu.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-20T18:46:05.7278135+00:00"}, "rq4vKdiLVWk2/zTb0VN7Kmntls+YUIoHqkO2EQ3xwOY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\y15gv69enr-37tfw0ft22.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-20T18:46:05.7398131+00:00"}, "qYx8QEagTv3zLZoltvITrcfAKJ2iGPDxYjszt5BLdUk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\14yfv0o313-hrwsygsryq.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-20T18:46:05.723814+00:00"}, "zowsTUbl/D4H+12gjyYXDSI+GXQuPx52pnTjG5cQ66Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\pzg1fp2i2c-pk9g2wxc8p.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-20T18:46:05.7368124+00:00"}, "MFqoMvEAno/Qx2qdVGmqgcHAMIemNP2eLGUTuAibnkU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\q03my4lew8-ft3s53vfgj.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-20T18:46:05.7258195+00:00"}, "RQUbF66XP1CMdqtWSw2dya+wVc3Mr/lQ2KQ+FBMFz7I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\norte3ma0m-6cfz1n2cew.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-20T18:46:05.738812+00:00"}, "YB4Wh0zckesBQ9cAnGubIx/SqeSYF09d3V25GiWXt44=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\464830vpwb-6pdc2jztkx.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-20T18:46:05.7298126+00:00"}, "l6tmJX4mOxcqyhQARM7s0zeL/RtHw6n592vYaSljA6U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\8wuc26eqrx-493y06b0oq.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-20T18:46:05.738812+00:00"}, "W+f8HxPg0vXjrXtym2022yI+0RlW6M3AHA7FDpILNEI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\82mjt9ldqh-iovd86k7lj.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-20T18:46:05.7258195+00:00"}, "ey38+CI04rSAuS/Y++5YqBQDfOCUPqWIUq+X0yIHA7A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\cb2zi3b42p-vr1egmr9el.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-20T18:46:05.738812+00:00"}, "mOYFw8UkSD72vbVKb/r/LuYm48+oUYV2wz39GM9od/k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\rxhyz9n44e-kbrnm935zg.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-20T18:46:05.7328145+00:00"}, "moQ7y8Qsd8fi7QCq511Ke5/ojC2kc3qb4jMslHmwPAA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\iqzikfx261-jj8uyg4cgr.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-20T18:46:05.7408133+00:00"}, "ndapbZRx6DGExrMIZjUn5NYD3Ae8Tj64ppZgM2CVesI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\k9vswo00sn-y7v9cxd14o.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-20T18:46:05.7278135+00:00"}, "QCI3ervEuo2zTlJ88KhGA+k3NBNdWCWJbd26EZ5ppDw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\zeg52r1bkk-notf2xhcfb.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-20T18:46:05.7408133+00:00"}, "+UnIdp6uZmMybuo4Qsz2f4cHSA6HBXO9nxaFJtnqEv0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\q7ihcpsjx9-h1s4sie4z3.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-20T18:46:05.7248129+00:00"}, "W4y4eYKrFDNNUAsathJA57EIxWnJEMI1GOZ2TYdwUFE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\drrwzc9lwg-63fj8s7r0e.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-20T18:46:05.7368124+00:00"}, "NXWQ6DTQvSkmLgnELZ3QpYlDIpwmcZgdYHZo2uFawEw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\ym507ult0h-0j3bgjxly4.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-20T18:46:05.7278135+00:00"}, "4XdRe+9/ZoAMqAdkmHjVHyPvNroLJEnEzw5mItnuUOU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\avpnk4dhx3-47otxtyo56.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-20T18:46:05.738812+00:00"}, "cnAI5NBNykTBwAFmj3gow3wi/xBiqJ9pgcSRg5FtT3k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\duiio2bnxq-4v8eqarkd7.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-20T18:46:05.7288129+00:00"}, "7lLwUN6AVSOYTnMgIvTMiHv2KJqfX9DcEIb/r8V4YbI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\unkwq17q7x-356vix0kms.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-20T18:46:05.738812+00:00"}, "4auONULAGgLEZ3XKpKuPD2vNIBPGWGGhYM+IkK6D2DI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\0pdgiq115q-83jwlth58m.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-20T18:46:05.7278135+00:00"}, "x2DRF9PM29cPIJz/Vh93WET9ZkTxuTFL1DeV4P30UTc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\bkm4qhaahg-mrlpezrjn3.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-20T18:46:05.7378125+00:00"}, "MITxjJ0hXASiFheYoOE0nvNJ5NxYxZ+3A+x+nkSVADg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\0gvnfc1to7-lzl9nlhx6b.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-20T18:46:05.7278135+00:00"}, "wnf22mL5ZZ0n++iH9lb4RWnhbwdV0wuv444DHKQYRxw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\cpvz16xkcd-ag7o75518u.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-20T18:46:05.738812+00:00"}, "UXaYsa3ADrTK1OplC/l8kGyK36vsS+b6VF1Vv6ZgrHU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\gpp063d3li-x0q3zqp4vz.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-20T18:46:05.7258195+00:00"}, "N2q7TGfoGJyDrWQRUOwLnJbhcD2p7aBN5qHJK9PAaAE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\nqr4hv4dp8-0i3buxo5is.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-20T18:46:05.7398131+00:00"}, "9YQ43+inLYPMIZci1oiFKmmojf/llYiWA8gNOhPwXtQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\pn004j7ovz-o1o13a6vjx.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-20T18:46:05.7278135+00:00"}, "Vg9oVfahSFenaZngI7eyDrzNkWSWn8BjW7ldMCLHyX0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\vko6v2xeew-ttgo8qnofa.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-20T18:46:05.7408133+00:00"}, "ISmWiLqNOvBu7fXn4k7NFKLo9bP5byH4U5ZMpjMFn+8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\uzo3c1x0xr-2z0ns9nrw6.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-20T18:46:05.7318144+00:00"}, "rGgnnz4LmaFyt0jr6dFLxT/iL0x5dO3F6JlI7y973+Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\sv6evfgl72-muycvpuwrr.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-20T18:46:05.7408133+00:00"}, "0DEbEsdgahmyQN8JFc2WeAMFm3VoQCKpYuia4iRG2nM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\4ifu4rbwde-87fc7y1x7t.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-20T18:46:05.7278135+00:00"}, "dAdFnTlOPJ6hHH2Zl7pNVOVa7iKxJ7tMXHYOKfdU7BA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\lm5myi8xbp-mlv21k5csn.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-20T18:46:05.738812+00:00"}, "T6Ac19IN5R/DC/3bKsoIsJpGaPF/mnxDRVyuVBDqTBI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\ca0fomb6qe-7d0qli0ar4.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "MedicalCenterSystem#[.{fingerprint=7d0qli0ar4}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MedicalCenterSystem.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ecd13p94o5", "Integrity": "znDEpKRmNrsgeUF/oT7p3o9hydcgCr4fnzDHHOFHAl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\scopedcss\\bundle\\MedicalCenterSystem.styles.css", "FileLength": 547, "LastWriteTime": "2025-07-20T18:46:05.7258195+00:00"}, "YzLbMs0/85CuMCSBUXQWY4xb7Kp6MLmsrKBoVAGlflE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\exats548fv-7d0qli0ar4.gz", "SourceId": "MedicalCenterSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "MedicalCenterSystem#[.{fingerprint=7d0qli0ar4}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MedicalCenterSystem.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ecd13p94o5", "Integrity": "znDEpKRmNrsgeUF/oT7p3o9hydcgCr4fnzDHHOFHAl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\MedicalCenterSystem.bundle.scp.css", "FileLength": 547, "LastWriteTime": "2025-07-20T18:46:05.7368124+00:00"}}, "CachedCopyCandidates": {}}