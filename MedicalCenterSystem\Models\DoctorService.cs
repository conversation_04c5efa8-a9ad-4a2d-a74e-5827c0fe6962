using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MedicalCenterSystem.Models
{
    public class DoctorService
    {
        public int DoctorServiceId { get; set; }

        [Required]
        public int DoctorId { get; set; }

        [Required]
        public int MedicalServiceId { get; set; }

        [StringLength(20)]
        [Display(Name = "نوع الربط")]
        public string LinkType { get; set; } = string.Empty; // Direct / Referral

        [Display(Name = "له نسبة")]
        public bool HasPercentage { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "النسبة")]
        public decimal? Percentage { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "السعر الافتراضي للطبيب")]
        public decimal? DoctorDefaultPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "تكلفة الخدمة")]
        public decimal? ServiceCost { get; set; }

        [Display(Name = "مبلغ مقطوع")]
        public bool IsFixedAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ المقطوع")]
        public decimal? FixedAmount { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        [ForeignKey("DoctorId")]
        public virtual Doctor Doctor { get; set; } = null!;

        [ForeignKey("MedicalServiceId")]
        public virtual MedicalService MedicalService { get; set; } = null!;
    }
}
