using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;

namespace MedicalCenterWinForms.Services
{
    public class SettingsService
    {
        private readonly DatabaseService _databaseService;
        private readonly Dictionary<string, object> _defaultSettings;

        public SettingsService(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            _defaultSettings = InitializeDefaultSettings();
        }

        // Get all settings
        public async Task<Dictionary<string, object>> GetAllSettings()
        {
            using var context = _databaseService.GetDbContext();
            
            var settings = await context.SystemSettings.ToListAsync();
            var result = new Dictionary<string, object>();

            // Load saved settings
            foreach (var setting in settings)
            {
                try
                {
                    result[setting.SettingKey] = DeserializeValue(setting.SettingValue, setting.SettingType);
                }
                catch
                {
                    // If deserialization fails, use default value
                    if (_defaultSettings.ContainsKey(setting.SettingKey))
                    {
                        result[setting.SettingKey] = _defaultSettings[setting.SettingKey];
                    }
                }
            }

            // Add missing default settings
            foreach (var defaultSetting in _defaultSettings)
            {
                if (!result.ContainsKey(defaultSetting.Key))
                {
                    result[defaultSetting.Key] = defaultSetting.Value;
                }
            }

            return result;
        }

        // Get specific setting
        public async Task<T> GetSetting<T>(string key, T defaultValue = default)
        {
            using var context = _databaseService.GetDbContext();
            
            var setting = await context.SystemSettings
                .FirstOrDefaultAsync(s => s.SettingKey == key);

            if (setting == null)
            {
                return defaultValue;
            }

            try
            {
                return (T)DeserializeValue(setting.SettingValue, setting.SettingType);
            }
            catch
            {
                return defaultValue;
            }
        }

        // Save specific setting
        public async Task SaveSetting<T>(string key, T value)
        {
            using var context = _databaseService.GetDbContext();
            
            var setting = await context.SystemSettings
                .FirstOrDefaultAsync(s => s.SettingKey == key);

            var serializedValue = SerializeValue(value);
            var valueType = typeof(T).Name;

            if (setting == null)
            {
                setting = new SystemSetting
                {
                    SettingKey = key,
                    SettingValue = serializedValue,
                    SettingType = valueType,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now
                };
                context.SystemSettings.Add(setting);
            }
            else
            {
                setting.SettingValue = serializedValue;
                setting.SettingType = valueType;
                setting.UpdatedDate = DateTime.Now;
            }

            await context.SaveChangesAsync();
        }

        // Save all settings
        public async Task SaveAllSettings(Dictionary<string, object> settings)
        {
            using var context = _databaseService.GetDbContext();
            
            foreach (var setting in settings)
            {
                var existingSetting = await context.SystemSettings
                    .FirstOrDefaultAsync(s => s.SettingKey == setting.Key);

                var serializedValue = SerializeValue(setting.Value);
                var valueType = setting.Value?.GetType().Name ?? "String";

                if (existingSetting == null)
                {
                    existingSetting = new SystemSetting
                    {
                        SettingKey = setting.Key,
                        SettingValue = serializedValue,
                        SettingType = valueType,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                    context.SystemSettings.Add(existingSetting);
                }
                else
                {
                    existingSetting.SettingValue = serializedValue;
                    existingSetting.SettingType = valueType;
                    existingSetting.UpdatedDate = DateTime.Now;
                }
            }

            await context.SaveChangesAsync();
        }

        // Reset to default settings
        public async Task ResetToDefaults()
        {
            using var context = _databaseService.GetDbContext();
            
            // Remove all existing settings
            var existingSettings = await context.SystemSettings.ToListAsync();
            context.SystemSettings.RemoveRange(existingSettings);

            // Add default settings
            foreach (var defaultSetting in _defaultSettings)
            {
                var setting = new SystemSetting
                {
                    SettingKey = defaultSetting.Key,
                    SettingValue = SerializeValue(defaultSetting.Value),
                    SettingType = defaultSetting.Value?.GetType().Name ?? "String",
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now
                };
                context.SystemSettings.Add(setting);
            }

            await context.SaveChangesAsync();
        }

        // Test database connection
        public async Task<bool> TestDatabaseConnection()
        {
            try
            {
                using var context = _databaseService.GetDbContext();
                await context.Database.CanConnectAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // Optimize database
        public async Task OptimizeDatabase()
        {
            using var context = _databaseService.GetDbContext();
            
            // Update statistics
            await context.Database.ExecuteSqlRawAsync("UPDATE STATISTICS");
            
            // Rebuild indexes (SQL Server specific)
            var tables = new[] { "Patients", "Doctors", "PatientVisits", "MainPayments", "ReferralPayments", "MedicalServices" };
            
            foreach (var table in tables)
            {
                try
                {
                    await context.Database.ExecuteSqlRawAsync($"ALTER INDEX ALL ON {table} REBUILD");
                }
                catch
                {
                    // Ignore errors for tables that don't exist or don't have indexes
                }
            }
        }

        // Export settings to JSON
        public async Task<string> ExportSettingsToJson()
        {
            var settings = await GetAllSettings();
            return JsonSerializer.Serialize(settings, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
        }

        // Import settings from JSON
        public async Task ImportSettingsFromJson(string json)
        {
            var settings = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
            if (settings != null)
            {
                await SaveAllSettings(settings);
            }
        }

        // Get settings by category
        public async Task<Dictionary<string, object>> GetSettingsByCategory(string category)
        {
            var allSettings = await GetAllSettings();
            return allSettings
                .Where(s => s.Key.StartsWith(category, StringComparison.OrdinalIgnoreCase))
                .ToDictionary(s => s.Key, s => s.Value);
        }

        // Backup settings
        public async Task<string> BackupSettings()
        {
            var settings = await GetAllSettings();
            var backup = new
            {
                BackupDate = DateTime.Now,
                Version = "1.0.0",
                Settings = settings
            };

            return JsonSerializer.Serialize(backup, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
        }

        // Restore settings from backup
        public async Task RestoreSettings(string backupJson)
        {
            var backup = JsonSerializer.Deserialize<Dictionary<string, object>>(backupJson);
            if (backup != null && backup.ContainsKey("Settings"))
            {
                var settings = JsonSerializer.Deserialize<Dictionary<string, object>>(backup["Settings"].ToString());
                if (settings != null)
                {
                    await SaveAllSettings(settings);
                }
            }
        }

        // Helper Methods
        private Dictionary<string, object> InitializeDefaultSettings()
        {
            return new Dictionary<string, object>
            {
                // General Settings
                ["CenterName"] = "المركز الطبي",
                ["CenterAddress"] = "الرياض، المملكة العربية السعودية",
                ["Phone"] = "+966-11-1234567",
                ["Email"] = "<EMAIL>",
                ["WorkingHours"] = "من 8:00 ص إلى 10:00 م",
                ["Language"] = "العربية",
                ["Theme"] = "فاتح",
                ["AutoSave"] = true,
                ["ShowTooltips"] = true,

                // Financial Settings
                ["Currency"] = "ريال سعودي",
                ["TaxRate"] = 15.0m,
                ["PaymentCash"] = true,
                ["PaymentCard"] = true,
                ["PaymentTransfer"] = true,

                // Security Settings
                ["RequireUppercase"] = true,
                ["RequireNumbers"] = true,
                ["RequireSpecialChars"] = false,
                ["SessionTimeout"] = 30,
                ["EnableAudit"] = true,

                // Notification Settings
                ["EmailPayments"] = true,
                ["EmailReports"] = false,
                ["SystemAlerts"] = true,
                ["SoundNotifications"] = true,

                // Backup Settings
                ["AutoBackup"] = true,
                ["BackupFrequency"] = "يومي",
                ["BackupLocation"] = @"C:\Backups\MedicalCenter",
                ["RetentionDays"] = 30,

                // Report Settings
                ["ReportFormat"] = "PDF",
                ["AutoGenerateReports"] = true,
                ["ReportSchedule"] = "شهري",

                // System Settings
                ["EnableCaching"] = true,
                ["EnableLogging"] = true
            };
        }

        private string SerializeValue(object value)
        {
            if (value == null) return string.Empty;
            
            return value switch
            {
                string str => str,
                bool boolean => boolean.ToString(),
                int integer => integer.ToString(),
                decimal dec => dec.ToString(),
                double dbl => dbl.ToString(),
                DateTime dateTime => dateTime.ToString("O"),
                _ => JsonSerializer.Serialize(value)
            };
        }

        private object DeserializeValue(string value, string type)
        {
            if (string.IsNullOrEmpty(value)) return null;

            return type switch
            {
                "String" => value,
                "Boolean" => bool.Parse(value),
                "Int32" => int.Parse(value),
                "Decimal" => decimal.Parse(value),
                "Double" => double.Parse(value),
                "DateTime" => DateTime.Parse(value),
                _ => JsonSerializer.Deserialize<object>(value)
            };
        }
    }
}
