using System;
using System.Drawing;
using System.Windows.Forms;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Controls
{
    public partial class MedicalServicesControl : UserControl
    {
        private readonly DatabaseService _databaseService;

        // UI Components
        private Panel topPanel;
        private Panel contentPanel;
        private DataGridView dgvServices;
        private TextBox txtSearch;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private Label lblSearch;
        private Label lblTotalCount;

        public MedicalServicesControl(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            InitializeComponent();
            LoadServices();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Configure main control
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Dock = DockStyle.Fill;
            this.Padding = new Padding(20);
            this.RightToLeft = RightToLeft.Yes;

            InitializeTopPanel();
            InitializeContentPanel();
            InitializeDataGrid();

            this.ResumeLayout(false);
        }

        private void InitializeTopPanel()
        {
            this.topPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            MaterialDesignHelper.AddShadow(topPanel);

            // Search section
            this.lblSearch = new Label
            {
                Text = "🔍 البحث:",
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(20, 15)
            };

            this.txtSearch = new TextBox
            {
                Size = new Size(300, 30),
                Location = new Point(20, 40),
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes
            };

            // Action buttons
            this.btnAdd = MaterialDesignHelper.CreateModernButton("➕ إضافة خدمة", MaterialDesignHelper.Colors.Success);
            this.btnEdit = MaterialDesignHelper.CreateModernButton("✏️ تعديل", MaterialDesignHelper.Colors.Primary);
            this.btnDelete = MaterialDesignHelper.CreateModernButton("🗑️ حذف", MaterialDesignHelper.Colors.Danger);
            this.btnRefresh = MaterialDesignHelper.CreateModernButton("🔄 تحديث", MaterialDesignHelper.Colors.Secondary);

            btnAdd.Location = new Point(350, 25);
            btnEdit.Location = new Point(480, 25);
            btnDelete.Location = new Point(610, 25);
            btnRefresh.Location = new Point(740, 25);

            // Total count label
            this.lblTotalCount = new Label
            {
                Text = "إجمالي الخدمات: 0",
                Font = ArabicFontHelper.GetArabicFont(10F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                AutoSize = true,
                Location = new Point(870, 35)
            };

            topPanel.Controls.AddRange(new Control[] 
            { 
                lblSearch, txtSearch, btnAdd, btnEdit, btnDelete, btnRefresh, lblTotalCount 
            });

            this.Controls.Add(topPanel);
        }

        private void InitializeContentPanel()
        {
            this.contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20),
                Margin = new Padding(0, 10, 0, 0)
            };

            MaterialDesignHelper.AddShadow(contentPanel);
            this.Controls.Add(contentPanel);
        }

        private void InitializeDataGrid()
        {
            this.dgvServices = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                EnableHeadersVisualStyles = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Configure header style
            dgvServices.ColumnHeadersDefaultCellStyle.BackColor = MaterialDesignHelper.Colors.Primary;
            dgvServices.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvServices.ColumnHeadersDefaultCellStyle.Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold);
            dgvServices.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvServices.ColumnHeadersHeight = 45;

            // Configure row style
            dgvServices.DefaultCellStyle.BackColor = Color.White;
            dgvServices.DefaultCellStyle.ForeColor = MaterialDesignHelper.Colors.TextPrimary;
            dgvServices.DefaultCellStyle.SelectionBackColor = MaterialDesignHelper.Colors.Primary;
            dgvServices.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvServices.RowTemplate.Height = 40;

            // Add columns
            dgvServices.Columns.Add("Id", "الرقم");
            dgvServices.Columns.Add("ServiceName", "اسم الخدمة");
            dgvServices.Columns.Add("Category", "الفئة");
            dgvServices.Columns.Add("Price", "السعر (ر.س)");
            dgvServices.Columns.Add("Duration", "المدة (دقيقة)");
            dgvServices.Columns.Add("IsActive", "الحالة");
            dgvServices.Columns.Add("CreatedDate", "تاريخ الإنشاء");

            // Configure column widths
            dgvServices.Columns["Id"].Width = 80;
            dgvServices.Columns["ServiceName"].Width = 200;
            dgvServices.Columns["Category"].Width = 150;
            dgvServices.Columns["Price"].Width = 120;
            dgvServices.Columns["Duration"].Width = 100;
            dgvServices.Columns["IsActive"].Width = 80;
            dgvServices.Columns["CreatedDate"].Width = 150;

            contentPanel.Controls.Add(dgvServices);
        }

        private void LoadServices()
        {
            try
            {
                // Sample data - replace with actual database queries
                var sampleServices = new[]
                {
                    new { Id = 1, ServiceName = "كشف عام", Category = "طب عام", Price = "150", Duration = "30", IsActive = "نشط", CreatedDate = "2025/01/15" },
                    new { Id = 2, ServiceName = "كشف أطفال", Category = "طب أطفال", Price = "200", Duration = "45", IsActive = "نشط", CreatedDate = "2025/01/14" },
                    new { Id = 3, ServiceName = "كشف نساء وولادة", Category = "نساء وولادة", Price = "250", Duration = "60", IsActive = "نشط", CreatedDate = "2025/01/13" },
                    new { Id = 4, ServiceName = "كشف عيون", Category = "طب عيون", Price = "180", Duration = "30", IsActive = "نشط", CreatedDate = "2025/01/12" },
                    new { Id = 5, ServiceName = "كشف أسنان", Category = "طب أسنان", Price = "120", Duration = "45", IsActive = "نشط", CreatedDate = "2025/01/11" },
                    new { Id = 6, ServiceName = "تحليل دم شامل", Category = "مختبر", Price = "80", Duration = "15", IsActive = "نشط", CreatedDate = "2025/01/10" },
                    new { Id = 7, ServiceName = "أشعة سينية", Category = "أشعة", Price = "100", Duration = "20", IsActive = "نشط", CreatedDate = "2025/01/09" },
                    new { Id = 8, ServiceName = "إيكو القلب", Category = "قلب", Price = "300", Duration = "60", IsActive = "نشط", CreatedDate = "2025/01/08" },
                    new { Id = 9, ServiceName = "علاج طبيعي", Category = "علاج طبيعي", Price = "100", Duration = "45", IsActive = "نشط", CreatedDate = "2025/01/07" },
                    new { Id = 10, ServiceName = "استشارة نفسية", Category = "طب نفسي", Price = "200", Duration = "60", IsActive = "نشط", CreatedDate = "2025/01/06" }
                };

                dgvServices.Rows.Clear();
                foreach (var service in sampleServices)
                {
                    dgvServices.Rows.Add(
                        service.Id,
                        service.ServiceName,
                        service.Category,
                        service.Price,
                        service.Duration,
                        service.IsActive,
                        service.CreatedDate
                    );
                }

                lblTotalCount.Text = $"إجمالي الخدمات: {dgvServices.Rows.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
