using System.Drawing;
using System.Drawing.Drawing2D;
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Styles
{
    /// <summary>
    /// Modern Medical Theme Manager for Ultra Modern UI Design
    /// Provides advanced styling and theming capabilities for medical applications
    /// </summary>
    public static class ModernMedicalTheme
    {
        // Theme Configuration
        public static class Config
        {
            public static bool IsDarkMode { get; set; } = false;
            public static bool UseAnimations { get; set; } = true;
            public static bool UseGradients { get; set; } = true;
            public static bool UseShadows { get; set; } = true;
            public static float AnimationSpeed { get; set; } = 0.3f;
        }

        // Advanced Color Schemes
        public static class ColorSchemes
        {
            public static class Light
            {
                public static readonly Color Primary = Color.FromArgb(37, 99, 235);
                public static readonly Color Secondary = Color.FromArgb(6, 182, 212);
                public static readonly Color Accent = Color.FromArgb(16, 185, 129);
                public static readonly Color Background = Color.FromArgb(249, 250, 251);
                public static readonly Color Surface = Color.FromArgb(255, 255, 255);
                public static readonly Color Text = Color.FromArgb(17, 24, 39);
            }

            public static class Dark
            {
                public static readonly Color Primary = Color.FromArgb(59, 130, 246);
                public static readonly Color Secondary = Color.FromArgb(34, 211, 238);
                public static readonly Color Accent = Color.FromArgb(52, 211, 153);
                public static readonly Color Background = Color.FromArgb(17, 24, 39);
                public static readonly Color Surface = Color.FromArgb(31, 41, 55);
                public static readonly Color Text = Color.FromArgb(249, 250, 251);
            }
        }

        // Current Colors Helper
        public static class CurrentColors
        {
            public static Color Primary => Config.IsDarkMode ? ColorSchemes.Dark.Primary : ColorSchemes.Light.Primary;
            public static Color Secondary => Config.IsDarkMode ? ColorSchemes.Dark.Secondary : ColorSchemes.Light.Secondary;
            public static Color Accent => Config.IsDarkMode ? ColorSchemes.Dark.Accent : ColorSchemes.Light.Accent;
            public static Color Background => Config.IsDarkMode ? ColorSchemes.Dark.Background : ColorSchemes.Light.Background;
            public static Color Surface => Config.IsDarkMode ? ColorSchemes.Dark.Surface : ColorSchemes.Light.Surface;
            public static Color Text => Config.IsDarkMode ? ColorSchemes.Dark.Text : ColorSchemes.Light.Text;
        }

        // Modern Component Styles
        public static class Components
        {
            // Modern Card with Advanced Styling
            public static Panel CreateAdvancedCard(string title = "", bool hasHeader = true)
            {
                var card = new Panel
                {
                    BackColor = CurrentColors.Surface,
                    Padding = new Padding(24),
                    BorderStyle = BorderStyle.None,
                    Margin = new Padding(16)
                };

                if (hasHeader && !string.IsNullOrEmpty(title))
                {
                    var header = CreateCardHeader(title);
                    card.Controls.Add(header);
                }

                // Advanced card painting
                card.Paint += (sender, e) =>
                {
                    var rect = card.ClientRectangle;
                    e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                    e.Graphics.CompositingQuality = CompositingQuality.HighQuality;

                    // Multiple shadow layers for realistic depth
                    if (Config.UseShadows)
                    {
                        DrawAdvancedShadow(e.Graphics, rect, 3);
                    }

                    // Main card with rounded corners
                    using var cardPath = MaterialDesignHelper.CreateRoundedRectanglePath(rect, 16);
                    
                    if (Config.UseGradients)
                    {
                        using var cardBrush = new LinearGradientBrush(
                            rect,
                            CurrentColors.Surface,
                            MaterialDesignHelper.LightenColor(CurrentColors.Surface, 0.02f),
                            LinearGradientMode.Vertical);
                        e.Graphics.FillPath(cardBrush, cardPath);
                    }
                    else
                    {
                        using var cardBrush = new SolidBrush(CurrentColors.Surface);
                        e.Graphics.FillPath(cardBrush, cardPath);
                    }

                    // Modern border
                    using var borderPen = new Pen(MaterialDesignHelper.Colors.BorderLight, 1.5f);
                    e.Graphics.DrawPath(borderPen, cardPath);

                    // Top accent line
                    var accentRect = new Rectangle(rect.X + 16, rect.Y, rect.Width - 32, 3);
                    using var accentBrush = new LinearGradientBrush(
                        accentRect,
                        CurrentColors.Primary,
                        CurrentColors.Secondary,
                        LinearGradientMode.Horizontal);
                    e.Graphics.FillRectangle(accentBrush, accentRect);
                };

                return card;
            }

            // Modern Card Header
            private static Panel CreateCardHeader(string title)
            {
                var header = new Panel
                {
                    Height = 60,
                    Dock = DockStyle.Top,
                    BackColor = Color.Transparent
                };

                var titleLabel = new Label
                {
                    Text = title,
                    Font = ArabicFontHelper.GetArabicHeaderFont(16F, FontStyle.Bold),
                    ForeColor = CurrentColors.Text,
                    AutoSize = false,
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleRight,
                    Padding = new Padding(0, 16, 0, 16)
                };

                header.Controls.Add(titleLabel);
                return header;
            }

            // Modern Button with Advanced Effects
            public static Button CreateAdvancedButton(string text, ButtonStyle style = ButtonStyle.Primary)
            {
                var button = new Button
                {
                    Text = text,
                    Font = ArabicFontHelper.GetArabicButtonFont(13F),
                    Size = new Size(140, 48),
                    FlatStyle = FlatStyle.Flat,
                    UseVisualStyleBackColor = false,
                    Cursor = Cursors.Hand,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Margin = new Padding(8)
                };

                ApplyButtonStyle(button, style);
                return button;
            }

            // Button Styles
            public enum ButtonStyle
            {
                Primary,
                Secondary,
                Success,
                Warning,
                Error,
                Ghost
            }

            private static void ApplyButtonStyle(Button button, ButtonStyle style)
            {
                Color backgroundColor, textColor;

                switch (style)
                {
                    case ButtonStyle.Primary:
                        backgroundColor = CurrentColors.Primary;
                        textColor = Color.White;
                        break;
                    case ButtonStyle.Secondary:
                        backgroundColor = CurrentColors.Secondary;
                        textColor = Color.White;
                        break;
                    case ButtonStyle.Success:
                        backgroundColor = MaterialDesignHelper.Colors.Success;
                        textColor = Color.White;
                        break;
                    case ButtonStyle.Warning:
                        backgroundColor = MaterialDesignHelper.Colors.Warning;
                        textColor = Color.White;
                        break;
                    case ButtonStyle.Error:
                        backgroundColor = MaterialDesignHelper.Colors.Error;
                        textColor = Color.White;
                        break;
                    case ButtonStyle.Ghost:
                        backgroundColor = Color.Transparent;
                        textColor = CurrentColors.Primary;
                        break;
                    default:
                        backgroundColor = CurrentColors.Primary;
                        textColor = Color.White;
                        break;
                }

                button.BackColor = backgroundColor;
                button.ForeColor = textColor;
                button.FlatAppearance.BorderSize = style == ButtonStyle.Ghost ? 2 : 0;
                button.FlatAppearance.BorderColor = style == ButtonStyle.Ghost ? CurrentColors.Primary : backgroundColor;

                // Advanced button effects
                SetupAdvancedButtonEffects(button, backgroundColor, textColor, style);
            }

            private static void SetupAdvancedButtonEffects(Button button, Color backgroundColor, Color textColor, ButtonStyle style)
            {
                var originalColor = backgroundColor;
                var isPressed = false;

                // Custom painting for advanced effects
                button.Paint += (sender, e) =>
                {
                    var rect = button.ClientRectangle;
                    e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                    // Shadow for non-ghost buttons
                    if (style != ButtonStyle.Ghost && Config.UseShadows)
                    {
                        var shadowRect = new Rectangle(rect.X + 2, rect.Y + 3, rect.Width - 2, rect.Height - 3);
                        using var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0));
                        e.Graphics.FillEllipse(shadowBrush, shadowRect);
                    }

                    // Button background
                    using var buttonPath = MaterialDesignHelper.CreateRoundedRectanglePath(rect, 12);
                    
                    if (style != ButtonStyle.Ghost)
                    {
                        if (Config.UseGradients)
                        {
                            using var gradientBrush = new LinearGradientBrush(
                                rect,
                                button.BackColor,
                                MaterialDesignHelper.LightenColor(button.BackColor, 0.1f),
                                LinearGradientMode.Vertical);
                            e.Graphics.FillPath(gradientBrush, buttonPath);
                        }
                        else
                        {
                            using var backgroundBrush = new SolidBrush(button.BackColor);
                            e.Graphics.FillPath(backgroundBrush, buttonPath);
                        }
                    }

                    // Border for ghost buttons
                    if (style == ButtonStyle.Ghost)
                    {
                        using var borderPen = new Pen(button.FlatAppearance.BorderColor, 2);
                        e.Graphics.DrawPath(borderPen, buttonPath);
                    }

                    // Text rendering
                    var textRect = new Rectangle(0, 0, rect.Width, rect.Height);
                    TextRenderer.DrawText(e.Graphics, button.Text, button.Font, textRect,
                        button.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
                };

                // Hover effects
                button.MouseEnter += (s, e) =>
                {
                    if (!isPressed)
                    {
                        button.BackColor = style == ButtonStyle.Ghost ? 
                            MaterialDesignHelper.WithOpacity(originalColor, 20) :
                            MaterialDesignHelper.LightenColor(originalColor, 0.1f);
                        button.Invalidate();
                    }
                };

                button.MouseLeave += (s, e) =>
                {
                    if (!isPressed)
                    {
                        button.BackColor = originalColor;
                        button.Invalidate();
                    }
                };

                button.MouseDown += (s, e) =>
                {
                    isPressed = true;
                    button.BackColor = style == ButtonStyle.Ghost ?
                        MaterialDesignHelper.WithOpacity(originalColor, 40) :
                        MaterialDesignHelper.DarkenColor(originalColor, 0.1f);
                    button.Invalidate();
                };

                button.MouseUp += (s, e) =>
                {
                    isPressed = false;
                    button.BackColor = MaterialDesignHelper.LightenColor(originalColor, 0.1f);
                    button.Invalidate();
                };
            }

            // Modern ComboBox with Advanced Styling
            public static ComboBox CreateAdvancedComboBox()
            {
                var comboBox = new ComboBox
                {
                    Font = ArabicFontHelper.GetArabicFont(10F),
                    BackColor = CurrentColors.Surface,
                    ForeColor = CurrentColors.Text,
                    FlatStyle = FlatStyle.Flat,
                    DropDownStyle = ComboBoxStyle.DropDownList
                };

                return comboBox;
            }

            // Modern TextBox with Advanced Styling
            public static TextBox CreateAdvancedTextBox(string placeholder = "")
            {
                var textBox = new TextBox
                {
                    Font = ArabicFontHelper.GetArabicFont(10F),
                    BackColor = CurrentColors.Surface,
                    ForeColor = CurrentColors.Text,
                    BorderStyle = BorderStyle.FixedSingle,
                    Tag = placeholder
                };

                // Add placeholder functionality
                if (!string.IsNullOrEmpty(placeholder))
                {
                    textBox.Text = placeholder;
                    textBox.ForeColor = Color.Gray;

                    textBox.Enter += (sender, e) =>
                    {
                        if (textBox.Text == placeholder)
                        {
                            textBox.Text = "";
                            textBox.ForeColor = CurrentColors.Text;
                        }
                    };

                    textBox.Leave += (sender, e) =>
                    {
                        if (string.IsNullOrWhiteSpace(textBox.Text))
                        {
                            textBox.Text = placeholder;
                            textBox.ForeColor = Color.Gray;
                        }
                    };
                }

                return textBox;
            }

            // Apply Modern Theme to DataGridView
            public static void ApplyDataGridViewTheme(DataGridView dataGridView)
            {
                // Header styling
                dataGridView.ColumnHeadersDefaultCellStyle.BackColor = CurrentColors.Primary;
                dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                dataGridView.ColumnHeadersDefaultCellStyle.Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold);
                dataGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dataGridView.ColumnHeadersHeight = 40;

                // Row styling
                dataGridView.DefaultCellStyle.BackColor = CurrentColors.Surface;
                dataGridView.DefaultCellStyle.ForeColor = CurrentColors.Text;
                dataGridView.DefaultCellStyle.Font = ArabicFontHelper.GetArabicFont(9F);
                dataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(230, 244, 255);
                dataGridView.DefaultCellStyle.SelectionForeColor = CurrentColors.Text;

                // Alternating row colors
                dataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 250, 252);
                dataGridView.AlternatingRowsDefaultCellStyle.ForeColor = CurrentColors.Text;

                // Grid styling
                dataGridView.GridColor = Color.FromArgb(229, 231, 235);
                dataGridView.BackgroundColor = CurrentColors.Background;
                dataGridView.BorderStyle = BorderStyle.None;
                dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
                dataGridView.RowHeadersVisible = false;
                dataGridView.EnableHeadersVisualStyles = false;

                // Row height
                dataGridView.RowTemplate.Height = 35;
            }
        }

        // Helper Methods - تم إزالة هذه الدالة واستبدالها بـ CurrentColors

        private static void DrawAdvancedShadow(Graphics graphics, Rectangle rect, int elevation)
        {
            for (int i = 0; i < elevation; i++)
            {
                var shadowRect = new Rectangle(
                    rect.X + i + 1, 
                    rect.Y + i + 2, 
                    rect.Width - i, 
                    rect.Height - i);
                
                var alpha = Math.Max(5, 20 - (i * 5));
                using var shadowBrush = new SolidBrush(Color.FromArgb(alpha, 0, 0, 0));
                using var shadowPath = MaterialDesignHelper.CreateRoundedRectanglePath(shadowRect, 16 - i);
                graphics.FillPath(shadowBrush, shadowPath);
            }
        }

        // Theme Application Methods
        public static void ApplyThemeToForm(Form form)
        {
            form.BackColor = CurrentColors.Background;
            form.ForeColor = CurrentColors.Text;

            // Apply to all child controls recursively
            ApplyThemeToControls(form.Controls);
        }

        private static void ApplyThemeToControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                ApplyThemeToControl(control);
                if (control.HasChildren)
                {
                    ApplyThemeToControls(control.Controls);
                }
            }
        }

        private static void ApplyThemeToControl(Control control)
        {
            switch (control)
            {
                case Panel panel:
                    panel.BackColor = CurrentColors.Surface;
                    break;
                case Label label:
                    label.ForeColor = CurrentColors.Text;
                    break;
                case TextBox textBox:
                    textBox.BackColor = CurrentColors.Surface;
                    textBox.ForeColor = CurrentColors.Text;
                    break;
                case Button button:
                    // Buttons are handled by CreateAdvancedButton method
                    break;
            }
        }


    }
}
