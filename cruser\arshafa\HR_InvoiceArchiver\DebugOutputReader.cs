using System;
using System.Diagnostics;
using System.IO;
using System.Text;

namespace HR_InvoiceArchiver
{
    public class DebugOutputReader
    {
        private static readonly string LogFilePath = Path.Combine(Path.GetTempPath(), "HR_InvoiceArchiver_Debug.log");
        private static StreamWriter? _logWriter;

        static DebugOutputReader()
        {
            try
            {
                _logWriter = new StreamWriter(LogFilePath, false, Encoding.UTF8) { AutoFlush = true };
                
                // Add a custom trace listener to capture Debug.WriteLine output
                Trace.Listeners.Add(new TextWriterTraceListener(_logWriter));
                
                Debug.WriteLine("=== DEBUG OUTPUT READER INITIALIZED ===");
                Debug.WriteLine($"Log file: {LogFilePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize debug output reader: {ex.Message}");
            }
        }

        public static void WriteDebug(string message)
        {
            Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] {message}");
        }

        public static void Dispose()
        {
            _logWriter?.Dispose();
        }
    }
}
