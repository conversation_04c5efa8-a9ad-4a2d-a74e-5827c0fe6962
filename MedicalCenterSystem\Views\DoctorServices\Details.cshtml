@model MedicalCenterSystem.Models.DoctorService

@{
    ViewData["Title"] = "تفاصيل ربط الطبيب بالخدمة";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل ربط الطبيب بالخدمة
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">معلومات الطبيب</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-md fa-2x text-primary me-3"></i>
                                        <div>
                                            <h5 class="mb-1">@Html.DisplayFor(model => model.Doctor.FullName)</h5>
                                            <p class="text-muted mb-0">@Html.DisplayFor(model => model.Doctor.Specialty)</p>
                                            <span class="badge bg-@(Model.Doctor.IsActive ? "success" : "secondary") mt-1">
                                                @(Model.Doctor.IsActive ? "نشط" : "غير نشط")
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">معلومات الخدمة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-stethoscope fa-2x text-success me-3"></i>
                                        <div>
                                            <h5 class="mb-1">@Html.DisplayFor(model => model.MedicalService.ServiceName)</h5>
                                            <p class="text-muted mb-0">
                                                نوع: @(Model.MedicalService.ServiceType == "Direct" ? "مباشر" : "تحويل")
                                            </p>
                                            <p class="text-muted mb-0">
                                                السعر الافتراضي: @(Model.MedicalService.DefaultPrice?.ToString("C") ?? "غير محدد")
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Link Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">إعدادات الربط</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label text-muted">نوع الربط:</label>
                                    <div>
                                        <span class="badge bg-@(Model.LinkType == "Direct" ? "primary" : "info") fs-6">
                                            @(Model.LinkType == "Direct" ? "مباشر" : "تحويل")
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label text-muted">الحالة:</label>
                                    <div>
                                        <span class="badge bg-@(Model.IsActive ? "success" : "secondary") fs-6">
                                            @(Model.IsActive ? "نشط" : "غير نشط")
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label text-muted">تاريخ الإنشاء:</label>
                                    <div>
                                        <strong>@Model.CreatedDate.ToString("yyyy-MM-dd HH:mm")</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">إعدادات المدفوعات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card border-0 bg-light h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-percentage fa-2x text-warning mb-2"></i>
                                            <h6>النسبة المئوية</h6>
                                            @if (Model.HasPercentage)
                                            {
                                                <span class="badge bg-success mb-2">مفعل</span>
                                                <p class="mb-0"><strong>@(Model.Percentage?.ToString("F2") ?? "0")%</strong></p>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary mb-2">غير مفعل</span>
                                                <p class="mb-0 text-muted">-</p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card border-0 bg-light h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                                            <h6>المبلغ المقطوع</h6>
                                            @if (Model.IsFixedAmount)
                                            {
                                                <span class="badge bg-success mb-2">مفعل</span>
                                                <p class="mb-0"><strong>@(Model.FixedAmount?.ToString("C") ?? "0")</strong></p>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary mb-2">غير مفعل</span>
                                                <p class="mb-0 text-muted">-</p>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">السعر الافتراضي للطبيب:</label>
                                    <div>
                                        <strong>@(Model.DoctorDefaultPrice?.ToString("C") ?? "غير محدد")</strong>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">تكلفة الخدمة:</label>
                                    <div>
                                        <strong>@(Model.ServiceCost?.ToString("C") ?? "غير محدد")</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Calculation Example -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">مثال على الحساب</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="exampleAmount" class="form-label">مبلغ المثال:</label>
                                    <input type="number" class="form-control" id="exampleAmount" value="1000" step="0.01" min="0">
                                </div>
                                <div class="col-md-6 mb-3 d-flex align-items-end">
                                    <button type="button" class="btn btn-info" onclick="calculateExample()">
                                        <i class="fas fa-calculator me-1"></i> احسب المثال
                                    </button>
                                </div>
                            </div>
                            <div id="exampleResult" class="alert alert-light" style="display: none;">
                                <div id="exampleText"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    @if (!string.IsNullOrWhiteSpace(Model.Notes))
                    {
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">ملاحظات</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">@Html.DisplayFor(model => model.Notes)</p>
                            </div>
                        </div>
                    }

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> العودة للقائمة
                        </a>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.DoctorServiceId" class="btn btn-warning me-2">
                                <i class="fas fa-edit me-1"></i> تعديل
                            </a>
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="fas fa-trash me-1"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف ربط الطبيب "<strong>@Model.Doctor.FullName</strong>" بالخدمة "<strong>@Model.MedicalService.ServiceName</strong>"؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form asp-action="Delete" asp-route-id="@Model.DoctorServiceId" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function calculateExample() {
            const amount = parseFloat($('#exampleAmount').val()) || 0;
            const hasPercentage = @(Model.HasPercentage ? "true" : "false");
            const isFixedAmount = @(Model.IsFixedAmount ? "true" : "false");
            const percentage = @(Model.Percentage ?? 0);
            const fixedAmount = @(Model.FixedAmount ?? 0);
            const serviceCost = @(Model.ServiceCost ?? 0);

            let doctorShare = 0;
            let centerShare = amount;

            if (isFixedAmount) {
                doctorShare = fixedAmount;
            } else if (hasPercentage) {
                const netAmount = amount - serviceCost;
                if (netAmount > 0) {
                    doctorShare = netAmount * (percentage / 100);
                }
            }

            centerShare = amount - doctorShare;

            const resultHtml = `
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h6 class="text-muted">المبلغ الكلي</h6>
                            <h4 class="text-primary">${amount.toFixed(2)} ريال</h4>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h6 class="text-muted">حصة الطبيب</h6>
                            <h4 class="text-success">${doctorShare.toFixed(2)} ريال</h4>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h6 class="text-muted">حصة المركز</h6>
                            <h4 class="text-info">${centerShare.toFixed(2)} ريال</h4>
                        </div>
                    </div>
                </div>
            `;

            $('#exampleText').html(resultHtml);
            $('#exampleResult').show();
        }

        function confirmDelete() {
            $('#deleteModal').modal('show');
        }

        // Calculate example on page load
        $(document).ready(function() {
            calculateExample();
        });
    </script>
}

@section Styles {
    <style>
        .card {
            border: none;
            border-radius: 10px;
        }
        
        .card-header {
            border-radius: 10px 10px 0 0;
        }
        
        .badge.fs-6 {
            font-size: 0.875rem !important;
        }
        
        .alert-light {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }
        
        .border {
            border-color: #dee2e6 !important;
        }
        
        .rounded {
            border-radius: 8px !important;
        }
    </style>
}
