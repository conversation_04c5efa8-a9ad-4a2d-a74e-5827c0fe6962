using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.IO;
using System.Threading.Tasks;
using Moq;

namespace HR_InvoiceArchiver.Tests.Services
{
    public class BackupRestoreServiceTests : IDisposable
    {
        private readonly DatabaseContext _context;
        private readonly BackupRestoreService _backupRestoreService;
        private readonly Mock<ILoggingService> _mockLoggingService;
        private readonly Mock<IEncryptionService> _mockEncryptionService;
        private readonly Mock<ISecurityService> _mockSecurityService;
        private readonly string _testDirectory;

        public BackupRestoreServiceTests()
        {
            // إعداد قاعدة بيانات في الذاكرة للاختبار
            var options = new DbContextOptionsBuilder<DatabaseContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new DatabaseContext(options);
            _mockLoggingService = new Mock<ILoggingService>();
            _mockEncryptionService = new Mock<IEncryptionService>();
            _mockSecurityService = new Mock<ISecurityService>();

            // إعداد مجلد اختبار مؤقت
            _testDirectory = Path.Combine(Path.GetTempPath(), "HR_InvoiceArchiver_Backup_Tests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);

            // إعداد mock للتشفير
            _mockEncryptionService.Setup(x => x.EncryptAsync(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync((string data, string password) => Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(data + "_encrypted")));
            
            _mockEncryptionService.Setup(x => x.DecryptAsync(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync((string data, string password) => System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(data)).Replace("_encrypted", ""));

            _backupRestoreService = new BackupRestoreService(
                _context,
                _mockLoggingService.Object,
                _mockEncryptionService.Object,
                _mockSecurityService.Object);

            // إضافة بيانات تجريبية
            SeedTestData();
        }

        private void SeedTestData()
        {
            // إضافة موردين تجريبيين
            var suppliers = new[]
            {
                new HR_InvoiceArchiver.Models.Supplier { Name = "مورد 1", Email = "<EMAIL>", IsActive = true },
                new HR_InvoiceArchiver.Models.Supplier { Name = "مورد 2", Email = "<EMAIL>", IsActive = true }
            };
            _context.Suppliers.AddRange(suppliers);

            // إضافة فواتير تجريبية
            var invoices = new[]
            {
                new HR_InvoiceArchiver.Models.Invoice 
                { 
                    InvoiceNumber = "INV001", 
                    InvoiceDate = DateTime.Now.AddDays(-10),
                    Amount = 1000,
                    SupplierId = 1,
                    Status = HR_InvoiceArchiver.Models.InvoiceStatus.Paid
                },
                new HR_InvoiceArchiver.Models.Invoice 
                { 
                    InvoiceNumber = "INV002", 
                    InvoiceDate = DateTime.Now.AddDays(-5),
                    Amount = 2000,
                    SupplierId = 2,
                    Status = HR_InvoiceArchiver.Models.InvoiceStatus.Pending
                }
            };
            _context.Invoices.AddRange(invoices);

            _context.SaveChanges();
        }

        [Fact]
        public async Task CreateFullBackupAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var options = new BackupOptions
            {
                BackupPath = Path.Combine(_testDirectory, "full_backup_test.bak"),
                Type = BackupType.Full,
                IncludeAttachments = true,
                IncludeSettings = true,
                CompressBackup = true,
                Description = "اختبار النسخة الاحتياطية الكاملة"
            };

            // Act
            var result = await _backupRestoreService.CreateFullBackupAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Type.Should().Be(BackupType.Full);
            result.BackupPath.Should().NotBeNullOrEmpty();
            result.BackupSizeBytes.Should().BeGreaterThan(0);
            result.Duration.Should().BeGreaterThan(TimeSpan.Zero);
            result.Metadata.Should().NotBeNull();
            File.Exists(result.BackupPath).Should().BeTrue();
        }

        [Fact]
        public async Task CreateIncrementalBackupAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var options = new BackupOptions
            {
                BackupPath = Path.Combine(_testDirectory, "incremental_backup_test.bak"),
                Type = BackupType.Incremental,
                CompressBackup = false,
                Description = "اختبار النسخة الاحتياطية التزايدية"
            };

            // Act
            var result = await _backupRestoreService.CreateIncrementalBackupAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Type.Should().Be(BackupType.Incremental);
            result.BackupPath.Should().NotBeNullOrEmpty();
            File.Exists(result.BackupPath).Should().BeTrue();
        }

        [Fact]
        public async Task CreateDifferentialBackupAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var options = new BackupOptions
            {
                BackupPath = Path.Combine(_testDirectory, "differential_backup_test.bak"),
                Type = BackupType.Differential,
                CompressBackup = true,
                Description = "اختبار النسخة الاحتياطية التفاضلية"
            };

            // Act
            var result = await _backupRestoreService.CreateDifferentialBackupAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Type.Should().Be(BackupType.Differential);
            result.BackupPath.Should().NotBeNullOrEmpty();
            File.Exists(result.BackupPath).Should().BeTrue();
        }

        [Fact]
        public async Task CreateFullBackupAsync_WithEncryption_ShouldReturnEncryptedBackup()
        {
            // Arrange
            var options = new BackupOptions
            {
                BackupPath = Path.Combine(_testDirectory, "encrypted_backup_test.bak"),
                Type = BackupType.Full,
                EncryptBackup = true,
                Password = "test_password_123",
                CompressBackup = false,
                Description = "اختبار النسخة الاحتياطية المشفرة"
            };

            // Act
            var result = await _backupRestoreService.CreateFullBackupAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.BackupPath.Should().EndWith(".enc");
            File.Exists(result.BackupPath).Should().BeTrue();
            
            // التحقق من أن التشفير تم استدعاؤه
            _mockEncryptionService.Verify(x => x.EncryptAsync(It.IsAny<string>(), "test_password_123"), Times.Once);
        }

        [Fact]
        public async Task RestoreFromBackupAsync_WithValidBackup_ShouldReturnSuccessResult()
        {
            // Arrange
            // إنشاء نسخة احتياطية أولاً
            var backupOptions = new BackupOptions
            {
                BackupPath = Path.Combine(_testDirectory, "restore_test_backup.bak"),
                Type = BackupType.Full,
                CompressBackup = false
            };
            var backupResult = await _backupRestoreService.CreateFullBackupAsync(backupOptions);
            backupResult.Success.Should().BeTrue();

            var restoreOptions = new RestoreOptions
            {
                BackupPath = backupResult.BackupPath,
                Mode = RestoreMode.Complete,
                CreateBackupBeforeRestore = false,
                VerifyIntegrity = true
            };

            // Act
            var result = await _backupRestoreService.RestoreFromBackupAsync(restoreOptions);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Duration.Should().BeGreaterThan(TimeSpan.Zero);
            result.TablesRestored.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task ValidateBackupAsync_WithValidBackup_ShouldReturnValidResult()
        {
            // Arrange
            // إنشاء نسخة احتياطية صحيحة
            var backupOptions = new BackupOptions
            {
                BackupPath = Path.Combine(_testDirectory, "validation_test_backup.bak"),
                Type = BackupType.Full
            };
            var backupResult = await _backupRestoreService.CreateFullBackupAsync(backupOptions);
            backupResult.Success.Should().BeTrue();

            // Act
            var result = await _backupRestoreService.ValidateBackupAsync(backupResult.BackupPath);

            // Assert
            result.Should().NotBeNull();
            result.IsValid.Should().BeTrue();
            result.Errors.Should().BeEmpty();
        }

        [Fact]
        public async Task ValidateBackupAsync_WithNonExistentFile_ShouldReturnInvalidResult()
        {
            // Arrange
            var nonExistentPath = Path.Combine(_testDirectory, "non_existent_backup.bak");

            // Act
            var result = await _backupRestoreService.ValidateBackupAsync(nonExistentPath);

            // Assert
            result.Should().NotBeNull();
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain("ملف النسخة الاحتياطية غير موجود");
        }

        [Fact]
        public async Task ValidateBackupAsync_WithEmptyFile_ShouldReturnInvalidResult()
        {
            // Arrange
            var emptyFilePath = Path.Combine(_testDirectory, "empty_backup.bak");
            await File.WriteAllTextAsync(emptyFilePath, ""); // ملف فارغ

            // Act
            var result = await _backupRestoreService.ValidateBackupAsync(emptyFilePath);

            // Assert
            result.Should().NotBeNull();
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain("ملف النسخة الاحتياطية فارغ");
        }

        [Fact]
        public async Task GetAvailableBackupsAsync_ShouldReturnBackupsList()
        {
            // Arrange
            // إنشاء عدة نسخ احتياطية
            for (int i = 1; i <= 3; i++)
            {
                var options = new BackupOptions
                {
                    BackupPath = Path.Combine(_testDirectory, $"backup_{i}.bak"),
                    Type = BackupType.Full,
                    Description = $"نسخة احتياطية رقم {i}"
                };
                await _backupRestoreService.CreateFullBackupAsync(options);
            }

            // Act
            var result = await _backupRestoreService.GetAvailableBackupsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCountGreaterOrEqualTo(3);
            result.Should().OnlyContain(b => b.IsValid);
        }

        [Fact]
        public async Task CleanupOldBackupsAsync_ShouldDeleteOldBackups()
        {
            // Arrange
            // إنشاء ملف قديم
            var oldBackupPath = Path.Combine(_testDirectory, "old_backup.bak");
            await File.WriteAllTextAsync(oldBackupPath, "Old backup content");
            
            // تعديل تاريخ الإنشاء ليكون قديماً
            File.SetCreationTime(oldBackupPath, DateTime.Now.AddDays(-35));

            // إنشاء ملف حديث
            var recentBackupPath = Path.Combine(_testDirectory, "recent_backup.bak");
            await File.WriteAllTextAsync(recentBackupPath, "Recent backup content");

            // Act
            var deletedCount = await _backupRestoreService.CleanupOldBackupsAsync(30);

            // Assert
            deletedCount.Should().BeGreaterOrEqualTo(0);
            File.Exists(recentBackupPath).Should().BeTrue(); // الملف الحديث يجب أن يبقى
        }

        [Fact]
        public async Task GetBackupStatisticsAsync_ShouldReturnStatistics()
        {
            // Arrange
            // إنشاء بعض النسخ الاحتياطية
            var options = new BackupOptions
            {
                BackupPath = Path.Combine(_testDirectory, "stats_test_backup.bak"),
                Type = BackupType.Full
            };
            await _backupRestoreService.CreateFullBackupAsync(options);

            // Act
            var result = await _backupRestoreService.GetBackupStatisticsAsync();

            // Assert
            result.Should().NotBeNull();
            result.TotalBackups.Should().BeGreaterOrEqualTo(0);
            result.TotalBackupSizeBytes.Should().BeGreaterOrEqualTo(0);
            result.SuccessfulBackups.Should().BeGreaterOrEqualTo(0);
            result.FailedBackups.Should().BeGreaterOrEqualTo(0);
        }

        [Fact]
        public async Task TestRestoreAsync_WithValidBackup_ShouldReturnCanRestore()
        {
            // Arrange
            var backupOptions = new BackupOptions
            {
                BackupPath = Path.Combine(_testDirectory, "test_restore_backup.bak"),
                Type = BackupType.Full
            };
            var backupResult = await _backupRestoreService.CreateFullBackupAsync(backupOptions);
            backupResult.Success.Should().BeTrue();

            // Act
            var result = await _backupRestoreService.TestRestoreAsync(backupResult.BackupPath);

            // Assert
            result.Should().NotBeNull();
            result.CanRestore.Should().BeTrue();
            result.Issues.Should().BeEmpty();
            result.EstimatedRestoreTime.Should().BeGreaterThan(TimeSpan.Zero);
        }

        [Fact]
        public async Task CompressBackupAsync_WithValidPath_ShouldReturnCompressedPath()
        {
            // Arrange
            var testDir = Path.Combine(_testDirectory, "compress_test");
            Directory.CreateDirectory(testDir);
            await File.WriteAllTextAsync(Path.Combine(testDir, "test_file.txt"), "Test content");

            // Act
            var result = await _backupRestoreService.CompressBackupAsync(testDir, CompressionLevel.Optimal);

            // Assert
            result.Should().NotBeNullOrEmpty();
            result.Should().EndWith(".zip");
            File.Exists(result).Should().BeTrue();
            Directory.Exists(testDir).Should().BeFalse(); // المجلد الأصلي يجب أن يُحذف
        }

        [Fact]
        public async Task DecompressBackupAsync_WithValidCompressedFile_ShouldReturnExtractedPath()
        {
            // Arrange
            // إنشاء مجلد وضغطه
            var testDir = Path.Combine(_testDirectory, "decompress_test");
            Directory.CreateDirectory(testDir);
            await File.WriteAllTextAsync(Path.Combine(testDir, "test_file.txt"), "Test content");
            
            var compressedPath = await _backupRestoreService.CompressBackupAsync(testDir, CompressionLevel.Optimal);

            // Act
            var result = await _backupRestoreService.DecompressBackupAsync(compressedPath);

            // Assert
            result.Should().NotBeNullOrEmpty();
            Directory.Exists(result).Should().BeTrue();
            File.Exists(Path.Combine(result, "test_file.txt")).Should().BeTrue();
        }

        [Fact]
        public async Task EncryptBackupAsync_WithValidFile_ShouldReturnEncryptedPath()
        {
            // Arrange
            var testFilePath = Path.Combine(_testDirectory, "encrypt_test.bak");
            await File.WriteAllTextAsync(testFilePath, "Test backup content");

            // Act
            var result = await _backupRestoreService.EncryptBackupAsync(testFilePath, "test_password");

            // Assert
            result.Should().NotBeNullOrEmpty();
            result.Should().EndWith(".enc");
            File.Exists(result).Should().BeTrue();
            File.Exists(testFilePath).Should().BeFalse(); // الملف الأصلي يجب أن يُحذف
            
            // التحقق من استدعاء التشفير
            _mockEncryptionService.Verify(x => x.EncryptAsync(It.IsAny<string>(), "test_password"), Times.Once);
        }

        [Fact]
        public async Task DecryptBackupAsync_WithValidEncryptedFile_ShouldReturnDecryptedPath()
        {
            // Arrange
            var testFilePath = Path.Combine(_testDirectory, "decrypt_test.bak");
            await File.WriteAllTextAsync(testFilePath, "Test backup content");
            
            var encryptedPath = await _backupRestoreService.EncryptBackupAsync(testFilePath, "test_password");

            // Act
            var result = await _backupRestoreService.DecryptBackupAsync(encryptedPath, "test_password");

            // Assert
            result.Should().NotBeNullOrEmpty();
            result.Should().NotEndWith(".enc");
            File.Exists(result).Should().BeTrue();
            
            // التحقق من استدعاء فك التشفير
            _mockEncryptionService.Verify(x => x.DecryptAsync(It.IsAny<string>(), "test_password"), Times.Once);
        }

        [Theory]
        [InlineData(BackupType.Full)]
        [InlineData(BackupType.Incremental)]
        [InlineData(BackupType.Differential)]
        public async Task CreateBackupAsync_WithDifferentTypes_ShouldSucceed(BackupType backupType)
        {
            // Arrange
            var options = new BackupOptions
            {
                BackupPath = Path.Combine(_testDirectory, $"{backupType}_test.bak"),
                Type = backupType,
                CompressBackup = false
            };

            // Act
            BackupResult result = backupType switch
            {
                BackupType.Full => await _backupRestoreService.CreateFullBackupAsync(options),
                BackupType.Incremental => await _backupRestoreService.CreateIncrementalBackupAsync(options),
                BackupType.Differential => await _backupRestoreService.CreateDifferentialBackupAsync(options),
                _ => throw new ArgumentException("نوع النسخة الاحتياطية غير مدعوم")
            };

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Type.Should().Be(backupType);
            File.Exists(result.BackupPath).Should().BeTrue();
        }

        public void Dispose()
        {
            _context?.Dispose();
            
            // تنظيف مجلد الاختبار
            if (Directory.Exists(_testDirectory))
            {
                try
                {
                    Directory.Delete(_testDirectory, true);
                }
                catch
                {
                    // تجاهل أخطاء التنظيف
                }
            }
        }
    }
}
