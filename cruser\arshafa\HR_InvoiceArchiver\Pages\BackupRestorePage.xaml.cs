using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    public partial class BackupRestorePage : UserControl, INavigationAware
    {
        private readonly IBackupRestoreService _backupRestoreService;
        private readonly IToastService _toastService;
        private readonly ILoggingService _loggingService;
        private bool _isProcessing = false;

        public BackupRestorePage()
        {
            InitializeComponent();
            
            // الحصول على الخدمات من DI Container
            var serviceProvider = App.ServiceProvider;
            _backupRestoreService = serviceProvider.GetRequiredService<IBackupRestoreService>();
            _toastService = serviceProvider.GetRequiredService<IToastService>();
            _loggingService = serviceProvider.GetRequiredService<ILoggingService>();

            // ربط أحداث التقدم
            _backupRestoreService.ProgressChanged += OnProgressChanged;

            InitializeDefaults();
        }

        public async void OnNavigatedTo(object parameter)
        {
            await _loggingService.LogInformationAsync("تم فتح صفحة النسخ الاحتياطي والاستعادة");
            await RefreshBackupsList();
        }

        public void OnNavigatedFrom()
        {
            // إلغاء ربط الأحداث
            _backupRestoreService.ProgressChanged -= OnProgressChanged;
        }

        private void InitializeDefaults()
        {
            // تعيين القيم الافتراضية
            BackupTypeComboBox.SelectedIndex = 0; // Full
            CompressionLevelComboBox.SelectedIndex = 2; // Optimal
            RestoreModeComboBox.SelectedIndex = 0; // Complete
        }

        private void OnProgressChanged(object? sender, BackupProgressEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                BackupProgressBar.Value = e.PercentageComplete;
                ProgressTextBlock.Text = e.CurrentOperation;
                
                if (e.PercentageComplete > 0)
                {
                    ProgressCard.Visibility = Visibility.Visible;
                }
                
                if (e.PercentageComplete >= 100)
                {
                    ProgressCard.Visibility = Visibility.Collapsed;
                }
            });
        }

        private void SetProcessingState(bool isProcessing, string message = "جاري المعالجة...")
        {
            _isProcessing = isProcessing;
            LoadingGrid.Visibility = isProcessing ? Visibility.Visible : Visibility.Collapsed;
            LoadingTextBlock.Text = message;
            
            // تعطيل/تفعيل الأزرار
            var buttons = new[] 
            { 
                CreateFullBackupButton, CreateIncrementalBackupButton, CreateDifferentialBackupButton,
                RestoreButton, TestRestoreButton, ValidateBackupButton, CleanupButton
            };

            foreach (var button in buttons)
            {
                button.IsEnabled = !isProcessing;
            }
        }

        #region Backup Events

        private void BrowseBackupPathButton_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "اختيار مسار النسخة الاحتياطية",
                Filter = "ملفات النسخ الاحتياطي (*.bak)|*.bak|ملفات مضغوطة (*.zip)|*.zip|جميع الملفات (*.*)|*.*",
                DefaultExt = "bak"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                BackupPathTextBox.Text = saveFileDialog.FileName;
            }
        }

        private async void CreateFullBackupButton_Click(object sender, RoutedEventArgs e)
        {
            await CreateBackupAsync(BackupType.Full);
        }

        private async void CreateIncrementalBackupButton_Click(object sender, RoutedEventArgs e)
        {
            await CreateBackupAsync(BackupType.Incremental);
        }

        private async void CreateDifferentialBackupButton_Click(object sender, RoutedEventArgs e)
        {
            await CreateBackupAsync(BackupType.Differential);
        }

        private async Task CreateBackupAsync(BackupType backupType)
        {
            if (_isProcessing) return;

            try
            {
                SetProcessingState(true, $"جاري إنشاء النسخة الاحتياطية {GetBackupTypeText(backupType)}...");

                var options = CreateBackupOptions(backupType);
                
                BackupResult result = backupType switch
                {
                    BackupType.Full => await _backupRestoreService.CreateFullBackupAsync(options),
                    BackupType.Incremental => await _backupRestoreService.CreateIncrementalBackupAsync(options),
                    BackupType.Differential => await _backupRestoreService.CreateDifferentialBackupAsync(options),
                    _ => throw new ArgumentException("نوع النسخة الاحتياطية غير مدعوم")
                };

                if (result.Success)
                {
                    var sizeText = FormatFileSize(result.BackupSizeBytes);
                    var durationText = result.Duration.ToString(@"mm\:ss");
                    
                    _toastService.ShowSuccess("تم إنشاء النسخة الاحتياطية", 
                        $"تم إنشاء النسخة الاحتياطية بنجاح\n" +
                        $"الحجم: {sizeText}\n" +
                        $"المدة: {durationText}\n" +
                        $"المسار: {result.BackupPath}");
                    
                    // تحديث قائمة النسخ الاحتياطية
                    await RefreshBackupsList();

                    // عرض خيار فتح المجلد
                    var openFolder = MessageBox.Show(
                        "تم إنشاء النسخة الاحتياطية بنجاح. هل تريد فتح مجلد الملف؟",
                        "تم إنشاء النسخة الاحتياطية",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (openFolder == MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{result.BackupPath}\"");
                    }
                }
                else
                {
                    _toastService.ShowError("فشل في إنشاء النسخة الاحتياطية", result.ErrorMessage ?? "حدث خطأ غير معروف");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء إنشاء النسخة الاحتياطية");
                await _loggingService.LogErrorAsync("فشل في إنشاء النسخة الاحتياطية", ex);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        #endregion

        #region Restore Events

        private async void RefreshBackupsButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshBackupsList();
        }

        private async void TestRestoreButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedBackup = GetSelectedBackup();
            if (selectedBackup == null)
            {
                _toastService.ShowWarning("تحذير", "يرجى اختيار نسخة احتياطية للاختبار");
                return;
            }

            if (_isProcessing) return;

            try
            {
                SetProcessingState(true, "جاري اختبار الاستعادة...");

                var testResult = await _backupRestoreService.TestRestoreAsync(selectedBackup.Path);

                if (testResult.CanRestore)
                {
                    var estimatedTime = testResult.EstimatedRestoreTime.ToString(@"mm\:ss");
                    _toastService.ShowSuccess("اختبار الاستعادة", 
                        $"يمكن استعادة النسخة الاحتياطية بنجاح\n" +
                        $"الوقت المقدر: {estimatedTime}");
                }
                else
                {
                    var issues = string.Join("\n", testResult.Issues);
                    _toastService.ShowError("فشل اختبار الاستعادة", 
                        $"لا يمكن استعادة النسخة الاحتياطية\n" +
                        $"المشاكل:\n{issues}");
                }

                if (testResult.Warnings.Any())
                {
                    var warnings = string.Join("\n", testResult.Warnings);
                    _toastService.ShowWarning("تحذيرات", $"تحذيرات:\n{warnings}");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء اختبار الاستعادة");
                await _loggingService.LogErrorAsync("فشل في اختبار الاستعادة", ex);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        private async void ValidateBackupButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedBackup = GetSelectedBackup();
            if (selectedBackup == null)
            {
                _toastService.ShowWarning("تحذير", "يرجى اختيار نسخة احتياطية للتحقق منها");
                return;
            }

            if (_isProcessing) return;

            try
            {
                SetProcessingState(true, "جاري التحقق من النسخة الاحتياطية...");

                var validation = await _backupRestoreService.ValidateBackupAsync(selectedBackup.Path);

                if (validation.IsValid)
                {
                    _toastService.ShowSuccess("التحقق من النسخة الاحتياطية", "النسخة الاحتياطية صحيحة وسليمة");
                }
                else
                {
                    var errors = string.Join("\n", validation.Errors);
                    _toastService.ShowError("النسخة الاحتياطية غير صحيحة", $"الأخطاء المكتشفة:\n{errors}");
                }

                if (validation.Warnings.Any())
                {
                    var warnings = string.Join("\n", validation.Warnings);
                    _toastService.ShowWarning("تحذيرات", $"تحذيرات:\n{warnings}");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء التحقق من النسخة الاحتياطية");
                await _loggingService.LogErrorAsync("فشل في التحقق من النسخة الاحتياطية", ex);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        private async void RestoreButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedBackup = GetSelectedBackup();
            if (selectedBackup == null)
            {
                _toastService.ShowWarning("تحذير", "يرجى اختيار نسخة احتياطية للاستعادة");
                return;
            }

            var result = MessageBox.Show(
                "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n" +
                "سيتم استبدال البيانات الحالية بالبيانات من النسخة الاحتياطية.\n" +
                "هذه العملية لا يمكن التراجع عنها.",
                "تأكيد الاستعادة",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result != MessageBoxResult.Yes) return;

            if (_isProcessing) return;

            try
            {
                SetProcessingState(true, "جاري استعادة النسخة الاحتياطية...");

                var options = CreateRestoreOptions(selectedBackup);
                var restoreResult = await _backupRestoreService.RestoreFromBackupAsync(options);

                if (restoreResult.Success)
                {
                    var durationText = restoreResult.Duration.ToString(@"mm\:ss");
                    var message = $"تمت الاستعادة بنجاح!\n" +
                                 $"الجداول المستعادة: {restoreResult.TablesRestored}\n" +
                                 $"السجلات المستعادة: {restoreResult.RecordsRestored}\n" +
                                 $"المدة: {durationText}";

                    if (!string.IsNullOrEmpty(restoreResult.BackupCreatedBeforeRestore))
                    {
                        message += $"\nالنسخة الاحتياطية قبل الاستعادة: {restoreResult.BackupCreatedBeforeRestore}";
                    }

                    _toastService.ShowSuccess("تمت الاستعادة", message);

                    // إعادة تشغيل التطبيق قد تكون مطلوبة
                    var restart = MessageBox.Show(
                        "تمت الاستعادة بنجاح. يُنصح بإعادة تشغيل التطبيق لضمان عمل جميع الميزات بشكل صحيح.\n" +
                        "هل تريد إعادة تشغيل التطبيق الآن؟",
                        "إعادة التشغيل",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (restart == MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Process.Start(Environment.ProcessPath ?? "");
                        Application.Current.Shutdown();
                    }
                }
                else
                {
                    var errorMessage = restoreResult.ErrorMessage ?? "حدث خطأ غير معروف";
                    if (restoreResult.Warnings.Any())
                    {
                        errorMessage += $"\nتحذيرات: {string.Join(", ", restoreResult.Warnings)}";
                    }
                    
                    _toastService.ShowError("فشل الاستعادة", errorMessage);
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء الاستعادة");
                await _loggingService.LogErrorAsync("فشل في الاستعادة", ex);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        #endregion

        #region Management Events

        private async void CleanupButton_Click(object sender, RoutedEventArgs e)
        {
            if (!int.TryParse(CleanupDaysTextBox.Text, out var daysToKeep) || daysToKeep <= 0)
            {
                _toastService.ShowWarning("تحذير", "يرجى إدخال عدد صحيح من الأيام");
                return;
            }

            var result = MessageBox.Show(
                $"هل تريد حذف جميع النسخ الاحتياطية الأقدم من {daysToKeep} يوم؟",
                "تأكيد التنظيف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes) return;

            try
            {
                SetProcessingState(true, "جاري تنظيف النسخ الاحتياطية القديمة...");
                var deletedCount = await _backupRestoreService.CleanupOldBackupsAsync(daysToKeep);
                _toastService.ShowSuccess("تم التنظيف", $"تم حذف {deletedCount} نسخة احتياطية قديمة");
                await RefreshBackupsList();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء تنظيف النسخ الاحتياطية");
                await _loggingService.LogErrorAsync("فشل في تنظيف النسخ الاحتياطية", ex);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        private async void StatisticsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var statistics = await _backupRestoreService.GetBackupStatisticsAsync();

                var message = $"إحصائيات النسخ الاحتياطي:\n\n" +
                             $"إجمالي النسخ: {statistics.TotalBackups}\n" +
                             $"الحجم الإجمالي: {FormatFileSize(statistics.TotalBackupSizeBytes)}\n" +
                             $"النسخ الناجحة: {statistics.SuccessfulBackups}\n" +
                             $"النسخ الفاشلة: {statistics.FailedBackups}";

                MessageBox.Show(message, "إحصائيات النسخ الاحتياطي", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء الحصول على الإحصائيات");
                await _loggingService.LogErrorAsync("فشل في الحصول على إحصائيات النسخ الاحتياطي", ex);
            }
        }

        private async void ScheduleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var schedule = new BackupSchedule
                {
                    IsEnabled = true,
                    Frequency = BackupFrequency.Daily,
                    ScheduledTime = new TimeSpan(2, 0, 0),
                    BackupType = BackupType.Full,
                    RetentionDays = 30,
                    CompressBackups = true
                };

                await _backupRestoreService.ScheduleAutomaticBackupAsync(schedule);
                _toastService.ShowSuccess("تم تفعيل الجدولة", "تم تفعيل النسخ الاحتياطي التلقائي");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "حدث خطأ أثناء جدولة النسخ الاحتياطي");
                await _loggingService.LogErrorAsync("فشل في جدولة النسخ الاحتياطي", ex);
            }
        }

        #endregion

        #region Helper Methods

        private BackupOptions CreateBackupOptions(BackupType backupType)
        {
            return new BackupOptions
            {
                BackupPath = BackupPathTextBox.Text,
                Type = backupType,
                IncludeAttachments = IncludeAttachmentsCheckBox.IsChecked ?? true,
                IncludeSettings = IncludeSettingsCheckBox.IsChecked ?? true,
                IncludeLogs = IncludeLogsCheckBox.IsChecked ?? false,
                CompressBackup = CompressBackupCheckBox.IsChecked ?? true,
                EncryptBackup = EncryptBackupCheckBox.IsChecked ?? false,
                Password = EncryptBackupCheckBox.IsChecked == true ? BackupPasswordBox.Password : null,
                CompressionLevel = GetSelectedCompressionLevel(),
                Description = BackupDescriptionTextBox.Text
            };
        }

        private RestoreOptions CreateRestoreOptions(BackupInfo backup)
        {
            return new RestoreOptions
            {
                BackupPath = backup.Path,
                Mode = GetSelectedRestoreMode(),
                RestoreData = true,
                RestoreSettings = true,
                RestoreAttachments = true,
                CreateBackupBeforeRestore = CreateBackupBeforeRestoreCheckBox.IsChecked ?? true,
                Password = !string.IsNullOrEmpty(RestorePasswordBox.Password) ? RestorePasswordBox.Password : null,
                VerifyIntegrity = VerifyIntegrityCheckBox.IsChecked ?? true
            };
        }

        private CompressionLevel GetSelectedCompressionLevel()
        {
            var selectedItem = CompressionLevelComboBox.SelectedItem as ComboBoxItem;
            var tag = selectedItem?.Tag?.ToString();

            return tag switch
            {
                "None" => CompressionLevel.None,
                "Fastest" => CompressionLevel.Fastest,
                "Optimal" => CompressionLevel.Optimal,
                "Maximum" => CompressionLevel.Maximum,
                _ => CompressionLevel.Optimal
            };
        }

        private RestoreMode GetSelectedRestoreMode()
        {
            var selectedItem = RestoreModeComboBox.SelectedItem as ComboBoxItem;
            var tag = selectedItem?.Tag?.ToString();

            return tag switch
            {
                "Complete" => RestoreMode.Complete,
                "DataOnly" => RestoreMode.DataOnly,
                "SettingsOnly" => RestoreMode.SettingsOnly,
                _ => RestoreMode.Complete
            };
        }

        private BackupInfo? GetSelectedBackup()
        {
            return BackupsDataGrid.SelectedItem as BackupInfo;
        }

        private async Task RefreshBackupsList()
        {
            try
            {
                var backups = await _backupRestoreService.GetAvailableBackupsAsync();
                BackupsDataGrid.ItemsSource = backups;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في تحديث قائمة النسخ الاحتياطية", ex);
            }
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";

            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }

            return $"{size:0.##} {sizes[order]}";
        }

        #endregion

        #region Helper Methods

        private string GetBackupTypeText(BackupType type)
        {
            return type switch
            {
                BackupType.Full => "كاملة",
                BackupType.Incremental => "تزايدية",
                BackupType.Differential => "تفاضلية",
                _ => "غير محدد"
            };
        }

        #endregion
    }
}
