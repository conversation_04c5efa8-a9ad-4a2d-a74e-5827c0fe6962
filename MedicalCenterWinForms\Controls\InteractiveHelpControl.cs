using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Collections.Generic;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class InteractiveHelpControl : BaseUserControl
    {
        private HelpService _helpService;

        // UI Components
        private Panel mainPanel;
        private Panel searchPanel;
        private Panel contentPanel;
        private Panel navigationPanel;

        // Search Components
        private TextBox txtSearch;
        private Button btnSearch;
        private ComboBox cmbCategory;

        // Navigation Components
        private TreeView tvHelpTopics;
        private Button btnBack;
        private Button btnForward;
        private Button btnHome;
        private Button btnPrint;

        // Content Components
        private WebBrowser webContent;
        private Panel videoPanel;
        private Button btnPlayVideo;
        private Label lblVideoTitle;

        // Help Data
        private List<HelpTopic> _helpTopics;
        private Stack<HelpTopic> _navigationHistory;
        private int _currentHistoryIndex;

        public InteractiveHelpControl() : base()
        {
            InitializeComponent();
            InitializeServices();
            LoadHelpContent();
        }

        public InteractiveHelpControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            InitializeServices();
            LoadHelpContent();
        }

        private void InitializeServices()
        {
            _helpService = new HelpService();
            _navigationHistory = new Stack<HelpTopic>();
            _currentHistoryIndex = -1;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Set control properties
            this.Size = new Size(1200, 800);
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);
            this.RightToLeft = RightToLeft.Yes;

            CreateMainPanel();
            CreateSearchPanel();
            CreateNavigationPanel();
            CreateContentPanel();

            this.ResumeLayout(false);
        }

        private void CreateMainPanel()
        {
            mainPanel = ModernMedicalTheme.Components.CreateAdvancedCard(
                "📚 دليل المستخدم التفاعلي", true);
            mainPanel.Size = new Size(1180, 780);
            mainPanel.Location = new Point(10, 10);
            this.Controls.Add(mainPanel);
        }

        private void CreateSearchPanel()
        {
            searchPanel = new Panel
            {
                Location = new Point(20, 60),
                Size = new Size(1140, 60),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Search Box
            var lblSearch = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 20),
                Size = new Size(50, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            txtSearch = ModernMedicalTheme.Components.CreateAdvancedTextBox("ابحث في دليل المستخدم...");
            txtSearch.Location = new Point(70, 18);
            txtSearch.Size = new Size(300, 25);
            txtSearch.KeyDown += TxtSearch_KeyDown;

            btnSearch = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔍", ModernMedicalTheme.Components.ButtonStyle.Primary);
            btnSearch.Location = new Point(380, 18);
            btnSearch.Size = new Size(40, 25);
            btnSearch.Click += BtnSearch_Click;

            // Category Filter
            var lblCategory = new Label
            {
                Text = "الفئة:",
                Location = new Point(440, 20),
                Size = new Size(50, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            cmbCategory = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbCategory.Location = new Point(500, 18);
            cmbCategory.Size = new Size(150, 25);
            cmbCategory.Items.AddRange(new object[] 
            { 
                "جميع الفئات", "البداية السريعة", "إدارة المرضى", "إدارة الأطباء", 
                "المدفوعات", "التقارير", "الإعدادات", "النسخ الاحتياطي", "استكشاف الأخطاء" 
            });
            cmbCategory.SelectedIndex = 0;
            cmbCategory.SelectedIndexChanged += CmbCategory_SelectedIndexChanged;

            // Navigation Buttons
            btnHome = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🏠", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnHome.Location = new Point(670, 18);
            btnHome.Size = new Size(40, 25);
            btnHome.Click += BtnHome_Click;

            btnBack = ModernMedicalTheme.Components.CreateAdvancedButton(
                "◀", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnBack.Location = new Point(720, 18);
            btnBack.Size = new Size(40, 25);
            btnBack.Click += BtnBack_Click;
            btnBack.Enabled = false;

            btnForward = ModernMedicalTheme.Components.CreateAdvancedButton(
                "▶", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnForward.Location = new Point(770, 18);
            btnForward.Size = new Size(40, 25);
            btnForward.Click += BtnForward_Click;
            btnForward.Enabled = false;

            btnPrint = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🖨️", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnPrint.Location = new Point(820, 18);
            btnPrint.Size = new Size(40, 25);
            btnPrint.Click += BtnPrint_Click;

            searchPanel.Controls.AddRange(new Control[]
            {
                lblSearch, txtSearch, btnSearch, lblCategory, cmbCategory,
                btnHome, btnBack, btnForward, btnPrint
            });

            mainPanel.Controls.Add(searchPanel);
        }

        private void CreateNavigationPanel()
        {
            navigationPanel = new Panel
            {
                Location = new Point(20, 130),
                Size = new Size(300, 630),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblTopics = new Label
            {
                Text = "المواضيع",
                Location = new Point(10, 10),
                Size = new Size(100, 25),
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Primary
            };

            tvHelpTopics = new TreeView
            {
                Location = new Point(10, 40),
                Size = new Size(280, 580),
                Font = ArabicFontHelper.GetArabicFont(9F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                BorderStyle = BorderStyle.None,
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true,
                FullRowSelect = true,
                HideSelection = false
            };

            tvHelpTopics.AfterSelect += TvHelpTopics_AfterSelect;

            navigationPanel.Controls.AddRange(new Control[] { lblTopics, tvHelpTopics });
            mainPanel.Controls.Add(navigationPanel);
        }

        private void CreateContentPanel()
        {
            contentPanel = new Panel
            {
                Location = new Point(330, 130),
                Size = new Size(830, 630),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Web Browser for HTML content
            webContent = new WebBrowser
            {
                Location = new Point(10, 10),
                Size = new Size(810, 610),
                ScriptErrorsSuppressed = true,
                WebBrowserShortcutsEnabled = false,
                IsWebBrowserContextMenuEnabled = false
            };

            webContent.Navigating += WebContent_Navigating;

            contentPanel.Controls.Add(webContent);
            mainPanel.Controls.Add(contentPanel);
        }

        private void LoadHelpContent()
        {
            try
            {
                _helpTopics = _helpService.GetAllHelpTopics();
                PopulateHelpTree();
                LoadHomePage();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل محتوى المساعدة: {ex.Message}");
            }
        }

        private void PopulateHelpTree()
        {
            tvHelpTopics.Nodes.Clear();

            var categories = _helpTopics.GroupBy(t => t.Category).OrderBy(g => g.Key);

            foreach (var category in categories)
            {
                var categoryNode = new TreeNode(category.Key)
                {
                    Tag = null,
                    ImageIndex = 0,
                    SelectedImageIndex = 0
                };

                foreach (var topic in category.OrderBy(t => t.Order))
                {
                    var topicNode = new TreeNode(topic.Title)
                    {
                        Tag = topic,
                        ImageIndex = 1,
                        SelectedImageIndex = 1
                    };

                    // Add subtopics if any
                    foreach (var subtopic in topic.Subtopics.OrderBy(s => s.Order))
                    {
                        var subtopicNode = new TreeNode(subtopic.Title)
                        {
                            Tag = subtopic,
                            ImageIndex = 2,
                            SelectedImageIndex = 2
                        };
                        topicNode.Nodes.Add(subtopicNode);
                    }

                    categoryNode.Nodes.Add(topicNode);
                }

                tvHelpTopics.Nodes.Add(categoryNode);
            }

            tvHelpTopics.ExpandAll();
        }

        private void LoadHomePage()
        {
            var homeContent = _helpService.GetHomePageContent();
            DisplayContent(homeContent);
        }

        private void DisplayContent(string htmlContent)
        {
            try
            {
                webContent.DocumentText = htmlContent;
            }
            catch (Exception ex)
            {
                webContent.DocumentText = $"<html><body><h2>خطأ في عرض المحتوى</h2><p>{ex.Message}</p></body></html>";
            }
        }

        private void DisplayTopic(HelpTopic topic)
        {
            if (topic == null) return;

            try
            {
                var content = _helpService.GetTopicContent(topic);
                DisplayContent(content);
                
                // Add to navigation history
                _navigationHistory.Push(topic);
                UpdateNavigationButtons();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في عرض الموضوع: {ex.Message}");
            }
        }

        private void UpdateNavigationButtons()
        {
            btnBack.Enabled = _navigationHistory.Count > 1;
            // btnForward logic would need a separate forward stack
        }

        private void SearchTopics(string searchTerm, string category = "")
        {
            try
            {
                var results = _helpService.SearchTopics(searchTerm, category);
                
                if (results.Any())
                {
                    var searchResultsHtml = _helpService.GenerateSearchResultsHtml(results, searchTerm);
                    DisplayContent(searchResultsHtml);
                }
                else
                {
                    var noResultsHtml = $@"
                        <html>
                        <body style='font-family: Arial; direction: rtl; text-align: right;'>
                            <h2>لا توجد نتائج</h2>
                            <p>لم يتم العثور على نتائج للبحث عن: <strong>{searchTerm}</strong></p>
                            <p>جرب:</p>
                            <ul>
                                <li>استخدام كلمات مختلفة</li>
                                <li>التحقق من الإملاء</li>
                                <li>استخدام مصطلحات أكثر عمومية</li>
                            </ul>
                        </body>
                        </html>";
                    DisplayContent(noResultsHtml);
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في البحث: {ex.Message}");
            }
        }

        // Event Handlers
        private void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                BtnSearch_Click(sender, e);
            }
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            var searchTerm = txtSearch.Text.Trim();
            if (string.IsNullOrEmpty(searchTerm))
            {
                ShowWarning("يرجى إدخال كلمة للبحث");
                return;
            }

            var category = cmbCategory.SelectedIndex > 0 ? cmbCategory.SelectedItem.ToString() : "";
            SearchTopics(searchTerm, category);
        }

        private void CmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            // Filter topics by category
            if (cmbCategory.SelectedIndex > 0)
            {
                var selectedCategory = cmbCategory.SelectedItem.ToString();
                FilterTopicsByCategory(selectedCategory);
            }
            else
            {
                PopulateHelpTree();
            }
        }

        private void FilterTopicsByCategory(string category)
        {
            tvHelpTopics.Nodes.Clear();

            var filteredTopics = _helpTopics.Where(t => t.Category == category);
            
            if (filteredTopics.Any())
            {
                var categoryNode = new TreeNode(category);
                
                foreach (var topic in filteredTopics.OrderBy(t => t.Order))
                {
                    var topicNode = new TreeNode(topic.Title)
                    {
                        Tag = topic
                    };
                    categoryNode.Nodes.Add(topicNode);
                }
                
                tvHelpTopics.Nodes.Add(categoryNode);
                tvHelpTopics.ExpandAll();
            }
        }

        private void TvHelpTopics_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node?.Tag is HelpTopic topic)
            {
                DisplayTopic(topic);
            }
        }

        private void BtnHome_Click(object sender, EventArgs e)
        {
            LoadHomePage();
            _navigationHistory.Clear();
            UpdateNavigationButtons();
        }

        private void BtnBack_Click(object sender, EventArgs e)
        {
            if (_navigationHistory.Count > 1)
            {
                _navigationHistory.Pop(); // Remove current
                var previousTopic = _navigationHistory.Peek();
                DisplayTopic(previousTopic);
            }
        }

        private void BtnForward_Click(object sender, EventArgs e)
        {
            // Implementation would require a forward stack
            MessageBox.Show("ميزة التقدم للأمام ستكون متاحة قريباً", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                webContent.ShowPrintDialog();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في الطباعة: {ex.Message}");
            }
        }

        private void WebContent_Navigating(object sender, WebBrowserNavigatingEventArgs e)
        {
            // Handle internal links
            if (e.Url.Scheme == "help")
            {
                e.Cancel = true;
                var topicId = e.Url.Host;
                var topic = _helpTopics.FirstOrDefault(t => t.Id == topicId);
                if (topic != null)
                {
                    DisplayTopic(topic);
                }
            }
        }
    }
}
