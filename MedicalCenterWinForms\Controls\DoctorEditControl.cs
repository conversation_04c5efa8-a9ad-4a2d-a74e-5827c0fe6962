using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Controls
{
    public partial class DoctorEditControl : BaseUserControl
    {
        private readonly Doctor? _doctor;
        private readonly bool _isEditMode;

        public event EventHandler? DoctorSaved;

        public DoctorEditControl() : base()
        {
            InitializeComponent();
            _isEditMode = false;
            InitializeForm();
        }

        public DoctorEditControl(DatabaseService databaseService, Doctor? doctor = null) : base(databaseService)
        {
            InitializeComponent();
            _doctor = doctor;
            _isEditMode = doctor != null;
            InitializeForm();
        }

        private void InitializeForm()
        {
            if (_isEditMode && _doctor != null)
            {
                txtFullName.Text = _doctor.FullName;
                txtSpecialty.Text = _doctor.Specialty;
                chkIsActive.Checked = _doctor.IsActive;
            }
            else
            {
                chkIsActive.Checked = true;
            }

            txtFullName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                SetLoadingState(true);
                btnSave.Text = "جاري الحفظ...";

                using var context = DatabaseService.GetDbContext();

                if (_isEditMode && _doctor != null)
                {
                    var existingDoctor = await context.Doctors.FindAsync(_doctor.DoctorId);
                    if (existingDoctor != null)
                    {
                        existingDoctor.FullName = txtFullName.Text.Trim();
                        existingDoctor.Specialty = txtSpecialty.Text.Trim();
                        existingDoctor.IsActive = chkIsActive.Checked;
                    }
                }
                else
                {
                    var newDoctor = new Doctor
                    {
                        FullName = txtFullName.Text.Trim(),
                        Specialty = txtSpecialty.Text.Trim(),
                        IsActive = chkIsActive.Checked
                    };

                    context.Doctors.Add(newDoctor);
                }

                await context.SaveChangesAsync();

                ShowSuccess("تم حفظ البيانات بنجاح");
                DoctorSaved?.Invoke(this, EventArgs.Empty);
                
                // Close the parent form
                this.ParentForm?.Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
                btnSave.Text = "حفظ";
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.ParentForm?.Close();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                ShowWarning("يرجى إدخال اسم الطبيب");
                txtFullName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtSpecialty.Text))
            {
                ShowWarning("يرجى إدخال التخصص");
                txtSpecialty.Focus();
                return false;
            }

            return true;
        }

        private void txtFullName_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtSpecialty.Focus();
                e.Handled = true;
            }
        }

        private void txtSpecialty_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnSave_Click(sender, e);
                e.Handled = true;
            }
        }
    }

    public partial class DoctorEditControl
    {
        private TextBox txtFullName;
        private TextBox txtSpecialty;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;
        private Label lblFullName;
        private Label lblSpecialty;
        private Panel pnlMain;
        private Panel pnlButtons;

        private void InitializeComponent()
        {
            this.txtFullName = CreateStyledTextBox();
            this.txtSpecialty = CreateStyledTextBox();
            this.lblFullName = CreateStyledLabel("اسم الطبيب:", true);
            this.lblSpecialty = CreateStyledLabel("التخصص:", true);
            this.pnlMain = CreateStyledPanel();
            this.pnlButtons = CreateStyledPanel();

            this.chkIsActive = new CheckBox
            {
                Text = "نشط",
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold),
                AutoSize = true,
                Checked = true
            };

            this.btnSave = CreateStyledButton("حفظ", Color.FromArgb(46, 204, 113), btnSave_Click);
            this.btnCancel = CreateStyledButton("إلغاء", Color.FromArgb(231, 76, 60), btnCancel_Click);

            this.SuspendLayout();

            // pnlMain
            this.pnlMain.Controls.Add(this.lblFullName);
            this.pnlMain.Controls.Add(this.txtFullName);
            this.pnlMain.Controls.Add(this.lblSpecialty);
            this.pnlMain.Controls.Add(this.txtSpecialty);
            this.pnlMain.Controls.Add(this.chkIsActive);
            this.pnlMain.Dock = DockStyle.Fill;

            // Controls positioning
            this.lblFullName.Location = new Point(40, 40);
            this.txtFullName.Location = new Point(40, 70);
            this.txtFullName.Size = new Size(370, 25);
            this.txtFullName.KeyPress += txtFullName_KeyPress;

            this.lblSpecialty.Location = new Point(40, 120);
            this.txtSpecialty.Location = new Point(40, 150);
            this.txtSpecialty.Size = new Size(370, 25);
            this.txtSpecialty.KeyPress += txtSpecialty_KeyPress;

            this.chkIsActive.Location = new Point(40, 200);

            // pnlButtons
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Height = 60;

            this.btnSave.Location = new Point(220, 10);
            this.btnCancel.Location = new Point(330, 10);

            // DoctorEditControl
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlButtons);
            this.Name = "DoctorEditControl";
            this.Size = new Size(450, 320);

            this.ResumeLayout(false);
        }
    }
}
