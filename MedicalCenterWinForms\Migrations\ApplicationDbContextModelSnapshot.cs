﻿// <auto-generated />
using System;
using MedicalCenterWinForms.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace MedicalCenterWinForms.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("MedicalCenterWinForms.Models.Doctor", b =>
                {
                    b.Property<int>("DoctorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DoctorId"));

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Specialty")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("DoctorId");

                    b.ToTable("Doctors");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.DoctorService", b =>
                {
                    b.Property<int>("DoctorServiceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DoctorServiceId"));

                    b.Property<int>("DoctorId")
                        .HasColumnType("int");

                    b.Property<bool>("HasPercentage")
                        .HasColumnType("bit");

                    b.Property<string>("LinkType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("MedicalServiceId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Percentage")
                        .HasColumnType("decimal(5,2)");

                    b.HasKey("DoctorServiceId");

                    b.HasIndex("DoctorId");

                    b.HasIndex("MedicalServiceId");

                    b.ToTable("DoctorServices");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.MainPayment", b =>
                {
                    b.Property<int>("MainPaymentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MainPaymentId"));

                    b.Property<string>("CashierName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("CenterShare")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ConsultationFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("DoctorShare")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ExamFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PatientVisitId")
                        .HasColumnType("int");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("datetime2");

                    b.HasKey("MainPaymentId");

                    b.HasIndex("PatientVisitId")
                        .IsUnique();

                    b.ToTable("MainPayments");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.MedicalService", b =>
                {
                    b.Property<int>("MedicalServiceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MedicalServiceId"));

                    b.Property<decimal?>("DefaultPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsCenterService")
                        .HasColumnType("bit");

                    b.Property<string>("ServiceName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ServiceType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("MedicalServiceId");

                    b.ToTable("MedicalServices");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.PatientVisit", b =>
                {
                    b.Property<int>("PatientVisitId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PatientVisitId"));

                    b.Property<int>("Age")
                        .HasColumnType("int");

                    b.Property<string>("BookingStaff")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Diagnosis")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("DoctorId")
                        .HasColumnType("int");

                    b.Property<string>("PatientName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Province")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("VisitCountLabel")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("VisitDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("VisitNumber")
                        .HasColumnType("int");

                    b.HasKey("PatientVisitId");

                    b.HasIndex("DoctorId", "VisitDate", "VisitNumber")
                        .IsUnique();

                    b.ToTable("PatientVisits");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.ReferralPayment", b =>
                {
                    b.Property<int>("ReferralPaymentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ReferralPaymentId"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CashierName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("CenterShare")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DoctorShare")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("MedicalServiceId")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PatientVisitId")
                        .HasColumnType("int");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Section")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("ReferralPaymentId");

                    b.HasIndex("MedicalServiceId");

                    b.HasIndex("PatientVisitId");

                    b.ToTable("ReferralPayments");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.UserAccount", b =>
                {
                    b.Property<int>("UserAccountId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserAccountId"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("HashedPassword")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("UserAccountId");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("UserAccounts");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.DoctorService", b =>
                {
                    b.HasOne("MedicalCenterWinForms.Models.Doctor", "Doctor")
                        .WithMany("DoctorServices")
                        .HasForeignKey("DoctorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MedicalCenterWinForms.Models.MedicalService", "MedicalService")
                        .WithMany("DoctorServices")
                        .HasForeignKey("MedicalServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Doctor");

                    b.Navigation("MedicalService");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.MainPayment", b =>
                {
                    b.HasOne("MedicalCenterWinForms.Models.PatientVisit", "PatientVisit")
                        .WithOne("MainPayment")
                        .HasForeignKey("MedicalCenterWinForms.Models.MainPayment", "PatientVisitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PatientVisit");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.PatientVisit", b =>
                {
                    b.HasOne("MedicalCenterWinForms.Models.Doctor", "Doctor")
                        .WithMany("PatientVisits")
                        .HasForeignKey("DoctorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Doctor");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.ReferralPayment", b =>
                {
                    b.HasOne("MedicalCenterWinForms.Models.MedicalService", "MedicalService")
                        .WithMany("ReferralPayments")
                        .HasForeignKey("MedicalServiceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MedicalCenterWinForms.Models.PatientVisit", "PatientVisit")
                        .WithMany("ReferralPayments")
                        .HasForeignKey("PatientVisitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MedicalService");

                    b.Navigation("PatientVisit");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.Doctor", b =>
                {
                    b.Navigation("DoctorServices");

                    b.Navigation("PatientVisits");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.MedicalService", b =>
                {
                    b.Navigation("DoctorServices");

                    b.Navigation("ReferralPayments");
                });

            modelBuilder.Entity("MedicalCenterWinForms.Models.PatientVisit", b =>
                {
                    b.Navigation("MainPayment");

                    b.Navigation("ReferralPayments");
                });
#pragma warning restore 612, 618
        }
    }
}
