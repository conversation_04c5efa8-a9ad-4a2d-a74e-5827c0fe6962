@model IEnumerable<MedicalCenterSystem.Models.MedicalService>

@{
    ViewData["Title"] = "الخدمات الطبية";
}

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2>@ViewData["Title"]</h2>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة خدمة جديدة
            </a>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الخدمة</th>
                                <th>نوع الخدمة</th>
                                <th>خدمة المركز</th>
                                <th>السعر الافتراضي</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr>
                                    <td>@item.MedicalServiceId</td>
                                    <td>@item.ServiceName</td>
                                    <td>
                                        @if (item.ServiceType == "Direct")
                                        {
                                            <span class="badge bg-success">مباشر</span>
                                        }
                                        else if (item.ServiceType == "Referral")
                                        {
                                            <span class="badge bg-info">تحويل</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">@item.ServiceType</span>
                                        }
                                    </td>
                                    <td>
                                        @if (item.IsCenterService)
                                        {
                                            <span class="badge bg-primary">نعم</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">لا</span>
                                        }
                                    </td>
                                    <td>
                                        @if (item.DefaultPrice.HasValue)
                                        {
                                            @item.DefaultPrice.Value.ToString("C")
                                        }
                                        else
                                        {
                                            <span class="text-muted">غير محدد</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a asp-action="Details" asp-route-id="@item.MedicalServiceId" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@item.MedicalServiceId" class="btn btn-warning btn-sm">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.MedicalServiceId" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                @if (!Model.Any())
                {
                    <div class="text-center py-4">
                        <p class="text-muted">لا توجد خدمات طبية مسجلة حالياً</p>
                        <a asp-action="Create" class="btn btn-primary">إضافة أول خدمة</a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
}
