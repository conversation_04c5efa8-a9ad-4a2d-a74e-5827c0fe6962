using MedicalCenterSystem.Data;
using MedicalCenterSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace MedicalCenterSystem.Services
{
    public class PaymentCalculationService
    {
        private readonly ApplicationDbContext _context;

        public PaymentCalculationService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// حساب حصة الطبيب والمركز للمدفوعات الرئيسية
        /// </summary>
        public async Task<(decimal doctorShare, decimal centerShare)> CalculateMainPaymentShares(
            int doctorId, decimal consultationFee, decimal examFee)
        {
            var totalAmount = consultationFee + examFee;
            
            // البحث عن نسبة الطبيب للخدمات المباشرة
            var doctorService = await _context.DoctorServices
                .Include(ds => ds.MedicalService)
                .FirstOrDefaultAsync(ds => ds.DoctorId == doctorId && 
                                         ds.LinkType == "Direct" && 
                                         ds.IsActive &&
                                         ds.MedicalService.ServiceType == "Direct");

            decimal doctorShare = 0;
            
            if (doctorService != null && doctorService.HasPercentage && doctorService.Percentage.HasValue)
            {
                doctorShare = totalAmount * (doctorService.Percentage.Value / 100);
            }
            else
            {
                // نسبة افتراضية 50% إذا لم تكن محددة
                doctorShare = totalAmount * 0.5m;
            }

            var centerShare = totalAmount - doctorShare;
            
            return (doctorShare, centerShare);
        }

        /// <summary>
        /// حساب حصة الطبيب والمركز لمدفوعات التحويلات
        /// </summary>
        public async Task<(decimal doctorShare, decimal centerShare)> CalculateReferralPaymentShares(
            int doctorId, int medicalServiceId, decimal amount)
        {
            var doctorService = await _context.DoctorServices
                .Include(ds => ds.MedicalService)
                .FirstOrDefaultAsync(ds => ds.DoctorId == doctorId && 
                                         ds.MedicalServiceId == medicalServiceId && 
                                         ds.IsActive);

            decimal doctorShare = 0;
            decimal centerShare = amount;

            if (doctorService != null)
            {
                if (doctorService.IsFixedAmount && doctorService.FixedAmount.HasValue)
                {
                    // مبلغ مقطوع
                    doctorShare = doctorService.FixedAmount.Value;
                }
                else if (doctorService.HasPercentage && doctorService.Percentage.HasValue)
                {
                    if (doctorService.ServiceCost.HasValue)
                    {
                        // حصة الطبيب = (مبلغ الخدمة - كلفة الخدمة) * النسبة
                        var netAmount = amount - doctorService.ServiceCost.Value;
                        if (netAmount > 0)
                        {
                            doctorShare = netAmount * (doctorService.Percentage.Value / 100);
                        }
                    }
                    else
                    {
                        // حصة الطبيب = مبلغ الخدمة * النسبة
                        doctorShare = amount * (doctorService.Percentage.Value / 100);
                    }
                }

                centerShare = amount - doctorShare;
            }

            return (doctorShare, centerShare);
        }

        /// <summary>
        /// الحصول على السعر الافتراضي للخدمة للطبيب المحدد
        /// </summary>
        public async Task<decimal?> GetDoctorServiceDefaultPrice(int doctorId, int medicalServiceId)
        {
            var doctorService = await _context.DoctorServices
                .Include(ds => ds.MedicalService)
                .FirstOrDefaultAsync(ds => ds.DoctorId == doctorId && 
                                         ds.MedicalServiceId == medicalServiceId && 
                                         ds.IsActive);

            if (doctorService?.DoctorDefaultPrice.HasValue == true)
            {
                return doctorService.DoctorDefaultPrice.Value;
            }

            // إذا لم يكن هناك سعر محدد للطبيب، استخدم السعر الافتراضي للخدمة
            var medicalService = await _context.MedicalServices
                .FirstOrDefaultAsync(ms => ms.MedicalServiceId == medicalServiceId);

            return medicalService?.DefaultPrice;
        }

        /// <summary>
        /// التحقق من صحة إعدادات الطبيب والخدمة
        /// </summary>
        public async Task<(bool isValid, string errorMessage)> ValidateDoctorServiceConfiguration(
            int doctorId, int medicalServiceId)
        {
            var doctorService = await _context.DoctorServices
                .Include(ds => ds.Doctor)
                .Include(ds => ds.MedicalService)
                .FirstOrDefaultAsync(ds => ds.DoctorId == doctorId && 
                                         ds.MedicalServiceId == medicalServiceId);

            if (doctorService == null)
            {
                return (false, "الطبيب غير مرتبط بهذه الخدمة");
            }

            if (!doctorService.IsActive)
            {
                return (false, "ربط الطبيب بالخدمة غير نشط");
            }

            if (!doctorService.Doctor.IsActive)
            {
                return (false, "الطبيب غير نشط");
            }

            if (doctorService.HasPercentage && !doctorService.Percentage.HasValue)
            {
                return (false, "النسبة غير محددة للطبيب");
            }

            if (doctorService.IsFixedAmount && !doctorService.FixedAmount.HasValue)
            {
                return (false, "المبلغ المقطوع غير محدد للطبيب");
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// الحصول على تفاصيل ربط الطبيب بالخدمة
        /// </summary>
        public async Task<DoctorService?> GetDoctorServiceDetails(int doctorId, int medicalServiceId)
        {
            return await _context.DoctorServices
                .Include(ds => ds.Doctor)
                .Include(ds => ds.MedicalService)
                .FirstOrDefaultAsync(ds => ds.DoctorId == doctorId && 
                                         ds.MedicalServiceId == medicalServiceId && 
                                         ds.IsActive);
        }

        /// <summary>
        /// الحصول على جميع الخدمات المرتبطة بطبيب معين
        /// </summary>
        public async Task<List<DoctorService>> GetDoctorServices(int doctorId)
        {
            return await _context.DoctorServices
                .Include(ds => ds.MedicalService)
                .Where(ds => ds.DoctorId == doctorId && ds.IsActive)
                .OrderBy(ds => ds.MedicalService.ServiceName)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على جميع الأطباء المرتبطين بخدمة معينة
        /// </summary>
        public async Task<List<DoctorService>> GetServiceDoctors(int medicalServiceId)
        {
            return await _context.DoctorServices
                .Include(ds => ds.Doctor)
                .Where(ds => ds.MedicalServiceId == medicalServiceId && ds.IsActive)
                .OrderBy(ds => ds.Doctor.FullName)
                .ToListAsync();
        }
    }
}
