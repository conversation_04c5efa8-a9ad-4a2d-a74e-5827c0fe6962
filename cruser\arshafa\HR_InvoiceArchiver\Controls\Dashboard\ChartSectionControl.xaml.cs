using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using LiveCharts;
using LiveCharts.Configurations;
using LiveCharts.Wpf;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Controls.Dashboard
{
    public partial class ChartSectionControl : UserControl
    {
        private readonly IDashboardService? _dashboardService;
        private bool _isLoading = false;

        public ChartSectionControl()
        {
            InitializeComponent();
            InitializeChart();
        }

        public ChartSectionControl(IDashboardService dashboardService) : this()
        {
            _dashboardService = dashboardService;
        }

        private void InitializeChart()
        {
            try
            {
                // Configure chart for better performance
                var dayConfig = Mappers.Xy<MonthlyTrend>()
                    .X(dayModel => (double)dayModel.PeriodStart.Ticks / TimeSpan.FromDays(1).Ticks)
                    .Y(dayModel => (double)dayModel.TotalAmount);

                Charting.For<MonthlyTrend>(dayConfig);

                // Initialize empty chart
                MonthlyChart.Series = new SeriesCollection();
                
                // Set Y formatter for currency
                YFormatter = value => $"{value:N0} د.ع";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Chart initialization error: {ex.Message}");
            }
        }

        public Func<double, string> YFormatter { get; set; } = value => $"{value:N0} د.ع";

        public async Task LoadChartData()
        {
            if (_isLoading || _dashboardService == null) return;

            try
            {
                _isLoading = true;
                ShowLoadingIndicator(true);

                var selectedPeriod = GetSelectedPeriod();
                var trends = await _dashboardService.GetMonthlyTrendsAsync(selectedPeriod);
                var trendsList = trends.ToList();

                if (!trendsList.Any())
                {
                    ShowNoDataMessage(true);
                    return;
                }

                ShowNoDataMessage(false);
                UpdateChart(trendsList);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading chart data: {ex.Message}");
                ShowNoDataMessage(true);
            }
            finally
            {
                _isLoading = false;
                ShowLoadingIndicator(false);
            }
        }

        private void UpdateChart(List<MonthlyTrend> trends)
        {
            try
            {
                var series = new SeriesCollection();

                if (ShowTotalAmountCheckBox.IsChecked == true)
                {
                    series.Add(new LineSeries
                    {
                        Title = "إجمالي المبلغ",
                        Values = new ChartValues<decimal>(trends.Select(t => t.TotalAmount)),
                        Stroke = System.Windows.Media.Brushes.Blue,
                        Fill = System.Windows.Media.Brushes.Transparent,
                        StrokeThickness = 3,
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8
                    });
                }

                if (ShowPaidAmountCheckBox.IsChecked == true)
                {
                    series.Add(new LineSeries
                    {
                        Title = "المبلغ المسدد",
                        Values = new ChartValues<decimal>(trends.Select(t => t.PaidAmount)),
                        Stroke = System.Windows.Media.Brushes.Green,
                        Fill = System.Windows.Media.Brushes.Transparent,
                        StrokeThickness = 3,
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8
                    });
                }

                MonthlyChart.Series = series;

                // Update X axis labels
                MonthlyChart.AxisX[0].Labels = trends.Select(t => 
                    $"{t.Month} {t.Year}").ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating chart: {ex.Message}");
            }
        }

        private int GetSelectedPeriod()
        {
            return ChartPeriodComboBox.SelectedIndex switch
            {
                0 => 3,  // آخر 3 أشهر
                1 => 6,  // آخر 6 أشهر
                2 => 12, // آخر 12 شهر
                _ => 6
            };
        }

        private void ShowLoadingIndicator(bool show)
        {
            ChartLoadingIndicator.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
        }

        private void ShowNoDataMessage(bool show)
        {
            NoDataPanel.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
        }

        private async void ChartPeriodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            await LoadChartData();
        }

        private async void ChartOptions_Changed(object sender, RoutedEventArgs e)
        {
            await LoadChartData();
        }

        private async void RefreshChartButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadChartData();
        }

        private void ExportChartButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Implement chart export functionality
                MessageBox.Show("ميزة التصدير ستكون متاحة قريباً", "قريباً", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Export error: {ex.Message}");
            }
        }
    }

    // Simplified MonthlyTrend class for chart
    public class MonthlyTrend
    {
        public string Month { get; set; } = string.Empty;
        public int Year { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public DateTime PeriodStart { get; set; }
    }
}
