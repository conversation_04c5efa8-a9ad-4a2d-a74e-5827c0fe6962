@model MedicalCenterSystem.Models.MainPayment

@{
    ViewData["Title"] = "إضافة دفع رئيسي";
}

<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                @if (ViewBag.PatientVisit != null)
                {
                    <div class="alert alert-info">
                        <h6>معلومات المراجع:</h6>
                        <p><strong>المريض:</strong> @ViewBag.PatientVisit.PatientName</p>
                        <p><strong>الطبيب:</strong> @ViewBag.PatientVisit.Doctor.FullName</p>
                        <p><strong>التاريخ:</strong> @ViewBag.PatientVisit.VisitDate.ToString("yyyy-MM-dd")</p>
                        <p><strong>رقم المراجع:</strong> @ViewBag.PatientVisit.VisitNumber</p>
                    </div>
                }

                <form asp-action="Create">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    @if (ViewBag.PatientVisit == null)
                    {
                        <div class="mb-3">
                            <label asp-for="PatientVisitId" class="form-label">المراجع</label>
                            <select asp-for="PatientVisitId" class="form-select" asp-items="ViewBag.PatientVisitId">
                                <option value="">-- اختر المراجع --</option>
                            </select>
                            <span asp-validation-for="PatientVisitId" class="text-danger"></span>
                        </div>
                    }
                    else
                    {
                        <input asp-for="PatientVisitId" type="hidden" />
                    }
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="ConsultationFee" class="form-label"></label>
                                <input asp-for="ConsultationFee" class="form-control" type="number" step="0.01" min="0" placeholder="0.00" />
                                <span asp-validation-for="ConsultationFee" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="ExamFee" class="form-label"></label>
                                <input asp-for="ExamFee" class="form-control" type="number" step="0.01" min="0" placeholder="0.00" />
                                <span asp-validation-for="ExamFee" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="DoctorShare" class="form-label"></label>
                                <input asp-for="DoctorShare" class="form-control" type="number" step="0.01" min="0" placeholder="0.00" />
                                <span asp-validation-for="DoctorShare" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="CenterShare" class="form-label"></label>
                                <input asp-for="CenterShare" class="form-control" type="number" step="0.01" min="0" placeholder="0.00" />
                                <span asp-validation-for="CenterShare" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="CashierName" class="form-label"></label>
                                <input asp-for="CashierName" class="form-control" placeholder="اسم الكاشير" />
                                <span asp-validation-for="CashierName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="PaymentDate" class="form-label"></label>
                                <input asp-for="PaymentDate" class="form-control" type="datetime-local" />
                                <span asp-validation-for="PaymentDate" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Notes" class="form-label"></label>
                        <textarea asp-for="Notes" class="form-control" rows="3" placeholder="ملاحظات (اختياري)"></textarea>
                        <span asp-validation-for="Notes" class="text-danger"></span>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الدفع
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Auto-calculate center share when consultation fee or exam fee changes
        document.addEventListener('DOMContentLoaded', function() {
            const consultationFee = document.getElementById('ConsultationFee');
            const examFee = document.getElementById('ExamFee');
            const doctorShare = document.getElementById('DoctorShare');
            const centerShare = document.getElementById('CenterShare');
            
            function calculateShares() {
                const consultation = parseFloat(consultationFee.value) || 0;
                const exam = parseFloat(examFee.value) || 0;
                const doctor = parseFloat(doctorShare.value) || 0;
                const total = consultation + exam;
                const center = total - doctor;
                
                if (center >= 0) {
                    centerShare.value = center.toFixed(2);
                }
            }
            
            consultationFee.addEventListener('input', calculateShares);
            examFee.addEventListener('input', calculateShares);
            doctorShare.addEventListener('input', calculateShares);
        });
    </script>
}
