<UserControl x:Class="HR_InvoiceArchiver.Controls.PaginationControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <UserControl.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="PaginationButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#495057"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#E3F2FD"/>
                                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="#2196F3"/>
                                <Setter Property="Foreground" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#BBDEFB"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="ButtonBorder" Property="Background" Value="#F8F9FA"/>
                                <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="#E9ECEF"/>
                                <Setter Property="Foreground" Value="#ADB5BD"/>
                                <Setter Property="Cursor" Value="Arrow"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Active Page Button Style -->
        <Style x:Key="ActivePageButtonStyle" TargetType="Button" BasedOn="{StaticResource PaginationButtonStyle}">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
        </Style>

        <!-- Page Size ComboBox Style -->
        <Style x:Key="PageSizeComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
    </UserControl.Resources>

    <Border Background="White" 
            BorderBrush="#E9ECEF" 
            BorderThickness="1" 
            CornerRadius="8" 
            Padding="20">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Page Size Selection -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="عرض" 
                          VerticalAlignment="Center" 
                          Margin="0,0,8,0"
                          FontSize="14"
                          Foreground="#495057"/>
                <ComboBox x:Name="PageSizeComboBox" 
                         Style="{StaticResource PageSizeComboBoxStyle}"
                         SelectionChanged="PageSizeComboBox_SelectionChanged">
                    <ComboBoxItem Content="10" IsSelected="True"/>
                    <ComboBoxItem Content="25"/>
                    <ComboBoxItem Content="50"/>
                    <ComboBoxItem Content="100"/>
                </ComboBox>
                <TextBlock Text="عنصر" 
                          VerticalAlignment="Center" 
                          Margin="8,0,0,0"
                          FontSize="14"
                          Foreground="#495057"/>
            </StackPanel>

            <!-- Pagination Buttons -->
            <StackPanel Grid.Column="1" 
                       Orientation="Horizontal" 
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center">
                
                <!-- First Page -->
                <Button x:Name="FirstPageButton" 
                       Content="⏮️" 
                       Style="{StaticResource PaginationButtonStyle}"
                       Click="FirstPage_Click"
                       ToolTip="الصفحة الأولى"/>

                <!-- Previous Page -->
                <Button x:Name="PreviousPageButton" 
                       Content="◀️" 
                       Style="{StaticResource PaginationButtonStyle}"
                       Click="PreviousPage_Click"
                       ToolTip="الصفحة السابقة"/>

                <!-- Page Numbers -->
                <StackPanel x:Name="PageNumbersPanel" 
                           Orientation="Horizontal"/>

                <!-- Next Page -->
                <Button x:Name="NextPageButton" 
                       Content="▶️" 
                       Style="{StaticResource PaginationButtonStyle}"
                       Click="NextPage_Click"
                       ToolTip="الصفحة التالية"/>

                <!-- Last Page -->
                <Button x:Name="LastPageButton" 
                       Content="⏭️" 
                       Style="{StaticResource PaginationButtonStyle}"
                       Click="LastPage_Click"
                       ToolTip="الصفحة الأخيرة"/>
            </StackPanel>

            <!-- Page Info -->
            <StackPanel Grid.Column="2" 
                       Orientation="Horizontal" 
                       VerticalAlignment="Center">
                <TextBlock x:Name="PageInfoTextBlock" 
                          Text="صفحة 1 من 1 (0 عنصر)"
                          FontSize="14"
                          Foreground="#6C757D"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
