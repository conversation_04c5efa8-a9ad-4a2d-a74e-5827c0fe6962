using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MedicalCenterSystem.Data;
using MedicalCenterSystem.Models;
using MedicalCenterSystem.Services;

namespace MedicalCenterSystem.Controllers
{
    public class DoctorServicesController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly PaymentCalculationService _paymentCalculationService;

        public DoctorServicesController(ApplicationDbContext context, PaymentCalculationService paymentCalculationService)
        {
            _context = context;
            _paymentCalculationService = paymentCalculationService;
        }

        // GET: DoctorServices
        public async Task<IActionResult> Index(int? doctorId, int? serviceId)
        {
            var query = _context.DoctorServices
                .Include(ds => ds.Doctor)
                .Include(ds => ds.MedicalService)
                .AsQueryable();

            if (doctorId.HasValue)
            {
                query = query.Where(ds => ds.DoctorId == doctorId.Value);
            }

            if (serviceId.HasValue)
            {
                query = query.Where(ds => ds.MedicalServiceId == serviceId.Value);
            }

            var doctorServices = await query
                .OrderBy(ds => ds.Doctor.FullName)
                .ThenBy(ds => ds.MedicalService.ServiceName)
                .ToListAsync();

            // Load filter data
            ViewBag.Doctors = new SelectList(
                await _context.Doctors.Where(d => d.IsActive).ToListAsync(),
                "DoctorId", "FullName", doctorId);

            ViewBag.MedicalServices = new SelectList(
                await _context.MedicalServices.ToListAsync(),
                "MedicalServiceId", "ServiceName", serviceId);

            ViewBag.SelectedDoctorId = doctorId;
            ViewBag.SelectedServiceId = serviceId;

            return View(doctorServices);
        }

        // GET: DoctorServices/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var doctorService = await _context.DoctorServices
                .Include(ds => ds.Doctor)
                .Include(ds => ds.MedicalService)
                .FirstOrDefaultAsync(m => m.DoctorServiceId == id);

            if (doctorService == null)
            {
                return NotFound();
            }

            return View(doctorService);
        }

        // GET: DoctorServices/Create
        public async Task<IActionResult> Create(int? doctorId, int? serviceId)
        {
            var model = new DoctorService();
            
            if (doctorId.HasValue)
            {
                model.DoctorId = doctorId.Value;
            }
            
            if (serviceId.HasValue)
            {
                model.MedicalServiceId = serviceId.Value;
            }

            await LoadSelectLists(model);
            return View(model);
        }

        // POST: DoctorServices/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("DoctorServiceId,DoctorId,MedicalServiceId,LinkType,HasPercentage,Percentage,DoctorDefaultPrice,ServiceCost,IsFixedAmount,FixedAmount,IsActive,Notes")] DoctorService doctorService)
        {
            // Check if this combination already exists
            var existingService = await _context.DoctorServices
                .FirstOrDefaultAsync(ds => ds.DoctorId == doctorService.DoctorId && 
                                         ds.MedicalServiceId == doctorService.MedicalServiceId);

            if (existingService != null)
            {
                ModelState.AddModelError("", "هذا الطبيب مرتبط بالفعل بهذه الخدمة");
            }

            // Validate business rules
            if (doctorService.HasPercentage && !doctorService.Percentage.HasValue)
            {
                ModelState.AddModelError("Percentage", "يجب تحديد النسبة عند اختيار 'له نسبة'");
            }

            if (doctorService.IsFixedAmount && !doctorService.FixedAmount.HasValue)
            {
                ModelState.AddModelError("FixedAmount", "يجب تحديد المبلغ المقطوع عند اختيار 'مبلغ مقطوع'");
            }

            if (doctorService.HasPercentage && doctorService.IsFixedAmount)
            {
                ModelState.AddModelError("", "لا يمكن اختيار النسبة والمبلغ المقطوع معاً");
            }

            if (ModelState.IsValid)
            {
                doctorService.CreatedDate = DateTime.Now;
                _context.Add(doctorService);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = "تم إضافة ربط الطبيب بالخدمة بنجاح";
                return RedirectToAction(nameof(Index));
            }

            await LoadSelectLists(doctorService);
            return View(doctorService);
        }

        // GET: DoctorServices/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var doctorService = await _context.DoctorServices.FindAsync(id);
            if (doctorService == null)
            {
                return NotFound();
            }

            await LoadSelectLists(doctorService);
            return View(doctorService);
        }

        // POST: DoctorServices/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("DoctorServiceId,DoctorId,MedicalServiceId,LinkType,HasPercentage,Percentage,DoctorDefaultPrice,ServiceCost,IsFixedAmount,FixedAmount,IsActive,CreatedDate,Notes")] DoctorService doctorService)
        {
            if (id != doctorService.DoctorServiceId)
            {
                return NotFound();
            }

            // Validate business rules
            if (doctorService.HasPercentage && !doctorService.Percentage.HasValue)
            {
                ModelState.AddModelError("Percentage", "يجب تحديد النسبة عند اختيار 'له نسبة'");
            }

            if (doctorService.IsFixedAmount && !doctorService.FixedAmount.HasValue)
            {
                ModelState.AddModelError("FixedAmount", "يجب تحديد المبلغ المقطوع عند اختيار 'مبلغ مقطوع'");
            }

            if (doctorService.HasPercentage && doctorService.IsFixedAmount)
            {
                ModelState.AddModelError("", "لا يمكن اختيار النسبة والمبلغ المقطوع معاً");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(doctorService);
                    await _context.SaveChangesAsync();
                    
                    TempData["SuccessMessage"] = "تم تحديث ربط الطبيب بالخدمة بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!DoctorServiceExists(doctorService.DoctorServiceId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            await LoadSelectLists(doctorService);
            return View(doctorService);
        }

        // GET: DoctorServices/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var doctorService = await _context.DoctorServices
                .Include(ds => ds.Doctor)
                .Include(ds => ds.MedicalService)
                .FirstOrDefaultAsync(m => m.DoctorServiceId == id);

            if (doctorService == null)
            {
                return NotFound();
            }

            return View(doctorService);
        }

        // POST: DoctorServices/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var doctorService = await _context.DoctorServices.FindAsync(id);
            if (doctorService != null)
            {
                _context.DoctorServices.Remove(doctorService);
                await _context.SaveChangesAsync();
                
                TempData["SuccessMessage"] = "تم حذف ربط الطبيب بالخدمة بنجاح";
            }

            return RedirectToAction(nameof(Index));
        }

        // AJAX: Get doctor services for dropdown
        [HttpGet]
        public async Task<JsonResult> GetDoctorServices(int doctorId)
        {
            var services = await _paymentCalculationService.GetDoctorServices(doctorId);
            var result = services.Select(ds => new
            {
                value = ds.MedicalServiceId,
                text = ds.MedicalService.ServiceName,
                defaultPrice = ds.DoctorDefaultPrice ?? ds.MedicalService.DefaultPrice,
                hasPercentage = ds.HasPercentage,
                percentage = ds.Percentage,
                isFixedAmount = ds.IsFixedAmount,
                fixedAmount = ds.FixedAmount
            });

            return Json(result);
        }

        // AJAX: Calculate payment shares
        [HttpPost]
        public async Task<JsonResult> CalculateShares(int doctorId, int medicalServiceId, decimal amount)
        {
            try
            {
                var (doctorShare, centerShare) = await _paymentCalculationService
                    .CalculateReferralPaymentShares(doctorId, medicalServiceId, amount);

                return Json(new
                {
                    success = true,
                    doctorShare = doctorShare,
                    centerShare = centerShare
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    message = ex.Message
                });
            }
        }

        private bool DoctorServiceExists(int id)
        {
            return _context.DoctorServices.Any(e => e.DoctorServiceId == id);
        }

        private async Task LoadSelectLists(DoctorService doctorService)
        {
            ViewData["DoctorId"] = new SelectList(
                await _context.Doctors.Where(d => d.IsActive).ToListAsync(),
                "DoctorId", "FullName", doctorService.DoctorId);

            ViewData["MedicalServiceId"] = new SelectList(
                await _context.MedicalServices.ToListAsync(),
                "MedicalServiceId", "ServiceName", doctorService.MedicalServiceId);

            ViewData["LinkTypes"] = new SelectList(new[]
            {
                new { Value = "Direct", Text = "مباشر" },
                new { Value = "Referral", Text = "تحويل" }
            }, "Value", "Text", doctorService.LinkType);
        }
    }
}
