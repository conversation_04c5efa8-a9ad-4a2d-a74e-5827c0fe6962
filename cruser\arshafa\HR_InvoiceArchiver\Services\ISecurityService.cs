using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// واجهة خدمة الأمان
    /// </summary>
    public interface ISecurityService
    {
        /// <summary>
        /// تسجيل محاولة دخول
        /// </summary>
        Task<LoginResult> LoginAsync(string username, string password);

        /// <summary>
        /// تسجيل خروج
        /// </summary>
        Task LogoutAsync();

        /// <summary>
        /// التحقق من صحة الجلسة
        /// </summary>
        Task<bool> IsSessionValidAsync();

        /// <summary>
        /// تجديد الجلسة
        /// </summary>
        Task<bool> RefreshSessionAsync();

        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        Task<bool> ChangePasswordAsync(string currentPassword, string newPassword);

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// </summary>
        Task<bool> ResetPasswordAsync(string username, string securityAnswer);

        /// <summary>
        /// قفل الحساب
        /// </summary>
        Task LockAccountAsync(string username, TimeSpan lockDuration);

        /// <summary>
        /// إلغاء قفل الحساب
        /// </summary>
        Task UnlockAccountAsync(string username);

        /// <summary>
        /// التحقق من قفل الحساب
        /// </summary>
        Task<bool> IsAccountLockedAsync(string username);

        /// <summary>
        /// تسجيل حدث أمني
        /// </summary>
        Task LogSecurityEventAsync(SecurityEventType eventType, string description, 
            string? userId = null, Dictionary<string, object>? additionalData = null);

        /// <summary>
        /// الحصول على أحداث الأمان الحديثة
        /// </summary>
        Task<List<SecurityEvent>> GetRecentSecurityEventsAsync(int count = 50);

        /// <summary>
        /// التحقق من الصلاحيات
        /// </summary>
        Task<bool> HasPermissionAsync(string permission);

        /// <summary>
        /// الحصول على المستخدم الحالي
        /// </summary>
        Task<SecurityUser?> GetCurrentUserAsync();

        /// <summary>
        /// تشفير البيانات الحساسة
        /// </summary>
        Task<string> EncryptSensitiveDataAsync(string data);

        /// <summary>
        /// فك تشفير البيانات الحساسة
        /// </summary>
        Task<string> DecryptSensitiveDataAsync(string encryptedData);

        /// <summary>
        /// التحقق من سلامة البيانات
        /// </summary>
        Task<bool> VerifyDataIntegrityAsync(string data, string signature);

        /// <summary>
        /// إنشاء توقيع للبيانات
        /// </summary>
        Task<string> CreateDataSignatureAsync(string data);

        /// <summary>
        /// مسح البيانات الحساسة من الذاكرة
        /// </summary>
        Task SecurelyWipeMemoryAsync();
    }

    /// <summary>
    /// نتيجة محاولة تسجيل الدخول
    /// </summary>
    public class LoginResult
    {
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
        public SecurityUser? User { get; set; }
        public string? SessionToken { get; set; }
        public DateTime? SessionExpiry { get; set; }
        public bool RequiresPasswordChange { get; set; }
        public int RemainingAttempts { get; set; }
        public TimeSpan? LockoutDuration { get; set; }
    }

    /// <summary>
    /// مستخدم الأمان
    /// </summary>
    public class SecurityUser
    {
        public string Id { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public List<string> Roles { get; set; } = new();
        public List<string> Permissions { get; set; } = new();
        public DateTime LastLogin { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsLocked { get; set; } = false;
        public DateTime? LockoutEnd { get; set; }
        public int FailedLoginAttempts { get; set; }
    }

    /// <summary>
    /// أنواع الأحداث الأمنية
    /// </summary>
    public enum SecurityEventType
    {
        LoginSuccess = 0,
        LoginFailure = 1,
        Logout = 2,
        PasswordChanged = 3,
        PasswordReset = 4,
        AccountLocked = 5,
        AccountUnlocked = 6,
        PermissionDenied = 7,
        DataAccess = 8,
        DataModification = 9,
        SecurityViolation = 10,
        SessionExpired = 11,
        UnauthorizedAccess = 12
    }

    /// <summary>
    /// حدث أمني
    /// </summary>
    public class SecurityEvent
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public SecurityEventType EventType { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? UserId { get; set; }
        public string? Username { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new();
        public SecurityLevel Level { get; set; }
    }

    /// <summary>
    /// مستويات الأمان
    /// </summary>
    public enum SecurityLevel
    {
        Low = 0,
        Medium = 1,
        High = 2,
        Critical = 3
    }
}
