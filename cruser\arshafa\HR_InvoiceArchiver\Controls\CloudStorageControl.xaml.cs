using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Services;
using MaterialDesignThemes.Wpf;

namespace HR_InvoiceArchiver.Controls
{
    /// <summary>
    /// UserControl للتخزين السحابي
    /// </summary>
    public partial class CloudStorageControl : UserControl
    {
        private ICloudStorageService? _cloudService;
        private IToastService? _toastService;
        private readonly ObservableCollection<SyncedFileInfo> _syncedFiles;

        public CloudStorageControl()
        {
            InitializeComponent();
            _syncedFiles = new ObservableCollection<SyncedFileInfo>();
            SyncedFilesList.ItemsSource = _syncedFiles;
        }

        /// <summary>
        /// تهيئة الخدمات
        /// </summary>
        public void InitializeServices(ICloudStorageService cloudService, IToastService? toastService = null)
        {
            _cloudService = cloudService;
            _toastService = toastService;

            // ربط الأحداث
            if (_cloudService != null)
            {
                _cloudService.ConnectionStatusChanged += OnConnectionStatusChanged;
            }

            // تحديث الحالة الأولية
            _ = UpdateConnectionStatusAsync();
        }

        public CloudStorageControl(ICloudStorageService cloudService, IToastService? toastService = null)
        {
            InitializeComponent();
            _cloudService = cloudService;
            _toastService = toastService;
            _syncedFiles = new ObservableCollection<SyncedFileInfo>();
            SyncedFilesList.ItemsSource = _syncedFiles;

            // ربط الأحداث
            if (_cloudService != null)
            {
                _cloudService.ConnectionStatusChanged += OnConnectionStatusChanged;
            }

            // تحديث الحالة الأولية
            _ = UpdateConnectionStatusAsync();
        }

        /// <summary>
        /// تحديث حالة الاتصال
        /// </summary>
        private async Task UpdateConnectionStatusAsync()
        {
            try
            {
                if (_cloudService == null) return;

                var isConnected = await _cloudService.IsConnectedAsync();
                
                if (isConnected)
                {
                    await ShowConnectedStateAsync();
                }
                else
                {
                    ShowDisconnectedState();
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحديث حالة الاتصال: {ex.Message}");
                ShowDisconnectedState();
            }
        }

        /// <summary>
        /// عرض حالة الاتصال
        /// </summary>
        private async Task ShowConnectedStateAsync()
        {
            // تحديث أيقونة ونص الحالة
            StatusIcon.Kind = PackIconKind.CloudCheck;
            StatusIcon.Foreground = System.Windows.Media.Brushes.Green;
            StatusText.Text = "متصل";
            StatusText.Foreground = System.Windows.Media.Brushes.Green;
            StatusDescription.Text = "تم ربط Google Drive بنجاح";

            // إظهار معلومات المستخدم
            try
            {
                if (_cloudService != null)
                {
                    var userInfo = await _cloudService.GetUserInfoAsync();
                    if (userInfo is not null)
                    {
                        UserName.Text = string.IsNullOrEmpty(userInfo.Name) ? "غير محدد" : userInfo.Name;
                        UserEmail.Text = string.IsNullOrEmpty(userInfo.Email) ? "غير محدد" : userInfo.Email;
                        UserInfoCard.Visibility = Visibility.Visible;
                    }
                    else
                    {
                        // إخفاء معلومات المستخدم إذا لم تكن متوفرة
                        UserInfoCard.Visibility = Visibility.Collapsed;
                    }
                }
                else
                {
                    // إخفاء معلومات المستخدم إذا لم تكن الخدمة متوفرة
                    UserInfoCard.Visibility = Visibility.Collapsed;
                }
            }
            catch
            {
                // تجاهل الخطأ إذا فشل في الحصول على معلومات المستخدم
                UserInfoCard.Visibility = Visibility.Collapsed;
            }

            // تحديث الأزرار
            ConnectButton.Visibility = Visibility.Collapsed;
            DisconnectButton.Visibility = Visibility.Visible;
            RefreshButton.Visibility = Visibility.Visible;

            // إظهار الإحصائيات والملفات
            StorageStatsCard.Visibility = Visibility.Visible;
            SyncedFilesCard.Visibility = Visibility.Visible;

            // تحديث الإحصائيات
            await UpdateStatisticsAsync();
        }

        /// <summary>
        /// عرض حالة عدم الاتصال
        /// </summary>
        private void ShowDisconnectedState()
        {
            // تحديث أيقونة ونص الحالة
            StatusIcon.Kind = PackIconKind.CloudOff;
            StatusIcon.Foreground = System.Windows.Media.Brushes.Red;
            StatusText.Text = "غير متصل";
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
            StatusDescription.Text = "لم يتم ربط Google Drive بعد";

            // إخفاء معلومات المستخدم
            UserInfoCard.Visibility = Visibility.Collapsed;

            // تحديث الأزرار
            ConnectButton.Visibility = Visibility.Visible;
            DisconnectButton.Visibility = Visibility.Collapsed;
            RefreshButton.Visibility = Visibility.Collapsed;

            // إخفاء الإحصائيات والملفات
            StorageStatsCard.Visibility = Visibility.Collapsed;
            SyncedFilesCard.Visibility = Visibility.Collapsed;

            // مسح البيانات
            _syncedFiles.Clear();
            TotalFilesText.Text = "0";
            TotalSizeText.Text = "0 MB";
            LastSyncText.Text = "--";
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private async Task UpdateStatisticsAsync()
        {
            try
            {
                if (_cloudService == null) return;

                // الحصول على قائمة الملفات المتزامنة
                var files = await GetSyncedFilesAsync();
                
                // تحديث الإحصائيات
                TotalFilesText.Text = files.Count.ToString();
                
                var totalSizeBytes = files.Sum(f => f.SizeBytes);
                TotalSizeText.Text = FormatFileSize(totalSizeBytes);
                
                var lastSync = files.OrderByDescending(f => f.UploadDateTime).FirstOrDefault()?.UploadDateTime;
                LastSyncText.Text = lastSync?.ToString("dd/MM/yyyy HH:mm") ?? "--";

                // تحديث قائمة الملفات
                _syncedFiles.Clear();
                foreach (var file in files.OrderByDescending(f => f.UploadDateTime))
                {
                    _syncedFiles.Add(file);
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على قائمة الملفات المتزامنة
        /// </summary>
        private async Task<List<SyncedFileInfo>> GetSyncedFilesAsync()
        {
            var files = new List<SyncedFileInfo>();

            try
            {
                if (_cloudService == null) return files;

                // الحصول على الملفات من مجلد التطبيق في Google Drive
                var syncedFiles = await _cloudService.GetFilesInFolderAsync("HR_InvoiceArchiver");

                foreach (var file in syncedFiles)
                {
                    files.Add(new SyncedFileInfo
                    {
                        FileName = file.Name,
                        FileSize = FormatFileSize(file.Size),
                        SizeBytes = file.Size,
                        UploadDate = file.CreatedTime.ToString("dd/MM/yyyy HH:mm"),
                        UploadDateTime = file.CreatedTime
                    });
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في الحصول على قائمة الملفات: {ex.Message}");
            }

            return files;
        }

        /// <summary>
        /// تنسيق حجم الملف
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} MB";
            return $"{bytes / (1024 * 1024 * 1024):F1} GB";
        }

        #region Event Handlers

        /// <summary>
        /// ربط Google Drive
        /// </summary>
        private async void ConnectButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_cloudService == null) return;

                _toastService?.ShowInfo("جاري الاتصال", "جاري الاتصال بـ Google Drive...");
                
                var success = await _cloudService.AuthenticateAsync();
                
                if (success)
                {
                    _toastService?.ShowSuccess("تم الاتصال", "تم ربط Google Drive بنجاح");
                }
                else
                {
                    _toastService?.ShowError("فشل الاتصال", "فشل في ربط Google Drive");
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء الاتصال: {ex.Message}");
            }
        }

        /// <summary>
        /// قطع الاتصال
        /// </summary>
        private async void DisconnectButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل أنت متأكد من قطع الاتصال؟\n\nسيتم إيقاف النسخ الاحتياطي التلقائي.",
                    "تأكيد قطع الاتصال",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes && _cloudService != null)
                {
                    await _cloudService.LogoutAsync();
                    _toastService?.ShowInfo("تم قطع الاتصال", "تم قطع الاتصال من Google Drive");
                    await UpdateConnectionStatusAsync();
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"حدث خطأ أثناء قطع الاتصال: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await UpdateConnectionStatusAsync();
            _toastService?.ShowSuccess("تم التحديث", "تم تحديث بيانات التخزين السحابي");
        }

        /// <summary>
        /// تحديث قائمة الملفات
        /// </summary>
        private async void RefreshFilesButton_Click(object sender, RoutedEventArgs e)
        {
            await UpdateStatisticsAsync();
            _toastService?.ShowSuccess("تم التحديث", "تم تحديث قائمة الملفات");
        }

        /// <summary>
        /// معالج تغيير حالة الاتصال
        /// </summary>
        private async void OnConnectionStatusChanged(object? sender, ConnectionStatusChangedEventArgs e)
        {
            await Dispatcher.InvokeAsync(async () =>
            {
                if (e.IsConnected)
                {
                    await ShowConnectedStateAsync();
                }
                else
                {
                    ShowDisconnectedState();
                }
            });
        }

        #endregion

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            if (_cloudService != null)
            {
                _cloudService.ConnectionStatusChanged -= OnConnectionStatusChanged;
            }
        }
    }

    /// <summary>
    /// معلومات الملف المتزامن
    /// </summary>
    public class SyncedFileInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string FileSize { get; set; } = string.Empty;
        public long SizeBytes { get; set; }
        public string UploadDate { get; set; } = string.Empty;
        public DateTime UploadDateTime { get; set; }
    }
}
