<UserControl x:Class="HR_InvoiceArchiver.Pages.SuppliersPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- حذف جميع الأنماط المحلية للأزرار والبطاقات هنا (ModernCardStyle, StatCardStyle, ModernPrimaryButtonStyle, ModernSecondaryButtonStyle, ModernSpecialButtonStyle) -->
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Modern Header with Integrated Actions and Statistics -->
        <materialDesign:Card Grid.Row="0" materialDesign:ElevationAssist.Elevation="Dp4" Margin="16">
            <Grid>
                <Grid.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#1976D2" Offset="0"/>
                        <GradientStop Color="#42A5F5" Offset="1"/>
                    </LinearGradientBrush>
                </Grid.Background>

                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Title and Actions Row -->
                <Grid Grid.Row="0" Margin="24,20,24,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" VerticalAlignment="Center">
                        <TextBlock Text="إدارة الموردين"
                                 Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                 Foreground="White" FontWeight="Bold"/>
                        <TextBlock Text="إدارة شاملة لجميع الموردين والحسابات"
                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                 Foreground="#E3F2FD" Margin="0,4,0,0"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button x:Name="AddNewSupplierButton"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="44"
                                Margin="0,0,12,0"
                                Click="AddNewSupplierButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="إضافة مورد" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SupplierStatementButton"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Height="44"
                                Click="SupplierStatementButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileDocumentEdit" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="كشف حساب" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>

                <!-- Enhanced Statistics Cards -->
                <UniformGrid Grid.Row="1" Columns="4" Margin="24,0,24,20">
                    <!-- Total Suppliers Card -->
                    <materialDesign:Card Margin="8" Padding="16">
                        <materialDesign:Card.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#007BFF" Offset="0"/>
                                <GradientStop Color="#0056B3" Offset="1"/>
                            </LinearGradientBrush>
                        </materialDesign:Card.Background>
                        <materialDesign:Card.Effect>
                            <DropShadowEffect Color="#007BFF" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
                        </materialDesign:Card.Effect>
                        <Grid Margin="20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Border Grid.Column="0" Background="#40FFFFFF" CornerRadius="30"
                                  Width="60" Height="60" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="AccountMultiple" Width="28" Height="28"
                                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>

                            <StackPanel Grid.Column="1" Margin="16,0,0,0" VerticalAlignment="Center">
                                <TextBlock x:Name="TotalSuppliersText" Text="0" FontSize="32" FontWeight="Bold"
                                         Foreground="White"/>
                                <TextBlock Text="إجمالي الموردين" FontSize="15" FontWeight="Medium"
                                         Foreground="#E3F2FD"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Active Suppliers Card -->
                    <materialDesign:Card Margin="8" Padding="16">
                        <materialDesign:Card.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#28A745" Offset="0"/>
                                <GradientStop Color="#1E7E34" Offset="1"/>
                            </LinearGradientBrush>
                        </materialDesign:Card.Background>
                        <materialDesign:Card.Effect>
                            <DropShadowEffect Color="#28A745" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
                        </materialDesign:Card.Effect>
                        <Grid Margin="20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Border Grid.Column="0" Background="#40FFFFFF" CornerRadius="30"
                                  Width="60" Height="60" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="CheckCircleOutline" Width="28" Height="28"
                                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>

                            <StackPanel Grid.Column="1" Margin="16,0,0,0" VerticalAlignment="Center">
                                <TextBlock x:Name="ActiveSuppliersText" Text="0" FontSize="32" FontWeight="Bold"
                                         Foreground="White"/>
                                <TextBlock Text="الموردين النشطين" FontSize="15" FontWeight="Medium"
                                         Foreground="#E8F5E8"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Total Outstanding Card -->
                    <materialDesign:Card Margin="8" Padding="16">
                        <materialDesign:Card.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#DC3545" Offset="0"/>
                                <GradientStop Color="#C82333" Offset="1"/>
                            </LinearGradientBrush>
                        </materialDesign:Card.Background>
                        <materialDesign:Card.Effect>
                            <DropShadowEffect Color="#DC3545" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
                        </materialDesign:Card.Effect>
                        <Grid Margin="20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Border Grid.Column="0" Background="#40FFFFFF" CornerRadius="30"
                                  Width="60" Height="60" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="CashMinus" Width="28" Height="28"
                                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>

                            <StackPanel Grid.Column="1" Margin="16,0,0,0" VerticalAlignment="Center">
                                <TextBlock x:Name="TotalOutstandingText" Text="0 د.ع" FontSize="32" FontWeight="Bold"
                                         Foreground="White"/>
                                <TextBlock Text="إجمالي المستحقات" FontSize="15" FontWeight="Medium"
                                         Foreground="#FFEBEE"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>

                    <!-- Total Paid Card -->
                    <materialDesign:Card Margin="8" Padding="16">
                        <materialDesign:Card.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FD7E14" Offset="0"/>
                                <GradientStop Color="#E55A00" Offset="1"/>
                            </LinearGradientBrush>
                        </materialDesign:Card.Background>
                        <materialDesign:Card.Effect>
                            <DropShadowEffect Color="#FD7E14" Opacity="0.3" BlurRadius="15" ShadowDepth="5"/>
                        </materialDesign:Card.Effect>
                        <Grid Margin="20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Border Grid.Column="0" Background="#40FFFFFF" CornerRadius="30"
                                  Width="60" Height="60" VerticalAlignment="Center">
                                <materialDesign:PackIcon Kind="CashCheck" Width="28" Height="28"
                                                       Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>

                            <StackPanel Grid.Column="1" Margin="16,0,0,0" VerticalAlignment="Center">
                                <TextBlock x:Name="TotalPaidText" Text="0 د.ع" FontSize="32" FontWeight="Bold"
                                         Foreground="White"/>
                                <TextBlock Text="إجمالي المسدد" FontSize="15" FontWeight="Medium"
                                         Foreground="#FFF3E0"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>
                </UniformGrid>

                <!-- Enhanced Search Bar -->
                <Grid Grid.Row="2" Margin="24,0,24,20">
                    <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp4" Background="White">
                        <TextBox x:Name="SearchTextBox"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="البحث في الموردين بالاسم أو رقم الهاتف..."
                               materialDesign:HintAssist.Foreground="#666"
                               Foreground="#333"
                               BorderBrush="Transparent"
                               FontSize="16"
                               Height="56"
                               Margin="16,8"
                               TextChanged="SearchTextBox_TextChanged"
                               materialDesign:TextFieldAssist.HasLeadingIcon="True"
                               materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                               materialDesign:TextFieldAssist.LeadingIconSize="24"/>
                    </materialDesign:Card>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- Enhanced DataGrid Section -->
        <materialDesign:Card Grid.Row="1" materialDesign:ElevationAssist.Elevation="Dp4" Margin="16,0,16,16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- DataGrid Header -->
                <Border Grid.Row="0" Background="#F5F5F5" Padding="20,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" VerticalAlignment="Center">
                            <TextBlock Text="قائمة الموردين"
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     FontWeight="Bold" Foreground="#333"/>
                            <TextBlock Text="إدارة وعرض جميع الموردين المسجلين"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                     Foreground="#666" Margin="0,2,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                            <Button Style="{StaticResource MaterialDesignFlatButton}"
                                    Height="42">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Download" Width="18" Height="18" Margin="0,0,8,0"/>
                                    <TextBlock Text="تصدير" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Modern Enhanced DataGrid -->
                <DataGrid Grid.Row="1" x:Name="SuppliersDataGrid"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         SelectionMode="Single"
                         Background="White"
                         RowHeight="70"
                         FontSize="15"
                         SelectionChanged="SuppliersDataGrid_SelectionChanged"
                         materialDesign:DataGridAssist.CellPadding="20"
                         materialDesign:DataGridAssist.ColumnHeaderPadding="20"
                         AlternatingRowBackground="#F8F9FA"
                         BorderThickness="0">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Background" Value="White"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Margin" Value="0,2"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#BBDEFB"/>
                                    <Setter Property="BorderBrush" Value="#007BFF"/>
                                    <Setter Property="BorderThickness" Value="2,0"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F8F9FA"/>
                            <Setter Property="Foreground" Value="#495057"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                            <Setter Property="FontSize" Value="14"/>
                            <Setter Property="Height" Value="50"/>
                            <Setter Property="BorderThickness" Value="0,0,0,2"/>
                            <Setter Property="BorderBrush" Value="#DEE2E6"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <!-- ID Column -->
                        <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="90" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="16"/>
                                    <Setter Property="Foreground" Value="#007BFF"/>
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                    <Setter Property="Padding" Value="8,4"/>
                                    <Setter Property="TextAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Name Column -->
                        <DataGridTextColumn Header="اسم المورد" Binding="{Binding Name}" Width="220" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter Property="FontSize" Value="16"/>
                                    <Setter Property="Foreground" Value="#212529"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Phone Column -->
                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="160" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontSize" Value="15"/>
                                    <Setter Property="Foreground" Value="#6C757D"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Email Column -->
                        <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="200" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="Foreground" Value="#6C757D"/>
                                    <Setter Property="FontStyle" Value="Italic"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Invoice Count Column -->
                        <DataGridTextColumn Header="عدد الفواتير" Binding="{Binding InvoiceCount}" Width="120" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="16"/>
                                    <Setter Property="Foreground" Value="#28A745"/>
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="Padding" Value="8,4"/>
                                    <Setter Property="TextAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Total Amount Column -->
                        <DataGridTextColumn Header="إجمالي المبلغ" Binding="{Binding TotalAmount, StringFormat='{}{0:N0} د.ع'}" Width="160" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="16"/>
                                    <Setter Property="Foreground" Value="#FD7E14"/>
                                    <Setter Property="Background" Value="#FFF3E0"/>
                                    <Setter Property="Padding" Value="8,4"/>
                                    <Setter Property="TextAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Modern Actions Column -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="180">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <Button ToolTip="تعديل المورد"
                                                Width="36" Height="36"
                                                Margin="4"
                                                Click="EditSupplierButton_Click"
                                                Tag="{Binding}"
                                                Background="#007BFF"
                                                BorderThickness="0"
                                                Style="{StaticResource MaterialDesignFloatingActionMiniButton}">
                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18" Foreground="White"/>
                                        </Button>
                                        <Button ToolTip="حذف المورد"
                                                Width="36" Height="36"
                                                Margin="4"
                                                Click="DeleteSupplierButton_Click"
                                                Tag="{Binding}"
                                                Background="#DC3545"
                                                BorderThickness="0"
                                                Style="{StaticResource MaterialDesignFloatingActionMiniButton}">
                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18" Foreground="White"/>
                                        </Button>
                                        <Button ToolTip="عرض التفاصيل"
                                                Width="36" Height="36"
                                                Margin="4"
                                                Click="ViewSupplierDetailsButton_Click"
                                                Tag="{Binding}"
                                                Background="#28A745"
                                                BorderThickness="0"
                                                Style="{StaticResource MaterialDesignFloatingActionMiniButton}">
                                            <materialDesign:PackIcon Kind="Eye" Width="18" Height="18" Foreground="White"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Modern Loading Panel -->
        <materialDesign:Card x:Name="LoadingPanel"
                           Grid.Row="1"
                           materialDesign:ElevationAssist.Elevation="Dp8"
                           Background="White"
                           Visibility="Collapsed"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Padding="40,30">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Width="50" Height="50"
                           IsIndeterminate="True"
                           Foreground="#007BFF"/>
                <TextBlock Text="جاري تحميل بيانات الموردين..."
                         FontSize="16"
                         FontWeight="Medium"
                         Foreground="#495057"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Empty State Panel -->
        <materialDesign:Card x:Name="EmptyStatePanel"
                           Grid.Row="1"
                           materialDesign:ElevationAssist.Elevation="Dp4"
                           Background="White"
                           Visibility="Collapsed"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Padding="60,40">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="AccountMultipleOutline"
                                       Width="80" Height="80"
                                       Foreground="#CED4DA"
                                       HorizontalAlignment="Center"/>
                <TextBlock Text="لا توجد موردين مسجلين"
                         FontSize="20"
                         FontWeight="SemiBold"
                         Foreground="#6C757D"
                         HorizontalAlignment="Center"
                         Margin="0,20,0,8"/>
                <TextBlock Text="ابدأ بإضافة مورد جديد لعرض البيانات هنا"
                         FontSize="14"
                         Foreground="#ADB5BD"
                         HorizontalAlignment="Center"
                         TextWrapping="Wrap"
                         TextAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
