﻿#pragma checksum "..\..\..\..\Pages\DashboardPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C0456AC4F0549BB0B1D5439341BB6345C10B4B93"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// DashboardPage
    /// </summary>
    public partial class DashboardPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 151 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingOverlay;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.PackIcon LoadingIcon;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainContent;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeSubtitle;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsQuickButton;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QuickTotalInvoices;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QuickTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QuickPaidAmount;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QuickOutstandingAmount;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar TotalInvoicesProgress;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 340 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar TotalAmountProgress;
        
        #line default
        #line hidden
        
        
        #line 366 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidAmountText;
        
        #line default
        #line hidden
        
        
        #line 372 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar PaidAmountProgress;
        
        #line default
        #line hidden
        
        
        #line 398 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OutstandingAmountText;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar OutstandingAmountProgress;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 436 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar OverdueInvoicesProgress;
        
        #line default
        #line hidden
        
        
        #line 475 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ChartTypeButton;
        
        #line default
        #line hidden
        
        
        #line 479 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportChartButton;
        
        #line default
        #line hidden
        
        
        #line 495 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ChartPeriodComboBox;
        
        #line default
        #line hidden
        
        
        #line 504 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowTotalAmountCheckBox;
        
        #line default
        #line hidden
        
        
        #line 506 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowPaidAmountCheckBox;
        
        #line default
        #line hidden
        
        
        #line 515 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart MonthlyChart;
        
        #line default
        #line hidden
        
        
        #line 538 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ChartLoadingIndicator;
        
        #line default
        #line hidden
        
        
        #line 583 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar UnpaidProgressBar;
        
        #line default
        #line hidden
        
        
        #line 586 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnpaidCountText;
        
        #line default
        #line hidden
        
        
        #line 603 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar PartiallyPaidProgressBar;
        
        #line default
        #line hidden
        
        
        #line 606 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PartiallyPaidCountText;
        
        #line default
        #line hidden
        
        
        #line 623 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar PaidProgressBar;
        
        #line default
        #line hidden
        
        
        #line 626 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidCountText;
        
        #line default
        #line hidden
        
        
        #line 639 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentRateText;
        
        #line default
        #line hidden
        
        
        #line 642 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar PaymentRateProgress;
        
        #line default
        #line hidden
        
        
        #line 682 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FilterRecentButton;
        
        #line default
        #line hidden
        
        
        #line 692 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid RecentInvoicesFilterPanel;
        
        #line default
        #line hidden
        
        
        #line 701 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RecentStatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 710 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RecentPeriodFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 719 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl RecentInvoicesItemsControl;
        
        #line default
        #line hidden
        
        
        #line 797 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AlertSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 801 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearAlertsButton;
        
        #line default
        #line hidden
        
        
        #line 817 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CriticalAlertsCount;
        
        #line default
        #line hidden
        
        
        #line 825 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WarningAlertsCount;
        
        #line default
        #line hidden
        
        
        #line 833 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InfoAlertsCount;
        
        #line default
        #line hidden
        
        
        #line 842 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AlertsStackPanel;
        
        #line default
        #line hidden
        
        
        #line 876 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InvoiceActionsTab;
        
        #line default
        #line hidden
        
        
        #line 879 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PaymentActionsTab;
        
        #line default
        #line hidden
        
        
        #line 882 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SystemActionsTab;
        
        #line default
        #line hidden
        
        
        #line 888 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ActionsContainer;
        
        #line default
        #line hidden
        
        
        #line 890 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.UniformGrid InvoiceActionsPanel;
        
        #line default
        #line hidden
        
        
        #line 892 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 908 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewInvoicesButton;
        
        #line default
        #line hidden
        
        
        #line 924 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchInvoicesButton;
        
        #line default
        #line hidden
        
        
        #line 940 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SuppliersButton;
        
        #line default
        #line hidden
        
        
        #line 956 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InvoiceReportsButton;
        
        #line default
        #line hidden
        
        
        #line 972 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportInvoicesButton;
        
        #line default
        #line hidden
        
        
        #line 989 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.UniformGrid PaymentActionsPanel;
        
        #line default
        #line hidden
        
        
        #line 991 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 1007 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMultiPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 1023 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewPaymentsButton;
        
        #line default
        #line hidden
        
        
        #line 1039 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PaymentReportsButton;
        
        #line default
        #line hidden
        
        
        #line 1055 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReconciliationButton;
        
        #line default
        #line hidden
        
        
        #line 1071 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PaymentRemindersButton;
        
        #line default
        #line hidden
        
        
        #line 1087 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportPaymentsButton;
        
        #line default
        #line hidden
        
        
        #line 1104 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.UniformGrid SystemActionsPanel;
        
        #line default
        #line hidden
        
        
        #line 1106 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 1122 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackupButton;
        
        #line default
        #line hidden
        
        
        #line 1138 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RestoreButton;
        
        #line default
        #line hidden
        
        
        #line 1154 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UserManagementButton;
        
        #line default
        #line hidden
        
        
        #line 1170 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SystemLogsButton;
        
        #line default
        #line hidden
        
        
        #line 1186 "..\..\..\..\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AboutButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/dashboardpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\DashboardPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LoadingOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.LoadingIcon = ((MaterialDesignThemes.Wpf.PackIcon)(target));
            return;
            case 3:
            this.MainContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 4:
            this.WelcomeSubtitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 214 "..\..\..\..\Pages\DashboardPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SettingsQuickButton = ((System.Windows.Controls.Button)(target));
            
            #line 219 "..\..\..\..\Pages\DashboardPage.xaml"
            this.SettingsQuickButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.QuickTotalInvoices = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.QuickTotalAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.QuickPaidAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.QuickOutstandingAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            
            #line 284 "..\..\..\..\Pages\DashboardPage.xaml"
            ((MaterialDesignThemes.Wpf.Card)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.TotalInvoicesCard_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.TotalInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TotalInvoicesProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 15:
            
            #line 316 "..\..\..\..\Pages\DashboardPage.xaml"
            ((MaterialDesignThemes.Wpf.Card)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.TotalAmountCard_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TotalAmountProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 18:
            
            #line 348 "..\..\..\..\Pages\DashboardPage.xaml"
            ((MaterialDesignThemes.Wpf.Card)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.PaidAmountCard_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.PaidAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.PaidAmountProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 21:
            
            #line 380 "..\..\..\..\Pages\DashboardPage.xaml"
            ((MaterialDesignThemes.Wpf.Card)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.OutstandingAmountCard_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.OutstandingAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.OutstandingAmountProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 24:
            
            #line 412 "..\..\..\..\Pages\DashboardPage.xaml"
            ((MaterialDesignThemes.Wpf.Card)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.OverdueInvoicesCard_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.OverdueInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.OverdueInvoicesProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 27:
            this.ChartTypeButton = ((System.Windows.Controls.Button)(target));
            
            #line 476 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ChartTypeButton.Click += new System.Windows.RoutedEventHandler(this.ChartTypeButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.ExportChartButton = ((System.Windows.Controls.Button)(target));
            
            #line 480 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ExportChartButton.Click += new System.Windows.RoutedEventHandler(this.ExportChartButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.ChartPeriodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 496 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ChartPeriodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ChartPeriodComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 30:
            this.ShowTotalAmountCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 505 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ShowTotalAmountCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ChartOptions_Changed);
            
            #line default
            #line hidden
            
            #line 505 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ShowTotalAmountCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ChartOptions_Changed);
            
            #line default
            #line hidden
            return;
            case 31:
            this.ShowPaidAmountCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 507 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ShowPaidAmountCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ChartOptions_Changed);
            
            #line default
            #line hidden
            
            #line 507 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ShowPaidAmountCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ChartOptions_Changed);
            
            #line default
            #line hidden
            return;
            case 32:
            this.MonthlyChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 33:
            this.ChartLoadingIndicator = ((System.Windows.Controls.Grid)(target));
            return;
            case 34:
            this.UnpaidProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 35:
            this.UnpaidCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.PartiallyPaidProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 37:
            this.PartiallyPaidCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.PaidProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 39:
            this.PaidCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.PaymentRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.PaymentRateProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 42:
            this.FilterRecentButton = ((System.Windows.Controls.Button)(target));
            
            #line 683 "..\..\..\..\Pages\DashboardPage.xaml"
            this.FilterRecentButton.Click += new System.Windows.RoutedEventHandler(this.FilterRecentButton_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            
            #line 687 "..\..\..\..\Pages\DashboardPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewAllInvoicesButton_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.RecentInvoicesFilterPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 45:
            this.RecentStatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 702 "..\..\..\..\Pages\DashboardPage.xaml"
            this.RecentStatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RecentStatusFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 46:
            this.RecentPeriodFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 711 "..\..\..\..\Pages\DashboardPage.xaml"
            this.RecentPeriodFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RecentPeriodFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 47:
            this.RecentInvoicesItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 49:
            this.AlertSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 798 "..\..\..\..\Pages\DashboardPage.xaml"
            this.AlertSettingsButton.Click += new System.Windows.RoutedEventHandler(this.AlertSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 50:
            this.ClearAlertsButton = ((System.Windows.Controls.Button)(target));
            
            #line 802 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ClearAlertsButton.Click += new System.Windows.RoutedEventHandler(this.ClearAlertsButton_Click);
            
            #line default
            #line hidden
            return;
            case 51:
            this.CriticalAlertsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.WarningAlertsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 53:
            this.InfoAlertsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            this.AlertsStackPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 55:
            this.InvoiceActionsTab = ((System.Windows.Controls.Button)(target));
            
            #line 878 "..\..\..\..\Pages\DashboardPage.xaml"
            this.InvoiceActionsTab.Click += new System.Windows.RoutedEventHandler(this.ActionTab_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            this.PaymentActionsTab = ((System.Windows.Controls.Button)(target));
            
            #line 881 "..\..\..\..\Pages\DashboardPage.xaml"
            this.PaymentActionsTab.Click += new System.Windows.RoutedEventHandler(this.ActionTab_Click);
            
            #line default
            #line hidden
            return;
            case 57:
            this.SystemActionsTab = ((System.Windows.Controls.Button)(target));
            
            #line 884 "..\..\..\..\Pages\DashboardPage.xaml"
            this.SystemActionsTab.Click += new System.Windows.RoutedEventHandler(this.ActionTab_Click);
            
            #line default
            #line hidden
            return;
            case 58:
            this.ActionsContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 59:
            this.InvoiceActionsPanel = ((System.Windows.Controls.Primitives.UniformGrid)(target));
            return;
            case 60:
            this.AddInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 893 "..\..\..\..\Pages\DashboardPage.xaml"
            this.AddInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.AddInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 61:
            this.ViewInvoicesButton = ((System.Windows.Controls.Button)(target));
            
            #line 909 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ViewInvoicesButton.Click += new System.Windows.RoutedEventHandler(this.ViewInvoicesButton_Click);
            
            #line default
            #line hidden
            return;
            case 62:
            this.SearchInvoicesButton = ((System.Windows.Controls.Button)(target));
            
            #line 925 "..\..\..\..\Pages\DashboardPage.xaml"
            this.SearchInvoicesButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 63:
            this.SuppliersButton = ((System.Windows.Controls.Button)(target));
            
            #line 941 "..\..\..\..\Pages\DashboardPage.xaml"
            this.SuppliersButton.Click += new System.Windows.RoutedEventHandler(this.SuppliersButton_Click);
            
            #line default
            #line hidden
            return;
            case 64:
            this.InvoiceReportsButton = ((System.Windows.Controls.Button)(target));
            
            #line 957 "..\..\..\..\Pages\DashboardPage.xaml"
            this.InvoiceReportsButton.Click += new System.Windows.RoutedEventHandler(this.ReportsButton_Click);
            
            #line default
            #line hidden
            return;
            case 65:
            this.ImportInvoicesButton = ((System.Windows.Controls.Button)(target));
            
            #line 973 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ImportInvoicesButton.Click += new System.Windows.RoutedEventHandler(this.ImportInvoicesButton_Click);
            
            #line default
            #line hidden
            return;
            case 66:
            this.PaymentActionsPanel = ((System.Windows.Controls.Primitives.UniformGrid)(target));
            return;
            case 67:
            this.AddPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 992 "..\..\..\..\Pages\DashboardPage.xaml"
            this.AddPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 68:
            this.AddMultiPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 1008 "..\..\..\..\Pages\DashboardPage.xaml"
            this.AddMultiPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddMultiPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 69:
            this.ViewPaymentsButton = ((System.Windows.Controls.Button)(target));
            
            #line 1024 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ViewPaymentsButton.Click += new System.Windows.RoutedEventHandler(this.ViewPaymentsButton_Click);
            
            #line default
            #line hidden
            return;
            case 70:
            this.PaymentReportsButton = ((System.Windows.Controls.Button)(target));
            
            #line 1040 "..\..\..\..\Pages\DashboardPage.xaml"
            this.PaymentReportsButton.Click += new System.Windows.RoutedEventHandler(this.PaymentReportsButton_Click);
            
            #line default
            #line hidden
            return;
            case 71:
            this.ReconciliationButton = ((System.Windows.Controls.Button)(target));
            
            #line 1056 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ReconciliationButton.Click += new System.Windows.RoutedEventHandler(this.ReconciliationButton_Click);
            
            #line default
            #line hidden
            return;
            case 72:
            this.PaymentRemindersButton = ((System.Windows.Controls.Button)(target));
            
            #line 1072 "..\..\..\..\Pages\DashboardPage.xaml"
            this.PaymentRemindersButton.Click += new System.Windows.RoutedEventHandler(this.PaymentRemindersButton_Click);
            
            #line default
            #line hidden
            return;
            case 73:
            this.ExportPaymentsButton = ((System.Windows.Controls.Button)(target));
            
            #line 1088 "..\..\..\..\Pages\DashboardPage.xaml"
            this.ExportPaymentsButton.Click += new System.Windows.RoutedEventHandler(this.ExportPaymentsButton_Click);
            
            #line default
            #line hidden
            return;
            case 74:
            this.SystemActionsPanel = ((System.Windows.Controls.Primitives.UniformGrid)(target));
            return;
            case 75:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 1107 "..\..\..\..\Pages\DashboardPage.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 76:
            this.BackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 1123 "..\..\..\..\Pages\DashboardPage.xaml"
            this.BackupButton.Click += new System.Windows.RoutedEventHandler(this.BackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 77:
            this.RestoreButton = ((System.Windows.Controls.Button)(target));
            
            #line 1139 "..\..\..\..\Pages\DashboardPage.xaml"
            this.RestoreButton.Click += new System.Windows.RoutedEventHandler(this.RestoreButton_Click);
            
            #line default
            #line hidden
            return;
            case 78:
            this.UserManagementButton = ((System.Windows.Controls.Button)(target));
            
            #line 1155 "..\..\..\..\Pages\DashboardPage.xaml"
            this.UserManagementButton.Click += new System.Windows.RoutedEventHandler(this.UserManagementButton_Click);
            
            #line default
            #line hidden
            return;
            case 79:
            this.SystemLogsButton = ((System.Windows.Controls.Button)(target));
            
            #line 1171 "..\..\..\..\Pages\DashboardPage.xaml"
            this.SystemLogsButton.Click += new System.Windows.RoutedEventHandler(this.SystemLogsButton_Click);
            
            #line default
            #line hidden
            return;
            case 80:
            this.AboutButton = ((System.Windows.Controls.Button)(target));
            
            #line 1187 "..\..\..\..\Pages\DashboardPage.xaml"
            this.AboutButton.Click += new System.Windows.RoutedEventHandler(this.AboutButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 48:
            
            #line 724 "..\..\..\..\Pages\DashboardPage.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.RecentInvoiceItem_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

