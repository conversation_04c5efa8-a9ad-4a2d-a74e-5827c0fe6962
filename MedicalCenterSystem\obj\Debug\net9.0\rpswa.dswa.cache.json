{"GlobalPropertiesHash": "WnMrRu3jRE3UGgOTTB+8MYRbUfrlXyIOGZPyEVNiJrw=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Dz8ItzBSrM0XbyrQovTKDR1ol2xJg8mrfTKtfIIOQiI=", "OUmPoLWAPc2NH1huTzofhmjNhE3nrxhjczjhlavuEzM=", "Aap4qLsMZhDxmM7wSxRvPWOTY/g0fBD4XaRAdWahvzc=", "L84BTU4s7YdFo3J+fPvksVPoiRqOzf3lJ2cPp3yKZrY=", "3oZBsehTPmzTdBCctmH0PIsrQL8/6ZXWODsDbe6PUe8=", "jEIkhi9qhG7HR6fYjSwWHHhtCwOYnMcJUG1h0EsH3Wo=", "3J8RDvM2zfeHJmSUKd+Rt3WBwQVeTUxX0SrcUSuI5WA=", "kuBPQmweEYn/huhWq1Plr0zytLqW6UknV35exgoyIbg=", "2ekL/knOL+nJkOUpl0CZTocmzKy0+ZKa9cXC1Hn3oXk=", "pmCtQflDtg2TFmvWoSuwZeqPq8MThA8Z+o1br9/LMUc=", "+WGX41FqzkSXuV9Qkv/wzeWosbyRKCH4RG6odNgHiao=", "1cjKBIdX+bRB6JvYpl3KiA5GM8PI23/+PX3ojS8qJPo=", "Gx6ENbvYJVm9unW9WNYvjSckcvWqL9V9exPjD6kEyJg=", "BYqH4sNopNdOm7vocoOuJJCtpeIAeatOPSVuvFz2dyY=", "1JzXruXNzzCScZU2rGv0d+vxyAtdMkM70icGCgvahYM=", "K0vo42lqq+m11uaSC/jDoi/uzgpvF1WsxeRHgPJysgE=", "EZzmuxZ6WIeIyw4zOnxIE+cniviQ2PVUK21LNsZajlM=", "8kCvtP3szZF1sx/5LwX2HRrBDEmvgurSuLPk9ezmbrM=", "/rltTffRb5TGeleLzFxJaIucI1RzK/sm5XMntZZs1QI=", "4vfwKcTJWslX4auF7mowUqPzvOwt7NIR7oHY/0pEFKw=", "Narkfo4XkQUe48/dP0OYTI3AI7PHy/9IG0+VlslQgXw=", "WsjoFLNGF0klKmq/B2WkZzMTGYGZIJls2Td7S7owx4o=", "6BVaEMG2XZLzkMUaeth+05zBGaeT5czafJSvxd0dddY=", "qSDbN9gpsxFKAU7X9X6g6eCOIdT9bz34cZH8z6Mdpm4=", "oU+edflfN0u3fTV2YeMxcClgxy1faBNIxPapnHqFYTU=", "x5gPakBkcUf92Q2cS/fnZ3zN/qghdUpa9Agcy42GcJs=", "a7oUyPJl1cgajKxluKpFAjIXsXU0CqX1t9V99Isbulc=", "Fr/JrPOh5iLnt87Rw20uzD9pGuT3OXdAbCt2Ia0is9I=", "xFR+qLG8VTJFNiV4k0DeG4in+mf0zl3h4Dg8V6gb8Lo=", "Pt/MMucOnzEXNpORI7QeUl82G7HGSkdXHhbhvcVQ4lw=", "Pv2tfPtTWbew5mb/APQesJe3oAeDLXzjwbP0Ru1syIE=", "31Ejib5DKJmTG0p4n8Ep2DbAip2yv/FIv0Pm92aMZsA=", "2cnMRdA87qVZ9mDkab4z0QgRvnNIOBM5sa6DGi6MX6o=", "QrupeNUQkYcl/NgA+ZSo1bF0q+9SL9jZE7YFzBra5kM=", "WUHXswH3CW3XQrEtQucAdPeffzZ26aZrJeqzGz7CmiQ=", "oYSZIpbJj2sumYJnCuWKuKKuyr6yyuCicAas0sowHDo=", "WKcp+VaPNlyiKhk+HH12va5rYx98/9aAdC0L/OFtVec=", "O44wNrWD5E7+8b61xDp7kFlbJ7KeTBaLhEAaKnMA0hY=", "uGRpVcxbUoq7FsMcTsgN2c34BwueU/jnhJ05ANuQV4w=", "1NBI9hhgODfe7MlbgO4A2D0Zuq5YJ/1cOGQkVir0LGI=", "A/jFfMCfgkaVdu9l9UdVVZzGPGToTYemwwzh5Q3T2SY=", "GeCMcon76NJNlfHRu7DPqzVZmJIvOIBKOUsDFyuOyBk=", "VynuBUhQQdVs27LQ6F4b6Wojt2WsqRCJjDKmL16+Gps=", "O/H86f+UDuXx8pPTWh209RnhoTULA5ZFkNH2mIXnkZU=", "heEslshDyCW1tKuF7hDZkvA0Kgk/u3UU100x94q3h1U=", "I+ZpEOqDeM/6zTng0AJ0bpgsrBEZIZh5PvXra0U2tJ4=", "CnCKO/RAGgCaAybJzbRdAguPFOvQ9Rni58ks8lJOXYM=", "AR8+NFeou4nN9UDQY/91H0GJer6t5mV/ret7syBLUDA=", "sX1desmvPVtXFWI6rvaShu6f4q7E6Bg3nm54SLbdmyE=", "lVuf6Kp/SRVDHJhN89YctPQN0ybP/qofoopu0l1H+ZY=", "ntv/CZGpj4WMZf5WJNGfj38JEUdhcY3gOaQiYn61oCY=", "TcnKlUOVYF8GeL2SQOlstB+6UCuhD6qLzy2bijDYVS4=", "PQJbV7Rywi64yEsC3NLfsBqTrWzZV8L2T6+ANR/13gA=", "MzYnsDRi+eybtmnAJH3h+/P79bbpluH90fuDqwJytgE=", "zESbPvo+qzqFve3zqgwLaCVAbcpsR6A883e9ob1Jm5Q=", "D65+ewIiANa4TU+DH0Ue4T+0+bXhtRfGeqalx+2WK7Y=", "e3+VrKb7pc+jNixYCJfgBZMs4iYGRC/ytkfqVjNbqmA=", "jgkqfMCnrozqFcJyWfeDnrRY5DtBjbCU+r0x3N179nw=", "fUrik2iko8pRX784Kud9/VYje4zRt86LKaIKiloygJk=", "FFNAlGtHUeKIoGV6S23+ZIvFbbQXlJHUvudvAk9gb1Y=", "r6ulKvNTjdUu1v+bs9QprW0Ff0oxqlnWtW4pIb2NI4E=", "u1pXuUjVNGsRI7BI6sghxe32xIbxUBrmnuwcnul6s8o=", "pEsfLJUVbYYZl2S64RVCqXFB1iiZw5dvIbWRTSZ08wI=", "12JhxRsaz6+HVrRb9ovofx6/RkCkSzQE/UedLRz9EEE=", "wUY263KbNiZXMC/udy9dote9bY8erX1suOWwQS+Koro=", "riXkTat+Px5/mCm1VaTDEVQo3fzLVKVTFgienH+aO0U=", "1fGkc0jNBBOsuj6kjHd8UKuIX1n6shSYDcxhsw9mb8Y=", "AiFlf33xw6Yam0065L7907e9WZ55TWe2DCNroqek4JQ=", "VdQXXGL00cbzzP9hPPow103lvlDkKj+AQwtZ/zBL46g=", "KBgAX9AFs/EWSqajBshV6DnzeLloaC8eboS1l8uIzT0=", "CwQElF0EhdzLr7H0KzadN6H418PcRrFUtsl2zmnGkQA=", "QGad0CdPTJiOkUOWIXi2QP4D7t2A3LPp18TKPY/priU=", "RWAQJVRQE75wsbK/H1+CM125anm4ViNiqJHOEpIHhZU=", "CC8wreK0f6YFWpSVrIs10GBgb/taq4BMC6Pindi0pKo=", "EPjfd5D53S4UArd7a6ABx36MVs06Qg/DerxDnyzT24o=", "MsEM2UBryAkAZkRKvDJBClANxRtUPTMlYUFzovGqkHc=", "6EzCfHNYAnyJCLzwGSXM5MEwxUmWAr5F9vBBHbpIjUg=", "bEwWZkwP1sh2QGjGwM7mxqavdsKK+J4Cm46yxad5rP4=", "qQZ6AFj8dp+HTw//fEt8R6kCo8EQ/4AcDxC7HZQdb8E=", "j1yrNIpGKDCXoH295Ra4lDmCrmuXQqv/sf/D0WsaWeI=", "lMOEzKA5aaeKSvp9+9r01KMQGEktC1EW49pZBi5NkPk=", "lAXNwRNwUGUWduyH/VKzhichsgVznIFTN5/rpIe7ngE="], "CachedAssets": {"Dz8ItzBSrM0XbyrQovTKDR1ol2xJg8mrfTKtfIIOQiI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\css\\site.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-07-20T18:42:16.5959515+00:00"}, "OUmPoLWAPc2NH1huTzofhmjNhE3nrxhjczjhlavuEzM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\favicon.ico", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-20T18:42:16.5629519+00:00"}, "Aap4qLsMZhDxmM7wSxRvPWOTY/g0fBD4XaRAdWahvzc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\js\\site.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-20T18:42:16.5959515+00:00"}, "L84BTU4s7YdFo3J+fPvksVPoiRqOzf3lJ2cPp3yKZrY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-20T18:42:16.5068781+00:00"}, "3oZBsehTPmzTdBCctmH0PIsrQL8/6ZXWODsDbe6PUe8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-20T18:42:16.5068781+00:00"}, "jEIkhi9qhG7HR6fYjSwWHHhtCwOYnMcJUG1h0EsH3Wo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-20T18:42:16.5078781+00:00"}, "3J8RDvM2zfeHJmSUKd+Rt3WBwQVeTUxX0SrcUSuI5WA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-20T18:42:16.5078781+00:00"}, "kuBPQmweEYn/huhWq1Plr0zytLqW6UknV35exgoyIbg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-20T18:42:16.5088784+00:00"}, "2ekL/knOL+nJkOUpl0CZTocmzKy0+ZKa9cXC1Hn3oXk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-20T18:42:16.5088784+00:00"}, "pmCtQflDtg2TFmvWoSuwZeqPq8MThA8Z+o1br9/LMUc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-20T18:42:16.5098766+00:00"}, "+WGX41FqzkSXuV9Qkv/wzeWosbyRKCH4RG6odNgHiao=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-20T18:42:16.5098766+00:00"}, "1cjKBIdX+bRB6JvYpl3KiA5GM8PI23/+PX3ojS8qJPo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-20T18:42:16.5098766+00:00"}, "Gx6ENbvYJVm9unW9WNYvjSckcvWqL9V9exPjD6kEyJg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-20T18:42:16.5113803+00:00"}, "BYqH4sNopNdOm7vocoOuJJCtpeIAeatOPSVuvFz2dyY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-20T18:42:16.5113803+00:00"}, "1JzXruXNzzCScZU2rGv0d+vxyAtdMkM70icGCgvahYM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-20T18:42:16.5113803+00:00"}, "K0vo42lqq+m11uaSC/jDoi/uzgpvF1WsxeRHgPJysgE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-20T18:42:16.512385+00:00"}, "EZzmuxZ6WIeIyw4zOnxIE+cniviQ2PVUK21LNsZajlM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-20T18:42:16.512385+00:00"}, "8kCvtP3szZF1sx/5LwX2HRrBDEmvgurSuLPk9ezmbrM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-20T18:42:16.512385+00:00"}, "/rltTffRb5TGeleLzFxJaIucI1RzK/sm5XMntZZs1QI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-20T18:42:16.512385+00:00"}, "4vfwKcTJWslX4auF7mowUqPzvOwt7NIR7oHY/0pEFKw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-20T18:42:16.5138335+00:00"}, "Narkfo4XkQUe48/dP0OYTI3AI7PHy/9IG0+VlslQgXw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-20T18:42:16.5138335+00:00"}, "WsjoFLNGF0klKmq/B2WkZzMTGYGZIJls2Td7S7owx4o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-20T18:42:16.5148387+00:00"}, "6BVaEMG2XZLzkMUaeth+05zBGaeT5czafJSvxd0dddY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-20T18:42:16.5148387+00:00"}, "qSDbN9gpsxFKAU7X9X6g6eCOIdT9bz34cZH8z6Mdpm4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-20T18:42:16.5158373+00:00"}, "oU+edflfN0u3fTV2YeMxcClgxy1faBNIxPapnHqFYTU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-20T18:42:16.5158373+00:00"}, "x5gPakBkcUf92Q2cS/fnZ3zN/qghdUpa9Agcy42GcJs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-20T18:42:16.5168388+00:00"}, "a7oUyPJl1cgajKxluKpFAjIXsXU0CqX1t9V99Isbulc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-20T18:42:16.5168388+00:00"}, "Fr/JrPOh5iLnt87Rw20uzD9pGuT3OXdAbCt2Ia0is9I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-20T18:42:16.517839+00:00"}, "xFR+qLG8VTJFNiV4k0DeG4in+mf0zl3h4Dg8V6gb8Lo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-20T18:42:16.5188377+00:00"}, "Pt/MMucOnzEXNpORI7QeUl82G7HGSkdXHhbhvcVQ4lw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-20T18:42:16.5198372+00:00"}, "Pv2tfPtTWbew5mb/APQesJe3oAeDLXzjwbP0Ru1syIE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-20T18:42:16.5208397+00:00"}, "31Ejib5DKJmTG0p4n8Ep2DbAip2yv/FIv0Pm92aMZsA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-20T18:42:16.5208397+00:00"}, "2cnMRdA87qVZ9mDkab4z0QgRvnNIOBM5sa6DGi6MX6o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-20T18:42:16.5218385+00:00"}, "QrupeNUQkYcl/NgA+ZSo1bF0q+9SL9jZE7YFzBra5kM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-20T18:42:16.5218385+00:00"}, "WUHXswH3CW3XQrEtQucAdPeffzZ26aZrJeqzGz7CmiQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-20T18:42:16.5228384+00:00"}, "oYSZIpbJj2sumYJnCuWKuKKuyr6yyuCicAas0sowHDo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-20T18:42:16.5238372+00:00"}, "WKcp+VaPNlyiKhk+HH12va5rYx98/9aAdC0L/OFtVec=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-20T18:42:16.52497+00:00"}, "O44wNrWD5E7+8b61xDp7kFlbJ7KeTBaLhEAaKnMA0hY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-20T18:42:16.5259747+00:00"}, "uGRpVcxbUoq7FsMcTsgN2c34BwueU/jnhJ05ANuQV4w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-20T18:42:16.5269748+00:00"}, "1NBI9hhgODfe7MlbgO4A2D0Zuq5YJ/1cOGQkVir0LGI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-20T18:42:16.5279748+00:00"}, "A/jFfMCfgkaVdu9l9UdVVZzGPGToTYemwwzh5Q3T2SY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-20T18:42:16.5289735+00:00"}, "GeCMcon76NJNlfHRu7DPqzVZmJIvOIBKOUsDFyuOyBk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-20T18:42:16.5289735+00:00"}, "VynuBUhQQdVs27LQ6F4b6Wojt2WsqRCJjDKmL16+Gps=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-20T18:42:16.5299731+00:00"}, "O/H86f+UDuXx8pPTWh209RnhoTULA5ZFkNH2mIXnkZU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-20T18:42:16.5299731+00:00"}, "heEslshDyCW1tKuF7hDZkvA0Kgk/u3UU100x94q3h1U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-20T18:42:16.5309748+00:00"}, "I+ZpEOqDeM/6zTng0AJ0bpgsrBEZIZh5PvXra0U2tJ4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-20T18:42:16.5309748+00:00"}, "CnCKO/RAGgCaAybJzbRdAguPFOvQ9Rni58ks8lJOXYM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-20T18:42:16.5319744+00:00"}, "AR8+NFeou4nN9UDQY/91H0GJer6t5mV/ret7syBLUDA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-20T18:42:16.52497+00:00"}, "sX1desmvPVtXFWI6rvaShu6f4q7E6Bg3nm54SLbdmyE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-20T18:42:16.5669516+00:00"}, "lVuf6Kp/SRVDHJhN89YctPQN0ybP/qofoopu0l1H+ZY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-20T18:42:16.5679648+00:00"}, "ntv/CZGpj4WMZf5WJNGfj38JEUdhcY3gOaQiYn61oCY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-20T18:42:16.5279748+00:00"}, "TcnKlUOVYF8GeL2SQOlstB+6UCuhD6qLzy2bijDYVS4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-20T18:42:16.5629519+00:00"}, "PQJbV7Rywi64yEsC3NLfsBqTrWzZV8L2T6+ANR/13gA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-20T18:42:16.5639527+00:00"}, "MzYnsDRi+eybtmnAJH3h+/P79bbpluH90fuDqwJytgE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-20T18:42:16.5639527+00:00"}, "zESbPvo+qzqFve3zqgwLaCVAbcpsR6A883e9ob1Jm5Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-20T18:42:16.5649517+00:00"}, "D65+ewIiANa4TU+DH0Ue4T+0+bXhtRfGeqalx+2WK7Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-20T18:42:16.5279748+00:00"}, "e3+VrKb7pc+jNixYCJfgBZMs4iYGRC/ytkfqVjNbqmA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-20T18:42:16.5329896+00:00"}, "jgkqfMCnrozqFcJyWfeDnrRY5DtBjbCU+r0x3N179nw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-20T18:42:16.5359742+00:00"}, "fUrik2iko8pRX784Kud9/VYje4zRt86LKaIKiloygJk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-20T18:42:16.536974+00:00"}, "FFNAlGtHUeKIoGV6S23+ZIvFbbQXlJHUvudvAk9gb1Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-20T18:42:16.5414211+00:00"}, "r6ulKvNTjdUu1v+bs9QprW0Ff0oxqlnWtW4pIb2NI4E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-20T18:42:16.543424+00:00"}, "u1pXuUjVNGsRI7BI6sghxe32xIbxUBrmnuwcnul6s8o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-20T18:42:16.5454446+00:00"}, "pEsfLJUVbYYZl2S64RVCqXFB1iiZw5dvIbWRTSZ08wI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "MedicalCenterSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\ahmed\\new\\MedicalCenterSystem\\wwwroot\\", "BasePath": "_content/MedicalCenterSystem", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-20T18:42:16.5259747+00:00"}}, "CachedCopyCandidates": {}}