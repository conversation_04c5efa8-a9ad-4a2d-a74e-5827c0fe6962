﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MedicalCenterSystem.Migrations
{
    /// <inheritdoc />
    public partial class AddDoctorServiceEnhancements : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "DoctorServices",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<decimal>(
                name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
                table: "DoctorServices",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "FixedAmount",
                table: "DoctorServices",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "DoctorServices",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsFixedAmount",
                table: "DoctorServices",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "DoctorServices",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "ServiceCost",
                table: "DoctorServices",
                type: "decimal(18,2)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "DoctorServices");

            migrationBuilder.DropColumn(
                name: "DoctorDefaultPrice",
                table: "DoctorServices");

            migrationBuilder.DropColumn(
                name: "FixedAmount",
                table: "DoctorServices");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "DoctorServices");

            migrationBuilder.DropColumn(
                name: "IsFixedAmount",
                table: "DoctorServices");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "DoctorServices");

            migrationBuilder.DropColumn(
                name: "ServiceCost",
                table: "DoctorServices");
        }
    }
}
