
برومبت تفصيلي بلغة C# لبناء نظام إدارة مركز طبي

======================================
✅ المرحلة الأولى: إعداد النظام (تعريف الأطباء والخدمات)
🎯 الهدف:
تهيئة قاعدة البيانات التي تشمل الأطباء، الخدمات، وربط الخدمات مع الأطباء مع تحديد النسب.

🔸 1. الكلاسات الأساسية (Models)

public class Doctor
{
    public int DoctorId { get; set; }
    public string FullName { get; set; }
    public string Specialty { get; set; }
    public bool IsActive { get; set; }
    public ICollection<DoctorService> DoctorServices { get; set; }
}

public class MedicalService
{
    public int MedicalServiceId { get; set; }
    public string ServiceName { get; set; } // كشفية، فحص، مختبر، سونار...
    public string ServiceType { get; set; } // Direct / Referral
    public bool IsCenterService { get; set; } // True إذا كانت تابعة للمركز
    public decimal? DefaultPrice { get; set; }
    public ICollection<DoctorService> DoctorServices { get; set; }
}

public class DoctorService
{
    public int DoctorServiceId { get; set; }
    public int DoctorId { get; set; }
    public int MedicalServiceId { get; set; }
    public string LinkType { get; set; } // Direct / Referral
    public bool HasPercentage { get; set; }
    public decimal? Percentage { get; set; }
    public Doctor Doctor { get; set; }
    public MedicalService MedicalService { get; set; }
}

🔸 2. جداول قاعدة البيانات (SQL Server)

CREATE TABLE Doctors (
    DoctorId INT PRIMARY KEY IDENTITY,
    FullName NVARCHAR(100),
    Specialty NVARCHAR(100),
    IsActive BIT
);

CREATE TABLE MedicalServices (
    MedicalServiceId INT PRIMARY KEY IDENTITY,
    ServiceName NVARCHAR(100),
    ServiceType NVARCHAR(20),
    IsCenterService BIT,
    DefaultPrice DECIMAL(18,2)
);

CREATE TABLE DoctorServices (
    DoctorServiceId INT PRIMARY KEY IDENTITY,
    DoctorId INT FOREIGN KEY REFERENCES Doctors(DoctorId),
    MedicalServiceId INT FOREIGN KEY REFERENCES MedicalServices(MedicalServiceId),
    LinkType NVARCHAR(20),
    HasPercentage BIT,
    Percentage DECIMAL(5,2)
);

======================================
✅ المرحلة الثانية: واجهة تسجيل المراجعين (الرسبشن)
🎯 الهدف:
إدخال معلومات المراجع، وإعطاءه رقم مراجع حسب الطبيب والتاريخ.

🔸 الكلاس البرمجي للمراجع:

public class PatientVisit
{
    public int PatientVisitId { get; set; }
    public DateTime VisitDate { get; set; }
    public int DoctorId { get; set; }
    public int VisitNumber { get; set; } // يبدأ من 1 لكل دكتور يومياً
    public string PatientName { get; set; }
    public string Diagnosis { get; set; }
    public int Age { get; set; }
    public string Province { get; set; }
    public string BookingStaff { get; set; }
    public string VisitCountLabel { get; set; } // أولى، ثانية...
    public string PhoneNumber { get; set; }
    public Doctor Doctor { get; set; }
}

🔸 قاعدة البيانات:

CREATE TABLE PatientVisits (
    PatientVisitId INT PRIMARY KEY IDENTITY,
    VisitDate DATE,
    DoctorId INT FOREIGN KEY REFERENCES Doctors(DoctorId),
    VisitNumber INT,
    PatientName NVARCHAR(100),
    Diagnosis NVARCHAR(255),
    Age INT,
    Province NVARCHAR(100),
    BookingStaff NVARCHAR(100),
    VisitCountLabel NVARCHAR(50),
    PhoneNumber NVARCHAR(20)
);

📌 المنطق المهم:
- رقم المراجع (VisitNumber) يبدأ من 1 لكل دكتور في كل يوم جديد.

======================================
✅ المرحلة الثالثة: الدفع الرئيسي (الكشف والفحص)
🎯 الهدف:
سجل أجور الكشف والفحص يدويًا، واحتسب النسبة للطبيب والمركز.

public class MainPayment
{
    public int MainPaymentId { get; set; }
    public int PatientVisitId { get; set; }
    public decimal ConsultationFee { get; set; }
    public decimal ExamFee { get; set; }
    public decimal DoctorShare { get; set; }
    public decimal CenterShare { get; set; }
    public string CashierName { get; set; }
    public string Notes { get; set; }
    public PatientVisit PatientVisit { get; set; }
}

CREATE TABLE MainPayments (
    MainPaymentId INT PRIMARY KEY IDENTITY,
    PatientVisitId INT FOREIGN KEY REFERENCES PatientVisits(PatientVisitId),
    ConsultationFee DECIMAL(18,2),
    ExamFee DECIMAL(18,2),
    DoctorShare DECIMAL(18,2),
    CenterShare DECIMAL(18,2),
    CashierName NVARCHAR(100),
    Notes NVARCHAR(MAX)
);

======================================
✅ المرحلة الرابعة: تسجيل الخدمات الإضافية (تحاليل، رنين...)
تكون حصه الطبيب اما نسبه بالمئه من الخدمه او مبلغ مقطوع لكل خدمه يتم تحويلها
ضع حقل لاضافه المبلغ الافتراضي 
وضع حقل لتكلفه الخدمه اختياري يكون 
يعني مثال 
حصه الطبيب من الخدمه = مبلغ الخدمه - كلفه الخدمه  /2 
حصه المركز = المبلغ الكلي - حصه الطبيب 
ويكون سعر  الخدمه يحدد سعر افتراضي وامكانيه تركهه بدون سعر ليحدد عند اضافتهه للمراجع لاحقا 
يعني اني عندي خدمات يكون سعرها ثابت وخدمات يكون سعرها متغير اريد مرونه في ذلك 
والخدمه تكون متغيره من طبيب الى اخر ضع امكانيه اضافه سعر افتراضي لكل خدمه مضافه لطبيب
public class ReferralPayment
{
    public int ReferralPaymentId { get; set; }
    public int PatientVisitId { get; set; }
    public int MedicalServiceId { get; set; }
    public decimal Amount { get; set; }
    public decimal? DoctorShare { get; set; }
    public decimal CenterShare { get; set; }
    public string Section { get; set; }
    public string CashierName { get; set; }
    public PatientVisit PatientVisit { get; set; }
    public MedicalService MedicalService { get; set; }
}

======================================
✅ المرحلة الخامسة: التقارير

- تقرير وارد المركز لكل قسم (مختبر، رنين، علاج...)
- تقرير تحويلات كل طبيب
- تقرير تفصيلي لكل مراجع
تقرير تفصيلي لجميع الايرادات والتحويلات مع المبالغ 
جميع الاطباء حصه المركز وحصه الطبيب مع الرصيد الاخير لجميع الواردات 
======================================
✅ المرحلة السادسة: المستخدمين والصلاحيات 

public class UserAccount
{
    public int UserAccountId { get; set; }
    public string Username { get; set; }
    public string HashedPassword { get; set; }
    public string Role { get; set; } // Admin / Reception / Cashier
}


✅ المرحلة السابعه: واجهه لقسم الكول سنتر 
تقارير تفصيليه لكل مراجع مسجل للطبيب وداخل للطبيب لكل يوم مع جميع معلوماتهم يكون بدون اسعار فقط احصائيات للمراجعين على كل طبيب
