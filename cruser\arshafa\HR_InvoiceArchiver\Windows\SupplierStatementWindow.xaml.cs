using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Utils;

namespace HR_InvoiceArchiver.Windows
{
    public partial class SupplierStatementWindow : Window
    {
        private readonly ISupplierService _supplierService;
        private readonly IInvoiceService _invoiceService;
        private readonly IPaymentService _paymentService;
        
        private readonly int _supplierId;
        private Supplier? _supplier;
        
        public ObservableCollection<Invoice> Invoices { get; set; } = new();
        public ObservableCollection<Payment> Payments { get; set; } = new();
        
        public SupplierStatementWindow(int supplierId)
        {
            _supplierId = supplierId;
            
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _paymentService = App.ServiceProvider.GetRequiredService<IPaymentService>();
            
            InitializeComponent();
            
            Loaded += SupplierStatementWindow_Loaded;
        }
        
        private async void SupplierStatementWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }
        
        private async Task LoadDataAsync()
        {
            try
            {
                // Load supplier information
                _supplier = await _supplierService.GetSupplierByIdAsync(_supplierId);
                if (_supplier == null)
                {
                    Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show("لم يتم العثور على المورد المطلوب", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        Close();
                    });
                    return;
                }

                // Update UI with supplier info on UI thread
                await Dispatcher.InvokeAsync(() => UpdateSupplierInfo());

                // Load invoices and payments
                await LoadInvoicesAsync();
                await LoadPaymentsAsync();

                // Update summary on UI thread
                await Dispatcher.InvokeAsync(() => UpdateSummary());
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }
        
        private void UpdateSupplierInfo()
        {
            if (_supplier == null) return;
            
            HeaderTextBlock.Text = $"كشف حساب المورد - {_supplier.Name}";
            SupplierNameTextBlock.Text = $"من تاريخ: {DateTime.Now.AddMonths(-12):yyyy/MM/dd} إلى تاريخ: {DateTime.Now:yyyy/MM/dd}";
            
            SupplierNameValueTextBlock.Text = _supplier.Name;
            ContactPersonTextBlock.Text = _supplier.ContactPerson ?? "غير محدد";
            PhoneTextBlock.Text = _supplier.Phone ?? "غير محدد";
            EmailTextBlock.Text = _supplier.Email ?? "غير محدد";
            TaxNumberTextBlock.Text = _supplier.TaxNumber ?? "غير محدد";
        }
        
        private async Task LoadInvoicesAsync()
        {
            try
            {
                var invoices = await _invoiceService.GetInvoicesBySupplierAsync(_supplierId);
                
                Invoices.Clear();
                foreach (var invoice in invoices.OrderByDescending(i => i.InvoiceDate))
                {
                    Invoices.Add(invoice);
                }
                
                InvoicesDataGrid.ItemsSource = Invoices;
                InvoiceCountTextBlock.Text = $"{Invoices.Count} فاتورة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفواتير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async Task LoadPaymentsAsync()
        {
            try
            {
                var payments = await _paymentService.GetPaymentsBySupplierAsync(_supplierId);

                // Ensure UI updates happen on UI thread
                await Dispatcher.InvokeAsync(() =>
                {
                    Payments.Clear();
                    foreach (var payment in payments.OrderByDescending(p => p.PaymentDate))
                    {
                        Payments.Add(payment);
                    }

                    PaymentsDataGrid.ItemsSource = Payments;
                    PaymentCountTextBlock.Text = $"{Payments.Count} مدفوعة";
                });
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"خطأ في تحميل المدفوعات: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }
        
        private void UpdateSummary()
        {
            var totalInvoices = Invoices.Count;
            var totalAmount = Invoices.Sum(i => i.Amount);
            var paidAmount = Invoices.Sum(i => i.PaidAmount);
            var remainingAmount = totalAmount - paidAmount;
            var overdueInvoices = Invoices.Count(i => i.IsOverdue);
            var lastInvoiceDate = Invoices.Any() ? Invoices.Max(i => i.InvoiceDate) : (DateTime?)null;
            
            TotalInvoicesTextBlock.Text = totalInvoices.ToString();
            TotalAmountTextBlock.Text = CurrencyHelper.FormatForStats(totalAmount);
            PaidAmountTextBlock.Text = CurrencyHelper.FormatForStats(paidAmount);
            RemainingAmountTextBlock.Text = CurrencyHelper.FormatForStats(remainingAmount);
            OverdueInvoicesTextBlock.Text = overdueInvoices.ToString();
            LastInvoiceDateTextBlock.Text = lastInvoiceDate?.ToString("yyyy/MM/dd") ?? "لا توجد فواتير";
            
            // Color coding for remaining amount
            if (remainingAmount > 0)
            {
                RemainingAmountTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                RemainingAmountTextBlock.FontWeight = FontWeights.Bold;
            }
            else
            {
                RemainingAmountTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                RemainingAmountTextBlock.FontWeight = FontWeights.Bold;
            }
            
            // Color coding for overdue invoices
            if (overdueInvoices > 0)
            {
                OverdueInvoicesTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                OverdueInvoicesTextBlock.FontWeight = FontWeights.Bold;
            }
            else
            {
                OverdueInvoicesTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                OverdueInvoicesTextBlock.FontWeight = FontWeights.Normal;
            }
        }
        
        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }
        
        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_supplier == null)
                    return;
                
                var fileName = $"كشف_حساب_{_supplier.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                
                // Create statement data
                var statementData = new SupplierStatementData
                {
                    Supplier = _supplier,
                    Invoices = Invoices.ToList(),
                    Payments = Payments.ToList(),
                    GeneratedDate = DateTime.Now
                };
                
                await ExportHelper.ExportSupplierStatementToExcelAsync(statementData, fileName);
                
                MessageBox.Show($"تم تصدير كشف الحساب بنجاح إلى:\n{fileName}", "نجح التصدير", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_supplier == null)
                    return;
                
                var fileName = $"كشف_حساب_{_supplier.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                
                // Create statement data
                var statementData = new SupplierStatementData
                {
                    Supplier = _supplier,
                    Invoices = Invoices.ToList(),
                    Payments = Payments.ToList(),
                    GeneratedDate = DateTime.Now
                };
                
                await ExportHelper.ExportSupplierStatementToPdfAsync(statementData, fileName);
                
                MessageBox.Show($"تم إنشاء كشف الحساب بنجاح:\n{fileName}", "نجح الإنشاء", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء كشف الحساب: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
    
    public class SupplierStatementData
    {
        public Supplier Supplier { get; set; } = new();
        public List<Invoice> Invoices { get; set; } = new();
        public List<Payment> Payments { get; set; } = new();
        public DateTime GeneratedDate { get; set; }
        
        public decimal TotalAmount => Invoices.Sum(i => i.Amount);
        public decimal PaidAmount => Invoices.Sum(i => i.PaidAmount);
        public decimal RemainingAmount => TotalAmount - PaidAmount;
        public int OverdueInvoicesCount => Invoices.Count(i => i.IsOverdue);
    }
}
