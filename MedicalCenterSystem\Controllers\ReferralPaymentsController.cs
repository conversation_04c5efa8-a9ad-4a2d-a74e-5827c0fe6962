using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MedicalCenterSystem.Data;
using MedicalCenterSystem.Models;

namespace MedicalCenterSystem.Controllers
{
    public class ReferralPaymentsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ReferralPaymentsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: ReferralPayments
        public async Task<IActionResult> Index()
        {
            var referralPayments = await _context.ReferralPayments
                .Include(r => r.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Include(r => r.MedicalService)
                .OrderByDescending(r => r.PaymentDate)
                .ToListAsync();
            return View(referralPayments);
        }

        // GET: ReferralPayments/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var referralPayment = await _context.ReferralPayments
                .Include(r => r.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Include(r => r.MedicalService)
                .FirstOrDefaultAsync(m => m.ReferralPaymentId == id);
            if (referralPayment == null)
            {
                return NotFound();
            }

            return View(referralPayment);
        }

        // GET: ReferralPayments/Create
        public IActionResult Create(int? patientVisitId)
        {
            if (patientVisitId.HasValue)
            {
                var patientVisit = _context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .FirstOrDefault(pv => pv.PatientVisitId == patientVisitId.Value);
                
                if (patientVisit != null)
                {
                    ViewBag.PatientVisit = patientVisit;
                    var model = new ReferralPayment
                    {
                        PatientVisitId = patientVisitId.Value,
                        PaymentDate = DateTime.Now
                    };
                    ViewData["MedicalServiceId"] = new SelectList(_context.MedicalServices, "MedicalServiceId", "ServiceName");
                    return View(model);
                }
            }

            ViewData["PatientVisitId"] = new SelectList(
                _context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .Select(pv => new { 
                        pv.PatientVisitId, 
                        DisplayText = $"{pv.PatientName} - {pv.Doctor.FullName} - {pv.VisitDate:yyyy-MM-dd} - رقم {pv.VisitNumber}" 
                    }),
                "PatientVisitId", "DisplayText");
            
            ViewData["MedicalServiceId"] = new SelectList(_context.MedicalServices, "MedicalServiceId", "ServiceName");
            
            return View(new ReferralPayment { PaymentDate = DateTime.Now });
        }

        // POST: ReferralPayments/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("ReferralPaymentId,PatientVisitId,MedicalServiceId,Amount,DoctorShare,CenterShare,Section,CashierName,PaymentDate,Notes")] ReferralPayment referralPayment)
        {
            if (ModelState.IsValid)
            {
                _context.Add(referralPayment);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }

            ViewData["PatientVisitId"] = new SelectList(
                _context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .Select(pv => new { 
                        pv.PatientVisitId, 
                        DisplayText = $"{pv.PatientName} - {pv.Doctor.FullName} - {pv.VisitDate:yyyy-MM-dd} - رقم {pv.VisitNumber}" 
                    }),
                "PatientVisitId", "DisplayText", referralPayment.PatientVisitId);
            
            ViewData["MedicalServiceId"] = new SelectList(_context.MedicalServices, "MedicalServiceId", "ServiceName", referralPayment.MedicalServiceId);
            
            return View(referralPayment);
        }

        // GET: ReferralPayments/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var referralPayment = await _context.ReferralPayments.FindAsync(id);
            if (referralPayment == null)
            {
                return NotFound();
            }

            ViewData["PatientVisitId"] = new SelectList(
                _context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .Select(pv => new { 
                        pv.PatientVisitId, 
                        DisplayText = $"{pv.PatientName} - {pv.Doctor.FullName} - {pv.VisitDate:yyyy-MM-dd} - رقم {pv.VisitNumber}" 
                    }),
                "PatientVisitId", "DisplayText", referralPayment.PatientVisitId);
            
            ViewData["MedicalServiceId"] = new SelectList(_context.MedicalServices, "MedicalServiceId", "ServiceName", referralPayment.MedicalServiceId);
            
            return View(referralPayment);
        }

        // POST: ReferralPayments/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("ReferralPaymentId,PatientVisitId,MedicalServiceId,Amount,DoctorShare,CenterShare,Section,CashierName,PaymentDate,Notes")] ReferralPayment referralPayment)
        {
            if (id != referralPayment.ReferralPaymentId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(referralPayment);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ReferralPaymentExists(referralPayment.ReferralPaymentId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewData["PatientVisitId"] = new SelectList(
                _context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .Select(pv => new { 
                        pv.PatientVisitId, 
                        DisplayText = $"{pv.PatientName} - {pv.Doctor.FullName} - {pv.VisitDate:yyyy-MM-dd} - رقم {pv.VisitNumber}" 
                    }),
                "PatientVisitId", "DisplayText", referralPayment.PatientVisitId);
            
            ViewData["MedicalServiceId"] = new SelectList(_context.MedicalServices, "MedicalServiceId", "ServiceName", referralPayment.MedicalServiceId);
            
            return View(referralPayment);
        }

        // GET: ReferralPayments/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var referralPayment = await _context.ReferralPayments
                .Include(r => r.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Include(r => r.MedicalService)
                .FirstOrDefaultAsync(m => m.ReferralPaymentId == id);
            if (referralPayment == null)
            {
                return NotFound();
            }

            return View(referralPayment);
        }

        // POST: ReferralPayments/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var referralPayment = await _context.ReferralPayments.FindAsync(id);
            if (referralPayment != null)
            {
                _context.ReferralPayments.Remove(referralPayment);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool ReferralPaymentExists(int id)
        {
            return _context.ReferralPayments.Any(e => e.ReferralPaymentId == id);
        }
    }
}
