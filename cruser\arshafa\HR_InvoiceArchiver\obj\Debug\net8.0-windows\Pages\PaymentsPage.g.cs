﻿#pragma checksum "..\..\..\..\Pages\PaymentsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "317D84A5D953E609FD7E812DB43B463BCFB39F0F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using HR_InvoiceArchiver.Converters;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// PaymentsPage
    /// </summary>
    public partial class PaymentsPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 180 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CashPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CardPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMultiPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 319 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 352 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ToggleFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 380 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FiltersPanel;
        
        #line default
        #line hidden
        
        
        #line 410 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 418 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 427 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 435 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MaxAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 444 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodComboBox;
        
        #line default
        #line hidden
        
        
        #line 457 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 471 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 485 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFiltersButton;
        
        #line default
        #line hidden
        
        
        #line 506 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 847 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentCountText;
        
        #line default
        #line hidden
        
        
        #line 860 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDisplayedAmountText;
        
        #line default
        #line hidden
        
        
        #line 873 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        
        #line 883 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 919 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid EmptyStatePanel;
        
        #line default
        #line hidden
        
        
        #line 961 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MultiPaymentOverlay;
        
        #line default
        #line hidden
        
        
        #line 978 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MultiPaymentContainer;
        
        #line default
        #line hidden
        
        
        #line 985 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PaymentDetailsOverlay;
        
        #line default
        #line hidden
        
        
        #line 1002 "..\..\..\..\Pages\PaymentsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PaymentDetailsContainer;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/paymentspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\PaymentsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TotalPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.CashPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.CardPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.AddPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 302 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AddMultiPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 312 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.AddMultiPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddMultiPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 322 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 332 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 357 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 365 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ToggleFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 375 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ToggleFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ToggleFilters_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.FiltersPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 13:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 413 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.FromDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 421 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ToDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 15:
            this.MinAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 430 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.MinAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 16:
            this.MaxAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 438 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.MaxAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 17:
            this.PaymentMethodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 447 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.PaymentMethodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentMethodFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 18:
            this.PaymentStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 460 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.PaymentStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentStatusFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ApplyFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 478 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ApplyFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ApplyFilters_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.ClearFiltersButton = ((System.Windows.Controls.Button)(target));
            
            #line 491 "..\..\..\..\Pages\PaymentsPage.xaml"
            this.ClearFiltersButton.Click += new System.Windows.RoutedEventHandler(this.ClearFilters_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 26:
            this.PaymentCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.TotalDisplayedAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.LoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 30:
            this.EmptyStatePanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 31:
            
            #line 939 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            
            #line 949 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.MultiPaymentOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 34:
            this.MultiPaymentContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 35:
            this.PaymentDetailsOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 36:
            this.PaymentDetailsContainer = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 22:
            
            #line 734 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewReceiptAttachment_Click);
            
            #line default
            #line hidden
            break;
            case 23:
            
            #line 759 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewPaymentDetails_Click);
            
            #line default
            #line hidden
            break;
            case 24:
            
            #line 783 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditPayment_Click);
            
            #line default
            #line hidden
            break;
            case 25:
            
            #line 805 "..\..\..\..\Pages\PaymentsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeletePayment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

