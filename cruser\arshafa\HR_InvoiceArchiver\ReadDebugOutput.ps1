# PowerShell script to read debug output from the application
$logPath = "$env:TEMP\HR_InvoiceArchiver_Debug.log"

Write-Host "Monitoring debug output from: $logPath" -ForegroundColor Green
Write-Host "Press Ctrl+C to stop monitoring" -ForegroundColor Yellow
Write-Host "=" * 50

if (Test-Path $logPath) {
    Get-Content $logPath -Wait -Tail 50
} else {
    Write-Host "Log file not found. Make sure the application is running." -ForegroundColor Red
    Write-Host "Expected path: $logPath" -ForegroundColor Yellow
}
