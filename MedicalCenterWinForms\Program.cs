using MedicalCenterWinForms.Forms;
using MedicalCenterWinForms.Services;

namespace MedicalCenterWinForms;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main()
    {
        // To customize application configuration such as set high DPI settings or default font,
        // see https://aka.ms/applicationconfiguration.
        ApplicationConfiguration.Initialize();

        // Enable visual styles
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);

        // Set Arabic font for the entire application
        SetArabicFont();

        // Test message to confirm app is running
        MessageBox.Show("التطبيق يعمل! سيتم فتح نافذة اختبار بسيطة", "اختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);

        // Create a simple test form
        var testForm = new Form();
        testForm.Text = "نافذة اختبار";
        testForm.Size = new Size(400, 300);
        testForm.StartPosition = FormStartPosition.CenterScreen;
        testForm.TopMost = true;
        testForm.ShowInTaskbar = true;

        var label = new Label();
        label.Text = "هذه نافذة اختبار\nإذا ظهرت هذه النافذة، فالمشكلة في LoginForm";
        label.AutoSize = true;
        label.Location = new Point(50, 50);
        testForm.Controls.Add(label);

        Application.Run(testForm);
    }

    private static void SetArabicFont()
    {
        // Set default font for Arabic support
        var arabicFont = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);

        // Try to use Tahoma if available (better Arabic support)
        try
        {
            arabicFont = new Font("Tahoma", 9F, FontStyle.Regular, GraphicsUnit.Point);
        }
        catch
        {
            // Fallback to Segoe UI if Tahoma is not available
            arabicFont = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
        }

        Application.SetDefaultFont(arabicFont);
    }
}