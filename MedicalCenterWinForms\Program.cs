using MedicalCenterWinForms.Forms;
using MedicalCenterWinForms.Services;

namespace MedicalCenterWinForms;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main()
    {
        // To customize application configuration such as set high DPI settings or default font,
        // see https://aka.ms/applicationconfiguration.
        ApplicationConfiguration.Initialize();

        // Enable visual styles
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);

        // Set Arabic font for the entire application
        SetArabicFont();

        // Start with login form
        var databaseService = new DatabaseService();

        try
        {
            MessageBox.Show("سيتم فتح نافذة تسجيل الدخول", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);

            using var loginForm = new LoginForm(databaseService);
            loginForm.WindowState = FormWindowState.Normal;
            loginForm.TopMost = true;
            loginForm.ShowInTaskbar = true;
            loginForm.BringToFront();

            if (loginForm.ShowDialog() == DialogResult.OK && loginForm.CurrentUser != null)
            {
                Application.Run(new MainForm(databaseService));
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void SetArabicFont()
    {
        // Set default font for Arabic support
        var arabicFont = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);

        // Try to use Tahoma if available (better Arabic support)
        try
        {
            arabicFont = new Font("Tahoma", 9F, FontStyle.Regular, GraphicsUnit.Point);
        }
        catch
        {
            // Fallback to Segoe UI if Tahoma is not available
            arabicFont = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
        }

        Application.SetDefaultFont(arabicFont);
    }
}