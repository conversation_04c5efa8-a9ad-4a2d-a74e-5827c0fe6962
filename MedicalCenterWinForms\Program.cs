using MedicalCenterWinForms.Forms;
using MedicalCenterWinForms.Services;

namespace MedicalCenterWinForms;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main()
    {
        // To customize application configuration such as set high DPI settings or default font,
        // see https://aka.ms/applicationconfiguration.
        ApplicationConfiguration.Initialize();

        // Enable visual styles
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);

        // Set Arabic font for the entire application
        SetArabicFont();

        // Test with simple form first
        try
        {
            MessageBox.Show("سيتم اختبار نافذة بسيطة أولاً", "اختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);

            // Create simple form without complex initialization
            var simpleForm = new Form();
            simpleForm.Text = "نافذة تسجيل دخول مبسطة";
            simpleForm.Size = new Size(400, 300);
            simpleForm.StartPosition = FormStartPosition.CenterScreen;
            simpleForm.FormBorderStyle = FormBorderStyle.FixedSingle;
            simpleForm.TopMost = true;
            simpleForm.ShowInTaskbar = true;

            // Add simple controls
            var lblTitle = new Label();
            lblTitle.Text = "تسجيل الدخول";
            lblTitle.Location = new Point(150, 50);
            lblTitle.AutoSize = true;
            simpleForm.Controls.Add(lblTitle);

            var txtUser = new TextBox();
            txtUser.Location = new Point(100, 100);
            txtUser.Size = new Size(200, 25);
            txtUser.Text = "admin";
            simpleForm.Controls.Add(txtUser);

            var btnLogin = new Button();
            btnLogin.Text = "دخول";
            btnLogin.Location = new Point(150, 150);
            btnLogin.Size = new Size(100, 30);
            btnLogin.Click += (s, e) => { simpleForm.DialogResult = DialogResult.OK; simpleForm.Close(); };
            simpleForm.Controls.Add(btnLogin);

            if (simpleForm.ShowDialog() == DialogResult.OK)
            {
                MessageBox.Show("تم تسجيل الدخول بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void SetArabicFont()
    {
        // Set default font for Arabic support
        var arabicFont = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);

        // Try to use Tahoma if available (better Arabic support)
        try
        {
            arabicFont = new Font("Tahoma", 9F, FontStyle.Regular, GraphicsUnit.Point);
        }
        catch
        {
            // Fallback to Segoe UI if Tahoma is not available
            arabicFont = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
        }

        Application.SetDefaultFont(arabicFont);
    }
}