using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Shapes;
using Microsoft.Extensions.DependencyInjection;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Utils;

namespace HR_InvoiceArchiver.Controls
{
    public partial class SupplierStatementControl : UserControl
    {
        private readonly ISupplierService _supplierService;
        private readonly IInvoiceService _invoiceService;
        private readonly IPaymentService _paymentService;
        private readonly IToastService _toastService;
        
        private readonly int _supplierId;
        private Supplier? _supplier;
        
        public ObservableCollection<Invoice> Invoices { get; set; } = new();
        public ObservableCollection<Payment> Payments { get; set; } = new();

        // Event for form closure
        public event EventHandler<SupplierStatementEventArgs>? FormClosed;

        public SupplierStatementControl(int supplierId)
        {
            InitializeComponent();
            
            _supplierId = supplierId;
            
            // Get services from DI container
            _supplierService = App.ServiceProvider.GetRequiredService<ISupplierService>();
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _paymentService = App.ServiceProvider.GetRequiredService<IPaymentService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();

            // Set data context
            InvoicesDataGrid.ItemsSource = Invoices;
            PaymentsDataGrid.ItemsSource = Payments;

            // Load data and show animation
            Loaded += async (s, e) => 
            {
                await LoadDataAsync();
                ShowAnimation();
            };
        }

        private void ShowAnimation()
        {
            var slideInStoryboard = (Storyboard)Resources["SlideInAnimation"];
            slideInStoryboard.Begin();
        }

        private void HideAnimation()
        {
            var slideOutStoryboard = (Storyboard)Resources["SlideOutAnimation"];
            slideOutStoryboard.Begin();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                LoadingProgressBar.Visibility = Visibility.Visible;

                // Load supplier information
                _supplier = await _supplierService.GetSupplierByIdAsync(_supplierId);
                if (_supplier == null)
                {
                    _toastService.ShowError("خطأ", "لم يتم العثور على المورد المطلوب");
                    CloseForm(false);
                    return;
                }

                // Update UI with supplier info
                await Dispatcher.InvokeAsync(() => UpdateSupplierInfo());

                // Load invoices and payments
                await LoadInvoicesAsync();
                await LoadPaymentsAsync();

                // Update summary
                await Dispatcher.InvokeAsync(() => UpdateSummary());

                LoadingProgressBar.Visibility = Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                LoadingProgressBar.Visibility = Visibility.Collapsed;
                _toastService.ShowError("خطأ في تحميل البيانات", ex.Message);
            }
        }

        private void UpdateSupplierInfo()
        {
            if (_supplier != null)
            {
                SupplierNameText.Text = _supplier.Name;
            }
        }

        private async Task LoadInvoicesAsync()
        {
            try
            {
                var invoices = await _invoiceService.GetInvoicesBySupplierAsync(_supplierId);

                await Dispatcher.InvokeAsync(() =>
                {
                    Invoices.Clear();
                    foreach (var invoice in invoices.OrderByDescending(i => i.InvoiceDate))
                    {
                        Invoices.Add(invoice);
                    }

                    InvoiceCountText.Text = $"{Invoices.Count} فاتورة";
                });
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل الفواتير", ex.Message);
            }
        }

        private async Task LoadPaymentsAsync()
        {
            try
            {
                var payments = await _paymentService.GetPaymentsBySupplierAsync(_supplierId);

                await Dispatcher.InvokeAsync(() =>
                {
                    Payments.Clear();
                    foreach (var payment in payments.OrderByDescending(p => p.PaymentDate))
                    {
                        Payments.Add(payment);
                    }

                    PaymentCountText.Text = $"{Payments.Count} دفعة";
                });
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل المدفوعات", ex.Message);
            }
        }

        private void UpdateSummary()
        {
            var totalInvoices = Invoices.Count;
            var totalAmount = Invoices.Sum(i => i.Amount);
            var paidAmount = Invoices.Sum(i => i.PaidAmount);
            var remainingAmount = totalAmount - paidAmount;

            TotalInvoicesText.Text = totalInvoices.ToString();
            TotalAmountText.Text = CurrencyHelper.FormatForStats(totalAmount);
            PaidAmountText.Text = CurrencyHelper.FormatForStats(paidAmount);
            RemainingAmountText.Text = CurrencyHelper.FormatForStats(remainingAmount);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            CloseForm(false);
        }

        private void RootGrid_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // Check if the click was on the background (not on the main card)
            if (e.Source is Grid grid && grid.Name == "RootGrid")
            {
                CloseForm(false);
            }
            else if (e.Source is Rectangle rectangle && rectangle.Name == "BackgroundOverlay")
            {
                CloseForm(false);
            }
        }

        private void CloseForm(bool success)
        {
            HideAnimation();
        }

        private void SlideOutAnimation_Completed(object sender, EventArgs e)
        {
            FormClosed?.Invoke(this, new SupplierStatementEventArgs { Success = false });
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_supplier == null)
                    return;
                
                var fileName = $"كشف_حساب_{_supplier.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                
                // Create statement data
                var statementData = new SupplierStatementData
                {
                    Supplier = _supplier,
                    Invoices = Invoices.ToList(),
                    Payments = Payments.ToList(),
                    GeneratedDate = DateTime.Now
                };
                
                // Show loading
                LoadingProgressBar.Visibility = Visibility.Visible;
                
                // For now, show a message that export is not implemented
                await Task.Delay(1000); // Simulate processing
                
                LoadingProgressBar.Visibility = Visibility.Collapsed;
                
                _toastService.ShowInfo("تصدير Excel", "سيتم تنفيذ هذه الميزة قريباً");
            }
            catch (Exception ex)
            {
                LoadingProgressBar.Visibility = Visibility.Collapsed;
                _toastService.ShowError("خطأ في التصدير", ex.Message);
            }
        }

        private async void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_supplier == null)
                    return;
                
                var fileName = $"كشف_حساب_{_supplier.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                
                // Create statement data
                var statementData = new SupplierStatementData
                {
                    Supplier = _supplier,
                    Invoices = Invoices.ToList(),
                    Payments = Payments.ToList(),
                    GeneratedDate = DateTime.Now
                };
                
                // Show loading
                LoadingProgressBar.Visibility = Visibility.Visible;
                
                // For now, show a message that print is not implemented
                await Task.Delay(1000); // Simulate processing
                
                LoadingProgressBar.Visibility = Visibility.Collapsed;
                
                _toastService.ShowInfo("طباعة PDF", "سيتم تنفيذ هذه الميزة قريباً");
            }
            catch (Exception ex)
            {
                LoadingProgressBar.Visibility = Visibility.Collapsed;
                _toastService.ShowError("خطأ في الطباعة", ex.Message);
            }
        }
    }

    // Event args for form closure
    public class SupplierStatementEventArgs : EventArgs
    {
        public bool Success { get; set; }
    }

    // Data class for statement export
    public class SupplierStatementData
    {
        public Supplier Supplier { get; set; } = new();
        public List<Invoice> Invoices { get; set; } = new();
        public List<Payment> Payments { get; set; } = new();
        public DateTime GeneratedDate { get; set; }
        
        public decimal TotalAmount => Invoices.Sum(i => i.Amount);
        public decimal PaidAmount => Invoices.Sum(i => i.PaidAmount);
        public decimal RemainingAmount => TotalAmount - PaidAmount;
        public int OverdueInvoicesCount => Invoices.Count(i => i.IsOverdue);
    }
}
