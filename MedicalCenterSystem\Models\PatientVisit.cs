using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MedicalCenterSystem.Models
{
    public class PatientVisit
    {
        public int PatientVisitId { get; set; }

        [Required]
        [Display(Name = "تاريخ الزيارة")]
        [DataType(DataType.Date)]
        public DateTime VisitDate { get; set; } = DateTime.Today;

        [Required]
        public int DoctorId { get; set; }

        [Display(Name = "رقم المراجع")]
        public int VisitNumber { get; set; } // يبدأ من 1 لكل دكتور يومياً

        [Required]
        [StringLength(100)]
        [Display(Name = "اسم المريض")]
        public string PatientName { get; set; } = string.Empty;

        [StringLength(255)]
        [Display(Name = "التشخيص")]
        public string Diagnosis { get; set; } = string.Empty;

        [Display(Name = "العمر")]
        public int Age { get; set; }

        [StringLength(100)]
        [Display(Name = "المحافظة")]
        public string Province { get; set; } = string.Empty;

        [StringLength(100)]
        [Display(Name = "موظف الحجز")]
        public string BookingStaff { get; set; } = string.Empty;

        [StringLength(50)]
        [Display(Name = "عدد الزيارة")]
        public string VisitCountLabel { get; set; } = string.Empty; // أولى، ثانية...

        [StringLength(20)]
        [Display(Name = "رقم الهاتف")]
        public string PhoneNumber { get; set; } = string.Empty;

        // Navigation Properties
        [ForeignKey("DoctorId")]
        public virtual Doctor Doctor { get; set; } = null!;

        public virtual MainPayment? MainPayment { get; set; }
        public virtual ICollection<ReferralPayment> ReferralPayments { get; set; } = new List<ReferralPayment>();
    }
}
