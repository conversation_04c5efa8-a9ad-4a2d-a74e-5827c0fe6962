@model MedicalCenterSystem.Models.Doctor

@{
    ViewData["Title"] = "إضافة طبيب جديد";
}

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="mb-3">
                        <label asp-for="FullName" class="form-label"></label>
                        <input asp-for="FullName" class="form-control" placeholder="أدخل اسم الطبيب الكامل" />
                        <span asp-validation-for="FullName" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Specialty" class="form-label"></label>
                        <input asp-for="Specialty" class="form-control" placeholder="أدخل تخصص الطبيب" />
                        <span asp-validation-for="Specialty" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input asp-for="IsActive" class="form-check-input" type="checkbox" checked />
                            <label asp-for="IsActive" class="form-check-label">
                                نشط
                            </label>
                        </div>
                        <span asp-validation-for="IsActive" class="text-danger"></span>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
