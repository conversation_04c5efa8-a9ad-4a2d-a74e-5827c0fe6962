using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using MedicalCenterWinForms.Data;
using System.Data.SqlClient;

namespace MedicalCenterWinForms.Services
{
    public class DatabaseService
    {
        private readonly IConfiguration _configuration;

        public DatabaseService()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            _configuration = builder.Build();
        }

        public ApplicationDbContext GetDbContext()
        {
            var connectionString = GetConnectionString();
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseSqlServer(connectionString)
                .Options;

            return new ApplicationDbContext(options);
        }

        public string GetConnectionString()
        {
            var useNetwork = bool.Parse(_configuration["AppSettings:UseNetworkDatabase"] ?? "false");
            
            if (useNetwork)
            {
                return _configuration.GetConnectionString("NetworkConnection") ?? 
                       throw new InvalidOperationException("Network connection string not found");
            }
            else
            {
                return _configuration.GetConnectionString("DefaultConnection") ?? 
                       throw new InvalidOperationException("Default connection string not found");
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var context = GetDbContext();
                await context.Database.CanConnectAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> EnsureDatabaseCreatedAsync()
        {
            try
            {
                using var context = GetDbContext();
                await context.Database.EnsureCreatedAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public void UpdateNetworkSettings(string serverName, string databaseName, string username, string password)
        {
            var connectionString = $"Server={serverName};Database={databaseName};User Id={username};Password={password};MultipleActiveResultSets=true;TrustServerCertificate=true";

            // Update configuration in memory
            _configuration["ConnectionStrings:NetworkConnection"] = connectionString;
            _configuration["AppSettings:ServerName"] = serverName;
            _configuration["AppSettings:DatabaseName"] = databaseName;
            _configuration["AppSettings:Username"] = username;
            _configuration["AppSettings:Password"] = password;
        }

        public void SetUseNetworkDatabase(bool useNetwork)
        {
            _configuration["AppSettings:UseNetworkDatabase"] = useNetwork.ToString();
        }

        public bool IsUsingNetworkDatabase()
        {
            return bool.Parse(_configuration["AppSettings:UseNetworkDatabase"] ?? "false");
        }

        public (string serverName, string databaseName, string username, string password) GetNetworkSettings()
        {
            return (
                _configuration["AppSettings:ServerName"] ?? "",
                _configuration["AppSettings:DatabaseName"] ?? "",
                _configuration["AppSettings:Username"] ?? "",
                _configuration["AppSettings:Password"] ?? ""
            );
        }


    }
}
