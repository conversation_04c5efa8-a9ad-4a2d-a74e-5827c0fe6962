<UserControl x:Class="HR_InvoiceArchiver.Controls.OfferFormWindow"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HR_InvoiceArchiver;component/Styles/MaterialDesignStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Main Container with Shadow -->
    <materialDesign:Card Style="{StaticResource ModernCardStyle}" 
                         Width="800" 
                         Height="600"
                         Margin="20"
                         materialDesign:ShadowAssist.ShadowDepth="Depth4">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" Background="#1976D2" CornerRadius="8,8,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="20,15">
                        <TextBlock Text="🤝" FontSize="24" Margin="0,0,12,0"/>
                        <StackPanel>
                            <TextBlock x:Name="HeaderTitle" Text="إضافة عرض جديد" 
                                     FontSize="20" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="أدخل تفاصيل العرض المقدم من المندوب" 
                                     FontSize="13" Foreground="#E3F2FD"/>
                        </StackPanel>
                    </StackPanel>
                    
                    <Button Grid.Column="1" x:Name="CloseButton" 
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="40" Height="40" Margin="10"
                            Foreground="White"
                            Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Form Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20">
                <StackPanel>
                    
                    <!-- معلومات المكتب والمندوب -->
                    <GroupBox Header="معلومات المكتب والمندوب" Margin="0,0,0,20">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="ScientificOfficeTextBox" 
                                   Grid.Row="0" Grid.Column="0"
                                   Margin="0,0,10,15"
                                   materialDesign:HintAssist.Hint="المكتب العلمي *"
                                   materialDesign:HintAssist.IsFloating="True"/>

                            <TextBox x:Name="RepresentativeNameTextBox" 
                                   Grid.Row="0" Grid.Column="1"
                                   Margin="5,0,5,15"
                                   materialDesign:HintAssist.Hint="اسم المندوب *"
                                   materialDesign:HintAssist.IsFloating="True"/>

                            <TextBox x:Name="RepresentativePhoneTextBox" 
                                   Grid.Row="0" Grid.Column="2"
                                   Margin="10,0,0,15"
                                   materialDesign:HintAssist.Hint="رقم المندوب"
                                   materialDesign:HintAssist.IsFloating="True"/>
                        </Grid>
                    </GroupBox>

                    <!-- معلومات المادة -->
                    <GroupBox Header="معلومات المادة" Margin="0,0,0,20">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <ComboBox x:Name="ScientificNameComboBox" 
                                    Grid.Row="0" Grid.Column="0"
                                    Margin="0,0,10,15"
                                    IsEditable="True"
                                    materialDesign:HintAssist.Hint="المادة العلمية *"
                                    materialDesign:HintAssist.IsFloating="True"/>

                            <TextBox x:Name="TradeNameTextBox" 
                                   Grid.Row="0" Grid.Column="1"
                                   Margin="10,0,0,15"
                                   materialDesign:HintAssist.Hint="المادة التجارية"
                                   materialDesign:HintAssist.IsFloating="True"/>
                        </Grid>
                    </GroupBox>

                    <!-- معلومات السعر والعرض -->
                    <GroupBox Header="معلومات السعر والعرض" Margin="0,0,0,20">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="PriceTextBox" 
                                   Grid.Row="0" Grid.Column="0"
                                   Margin="0,0,10,15"
                                   materialDesign:HintAssist.Hint="السعر *"
                                   materialDesign:HintAssist.IsFloating="True"/>

                            <TextBox x:Name="BonusOrDiscountTextBox" 
                                   Grid.Row="0" Grid.Column="1"
                                   Margin="10,0,0,15"
                                   materialDesign:HintAssist.Hint="البونص أو الخصم"
                                   materialDesign:HintAssist.IsFloating="True"/>
                        </Grid>
                    </GroupBox>

                    <!-- ملاحظات ومرفقات -->
                    <GroupBox Header="ملاحظات ومرفقات" Margin="0,0,0,20">
                        <StackPanel Margin="10">
                            <TextBox x:Name="NotesTextBox" 
                                   Height="80"
                                   TextWrapping="Wrap"
                                   AcceptsReturn="True"
                                   VerticalScrollBarVisibility="Auto"
                                   Margin="0,0,0,15"
                                   materialDesign:HintAssist.Hint="ملاحظات إضافية"
                                   materialDesign:HintAssist.IsFloating="True"/>

                            <StackPanel Orientation="Horizontal">
                                <Button x:Name="AttachmentButton" 
                                      Content="رفع مرفق" 
                                      Style="{StaticResource ModernSecondaryButtonStyle}"
                                      Width="120"
                                      Margin="0,0,10,0"
                                      Click="AttachmentButton_Click">
                                    <Button.Content>
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Attachment" Width="16" Height="16" Margin="0,0,5,0"/>
                                            <TextBlock Text="رفع مرفق"/>
                                        </StackPanel>
                                    </Button.Content>
                                </Button>
                                
                                <TextBlock x:Name="AttachmentLabel" 
                                         Text="لم يتم اختيار ملف"
                                         VerticalAlignment="Center"
                                         Foreground="#666"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>

                </StackPanel>
            </ScrollViewer>

            <!-- Footer Buttons -->
            <Border Grid.Row="2" Background="#F5F5F5" CornerRadius="0,0,8,8">
                <StackPanel Orientation="Horizontal" 
                          HorizontalAlignment="Left" 
                          Margin="20,15">
                    
                    <Button x:Name="SaveButton" 
                          Content="حفظ العرض" 
                          Style="{StaticResource ModernPrimaryButtonStyle}"
                          Width="120"
                          Margin="0,0,10,0"
                          Click="SaveButton_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="حفظ العرض"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                    
                    <Button x:Name="CancelButton" 
                          Content="إلغاء" 
                          Style="{StaticResource ModernSecondaryButtonStyle}"
                          Width="100"
                          Margin="0,0,10,0"
                          Click="CancelButton_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cancel" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="إلغاء"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                    
                    <Button x:Name="ClearButton" 
                          Content="تفريغ الحقول" 
                          Style="{StaticResource ModernSecondaryButtonStyle}"
                          Width="120"
                          Click="ClearButton_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="تفريغ الحقول"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Border>

        </Grid>
    </materialDesign:Card>
</UserControl>
