using System.Windows;

namespace TestApp
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }
    }
}

public partial class App : Application
{
    [System.STAThread]
    public static void Main()
    {
        var app = new App();
        app.InitializeComponent();
        app.Run(new MainWindow());
    }

    public void InitializeComponent()
    {
        // Empty implementation
    }
}
