using Xunit;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Tests.Repositories
{
    public class InvoiceRepositoryTests : IDisposable
    {
        private readonly DatabaseContext _context;
        private readonly InvoiceRepository _repository;

        public InvoiceRepositoryTests()
        {
            var options = new DbContextOptionsBuilder<DatabaseContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new DatabaseContext(options);
            _repository = new InvoiceRepository(_context);

            // إضافة بيانات تجريبية
            SeedTestData();
        }

        private void SeedTestData()
        {
            var supplier = new Supplier
            {
                Id = 1,
                Name = "مورد تجريبي",
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            var invoices = new List<Invoice>
            {
                new Invoice
                {
                    Id = 1,
                    InvoiceNumber = "INV-001",
                    SupplierId = 1,
                    Amount = 1000,
                    PaidAmount = 0,
                    Status = InvoiceStatus.Unpaid,
                    InvoiceDate = DateTime.Now.AddDays(-10),
                    DueDate = DateTime.Now.AddDays(20),
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Invoice
                {
                    Id = 2,
                    InvoiceNumber = "INV-002",
                    SupplierId = 1,
                    Amount = 2000,
                    PaidAmount = 1000,
                    Status = InvoiceStatus.PartiallyPaid,
                    InvoiceDate = DateTime.Now.AddDays(-5),
                    DueDate = DateTime.Now.AddDays(25),
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Invoice
                {
                    Id = 3,
                    InvoiceNumber = "INV-003",
                    SupplierId = 1,
                    Amount = 1500,
                    PaidAmount = 1500,
                    Status = InvoiceStatus.Paid,
                    InvoiceDate = DateTime.Now.AddDays(-15),
                    DueDate = DateTime.Now.AddDays(-5),
                    IsActive = true,
                    CreatedDate = DateTime.Now
                }
            };

            _context.Suppliers.Add(supplier);
            _context.Invoices.AddRange(invoices);
            _context.SaveChanges();
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnAllActiveInvoices()
        {
            // Act
            var result = await _repository.GetAllAsync();

            // Assert
            result.Should().HaveCount(3);
            result.All(i => i.IsActive).Should().BeTrue();
        }

        [Fact]
        public async Task GetAllBasicAsync_ShouldReturnAllActiveInvoicesWithoutRelations()
        {
            // Act
            var result = await _repository.GetAllBasicAsync();

            // Assert
            result.Should().HaveCount(3);
            result.All(i => i.IsActive).Should().BeTrue();
        }

        [Fact]
        public async Task GetByIdAsync_WithValidId_ShouldReturnInvoice()
        {
            // Act
            var result = await _repository.GetByIdAsync(1);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(1);
            result.InvoiceNumber.Should().Be("INV-001");
        }

        [Fact]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Act
            var result = await _repository.GetByIdAsync(999);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetByInvoiceNumberAsync_WithValidNumber_ShouldReturnInvoice()
        {
            // Act
            var result = await _repository.GetByInvoiceNumberAsync("INV-001");

            // Assert
            result.Should().NotBeNull();
            result!.InvoiceNumber.Should().Be("INV-001");
        }

        [Fact]
        public async Task GetByInvoiceNumberAsync_WithInvalidNumber_ShouldReturnNull()
        {
            // Act
            var result = await _repository.GetByInvoiceNumberAsync("INV-999");

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetBySupplierIdAsync_WithValidSupplierId_ShouldReturnInvoices()
        {
            // Act
            var result = await _repository.GetBySupplierIdAsync(1);

            // Assert
            result.Should().HaveCount(3);
            result.All(i => i.SupplierId == 1).Should().BeTrue();
        }

        [Fact]
        public async Task GetBySupplierIdAsync_WithInvalidSupplierId_ShouldReturnEmpty()
        {
            // Act
            var result = await _repository.GetBySupplierIdAsync(999);

            // Assert
            result.Should().BeEmpty();
        }

        [Fact]
        public async Task AddAsync_WithValidInvoice_ShouldAddAndReturnInvoice()
        {
            // Arrange
            var newInvoice = new Invoice
            {
                InvoiceNumber = "INV-004",
                SupplierId = 1,
                Amount = 3000,
                Status = InvoiceStatus.Unpaid,
                InvoiceDate = DateTime.Now,
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            // Act
            var result = await _repository.AddAsync(newInvoice);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().BeGreaterThan(0);
            result.InvoiceNumber.Should().Be("INV-004");

            // التحقق من إضافة السجل في قاعدة البيانات
            var addedInvoice = await _context.Invoices.FindAsync(result.Id);
            addedInvoice.Should().NotBeNull();
        }

        [Fact]
        public async Task UpdateAsync_WithValidInvoice_ShouldUpdateAndReturnInvoice()
        {
            // Arrange
            var existingInvoice = await _repository.GetByIdAsync(1);
            existingInvoice!.Amount = 1200;
            existingInvoice.UpdatedDate = DateTime.Now;

            // Act
            var result = await _repository.UpdateAsync(existingInvoice);

            // Assert
            result.Should().NotBeNull();
            result.Amount.Should().Be(1200);
            result.UpdatedDate.Should().NotBeNull();

            // التحقق من التحديث في قاعدة البيانات
            var updatedInvoice = await _context.Invoices.FindAsync(1);
            updatedInvoice!.Amount.Should().Be(1200);
        }

        [Fact]
        public async Task DeleteAsync_WithValidId_ShouldMarkAsInactiveAndReturnTrue()
        {
            // Act
            var result = await _repository.DeleteAsync(1);

            // Assert
            result.Should().BeTrue();

            // التحقق من أن الفاتورة تم تعليمها كغير نشطة
            var deletedInvoice = await _context.Invoices.FindAsync(1);
            deletedInvoice!.IsActive.Should().BeFalse();
        }

        [Fact]
        public async Task DeleteAsync_WithInvalidId_ShouldReturnFalse()
        {
            // Act
            var result = await _repository.DeleteAsync(999);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task ExistsAsync_WithExistingId_ShouldReturnTrue()
        {
            // Act
            var result = await _repository.ExistsAsync(1);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task ExistsAsync_WithNonExistingId_ShouldReturnFalse()
        {
            // Act
            var result = await _repository.ExistsAsync(999);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task ExistsByInvoiceNumberAsync_WithExistingNumber_ShouldReturnTrue()
        {
            // Act
            var result = await _repository.ExistsByInvoiceNumberAsync("INV-001");

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task ExistsByInvoiceNumberAsync_WithNonExistingNumber_ShouldReturnFalse()
        {
            // Act
            var result = await _repository.ExistsByInvoiceNumberAsync("INV-999");

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task ExistsByInvoiceNumberAsync_WithExcludeId_ShouldReturnFalse()
        {
            // Act - استثناء الفاتورة نفسها
            var result = await _repository.ExistsByInvoiceNumberAsync("INV-001", 1);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task SearchAsync_WithMatchingTerm_ShouldReturnMatchingInvoices()
        {
            // Act
            var result = await _repository.SearchAsync("INV-001");

            // Assert
            result.Should().HaveCount(1);
            result.First().InvoiceNumber.Should().Be("INV-001");
        }

        [Fact]
        public async Task SearchAsync_WithNonMatchingTerm_ShouldReturnEmpty()
        {
            // Act
            var result = await _repository.SearchAsync("XYZ-999");

            // Assert
            result.Should().BeEmpty();
        }

        [Fact]
        public async Task GetCountByStatusAsync_ShouldReturnCorrectCount()
        {
            // Act
            var unpaidCount = await _repository.GetCountByStatusAsync(InvoiceStatus.Unpaid);
            var partiallyPaidCount = await _repository.GetCountByStatusAsync(InvoiceStatus.PartiallyPaid);
            var paidCount = await _repository.GetCountByStatusAsync(InvoiceStatus.Paid);

            // Assert
            unpaidCount.Should().Be(1);
            partiallyPaidCount.Should().Be(1);
            paidCount.Should().Be(1);
        }

        [Fact]
        public async Task GetTotalCountAsync_ShouldReturnCorrectCount()
        {
            // Act
            var result = await _repository.GetTotalCountAsync();

            // Assert
            result.Should().Be(3);
        }

        [Fact]
        public async Task GetTotalAmountAsync_ShouldReturnCorrectSum()
        {
            // Act
            var result = await _repository.GetTotalAmountAsync();

            // Assert
            result.Should().Be(4500); // 1000 + 2000 + 1500
        }

        [Fact]
        public async Task GetTotalPaidAmountAsync_ShouldReturnCorrectSum()
        {
            // Act
            var result = await _repository.GetTotalPaidAmountAsync();

            // Assert
            result.Should().Be(2500); // 0 + 1000 + 1500
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
