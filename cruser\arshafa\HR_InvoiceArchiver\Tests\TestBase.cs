using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Models;
using System;
using System.Collections.Generic;

namespace HR_InvoiceArchiver.Tests
{
    /// <summary>
    /// فئة أساسية للاختبارات توفر إعدادات مشتركة
    /// </summary>
    public abstract class TestBase : IDisposable
    {
        protected DatabaseContext Context { get; private set; }

        protected TestBase()
        {
            var options = new DbContextOptionsBuilder<DatabaseContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            Context = new DatabaseContext(options);
            SeedTestData();
        }

        /// <summary>
        /// إضافة بيانات تجريبية للاختبارات
        /// </summary>
        protected virtual void SeedTestData()
        {
            var suppliers = new List<Supplier>
            {
                new Supplier
                {
                    Id = 1,
                    Name = "مورد تجريبي 1",
                    ContactPerson = "أحمد محمد",
                    Phone = "***********",
                    Email = "<EMAIL>",
                    Address = "القاهرة، مصر",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new Supplier
                {
                    Id = 2,
                    Name = "مورد تجريبي 2",
                    ContactPerson = "محمد أحمد",
                    Phone = "01987654321",
                    Email = "<EMAIL>",
                    Address = "الإسكندرية، مصر",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-20)
                }
            };

            var invoices = new List<Invoice>
            {
                new Invoice
                {
                    Id = 1,
                    InvoiceNumber = "INV-001",
                    SupplierId = 1,
                    Amount = 1000,
                    PaidAmount = 0,
                    Status = InvoiceStatus.Unpaid,
                    InvoiceDate = DateTime.Now.AddDays(-10),
                    DueDate = DateTime.Now.AddDays(20),
                    Description = "فاتورة تجريبية 1",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-10)
                },
                new Invoice
                {
                    Id = 2,
                    InvoiceNumber = "INV-002",
                    SupplierId = 1,
                    Amount = 2000,
                    PaidAmount = 1000,
                    Status = InvoiceStatus.PartiallyPaid,
                    InvoiceDate = DateTime.Now.AddDays(-5),
                    DueDate = DateTime.Now.AddDays(25),
                    Description = "فاتورة تجريبية 2",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-5)
                },
                new Invoice
                {
                    Id = 3,
                    InvoiceNumber = "INV-003",
                    SupplierId = 2,
                    Amount = 1500,
                    PaidAmount = 1500,
                    Status = InvoiceStatus.Paid,
                    InvoiceDate = DateTime.Now.AddDays(-15),
                    DueDate = DateTime.Now.AddDays(-5),
                    Description = "فاتورة تجريبية 3",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-15)
                },
                new Invoice
                {
                    Id = 4,
                    InvoiceNumber = "INV-004",
                    SupplierId = 2,
                    Amount = 3000,
                    PaidAmount = 0,
                    Status = InvoiceStatus.Unpaid,
                    InvoiceDate = DateTime.Now.AddDays(-20),
                    DueDate = DateTime.Now.AddDays(-10), // متأخرة
                    Description = "فاتورة متأخرة",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-20)
                }
            };

            var payments = new List<Payment>
            {
                new Payment
                {
                    Id = 1,
                    ReceiptNumber = "REC-001",
                    InvoiceId = 2,
                    Amount = 1000,
                    PaymentDate = DateTime.Now.AddDays(-3),
                    Method = PaymentMethod.Cash,
                    Status = PaymentStatus.PartialPayment,
                    Notes = "دفعة جزئية",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-3)
                },
                new Payment
                {
                    Id = 2,
                    ReceiptNumber = "REC-002",
                    InvoiceId = 3,
                    Amount = 1500,
                    PaymentDate = DateTime.Now.AddDays(-12),
                    Method = PaymentMethod.BankTransfer,
                    Status = PaymentStatus.FullPayment,
                    Notes = "دفعة كاملة",
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddDays(-12)
                }
            };

            Context.Suppliers.AddRange(suppliers);
            Context.Invoices.AddRange(invoices);
            Context.Payments.AddRange(payments);
            Context.SaveChanges();
        }

        /// <summary>
        /// إنشاء مورد تجريبي جديد
        /// </summary>
        protected Supplier CreateTestSupplier(string name = "مورد تجريبي")
        {
            return new Supplier
            {
                Name = name,
                ContactPerson = "شخص مسؤول",
                Phone = "***********",
                Email = "<EMAIL>",
                Address = "عنوان تجريبي",
                IsActive = true,
                CreatedDate = DateTime.Now
            };
        }

        /// <summary>
        /// إنشاء فاتورة تجريبية جديدة
        /// </summary>
        protected Invoice CreateTestInvoice(int supplierId = 1, string invoiceNumber = "TEST-001", decimal amount = 1000)
        {
            return new Invoice
            {
                InvoiceNumber = invoiceNumber,
                SupplierId = supplierId,
                Amount = amount,
                Status = InvoiceStatus.Unpaid,
                InvoiceDate = DateTime.Now,
                DueDate = DateTime.Now.AddDays(30),
                Description = "فاتورة تجريبية",
                IsActive = true,
                CreatedDate = DateTime.Now
            };
        }

        /// <summary>
        /// إنشاء وصل دفع تجريبي جديد
        /// </summary>
        protected Payment CreateTestPayment(int invoiceId = 1, string receiptNumber = "TEST-REC-001", decimal amount = 500)
        {
            return new Payment
            {
                ReceiptNumber = receiptNumber,
                InvoiceId = invoiceId,
                Amount = amount,
                PaymentDate = DateTime.Now,
                Method = PaymentMethod.Cash,
                Status = PaymentStatus.FullPayment,
                Notes = "وصل تجريبي",
                IsActive = true,
                CreatedDate = DateTime.Now
            };
        }

        /// <summary>
        /// تنظيف قاعدة البيانات
        /// </summary>
        protected void ClearDatabase()
        {
            Context.Payments.RemoveRange(Context.Payments);
            Context.Invoices.RemoveRange(Context.Invoices);
            Context.Suppliers.RemoveRange(Context.Suppliers);
            Context.SaveChanges();
        }

        /// <summary>
        /// إعادة تحميل البيانات من قاعدة البيانات
        /// </summary>
        protected void RefreshContext()
        {
            Context.ChangeTracker.Clear();
        }

        public virtual void Dispose()
        {
            Context?.Dispose();
        }
    }
}
