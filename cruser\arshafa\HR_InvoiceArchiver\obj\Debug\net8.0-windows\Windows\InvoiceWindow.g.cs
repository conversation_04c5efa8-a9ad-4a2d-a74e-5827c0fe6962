﻿#pragma checksum "..\..\..\..\Windows\InvoiceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "AC7A2F43445B3C1BF7FF4B4F5FABCF0A0459E3D1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using HR_InvoiceArchiver.Controls;
using HR_InvoiceArchiver.Converters;
using HR_InvoiceArchiver.Windows;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Windows {
    
    
    /// <summary>
    /// InvoiceWindow
    /// </summary>
    public partial class InvoiceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 50 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainContentGrid;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearFilterButton;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecordCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilterStatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\Windows\InvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HR_InvoiceArchiver.Controls.InvoiceFormOverlay InvoiceFormOverlay;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/windows/invoicewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\InvoiceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainContentGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 107 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 116 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 124 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.FromDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 131 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.ToDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ClearFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 136 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.ClearFilterButton.Click += new System.Windows.RoutedEventHandler(this.ClearFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AddInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 160 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.AddInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.AddInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.EditInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.EditInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.EditInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.DeleteInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 172 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.DeleteInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.DeleteInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 191 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.InvoicesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.InvoicesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 192 "..\..\..\..\Windows\InvoiceWindow.xaml"
            this.InvoicesDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.InvoicesDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 17:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.RecordCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.FilterStatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.TotalAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.RemainingAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.InvoiceFormOverlay = ((HR_InvoiceArchiver.Controls.InvoiceFormOverlay)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 14:
            
            #line 258 "..\..\..\..\Windows\InvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 263 "..\..\..\..\Windows\InvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 269 "..\..\..\..\Windows\InvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintInvoiceButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

