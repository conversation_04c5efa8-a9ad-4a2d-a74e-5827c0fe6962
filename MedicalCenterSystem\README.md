# نظام إدارة المركز الطبي

نظام شامل لإدارة المراكز الطبية مبني باستخدام ASP.NET Core MVC مع Entity Framework Core وSQL Server.

## الميزات الرئيسية

### 🏥 إدارة المراجعين
- تسجيل المراجعين مع ترقيم تلقائي حسب الطبيب والتاريخ
- تتبع معلومات المريض الكاملة (الاسم، العمر، المحافظة، الهاتف)
- ربط المراجع بالطبيب المختص
- تسجيل التشخيص وعدد الزيارة

### 👨‍⚕️ إدارة الأطباء
- إضافة وإدارة الأطباء والتخصصات
- ربط الأطباء بالخدمات الطبية
- تحديد نسب الأطباء من كل خدمة
- تفعيل/إلغاء تفعيل الأطباء

### 🏥 الخدمات الطبية
- إدارة الخدمات الطبية (كشفية، فحوصات، تحاليل، أشعة)
- تصنيف الخدمات (مباشرة أو تحويل)
- تحديد الأسعار الافتراضية
- تمييز خدمات المركز عن الخدمات الخارجية

### 💰 نظام المدفوعات
#### المدفوعات الرئيسية
- تسجيل أجور الكشف والفحص
- حساب حصة الطبيب والمركز تلقائياً
- تسجيل اسم الكاشير والملاحظات

#### مدفوعات التحويلات
- تسجيل مدفوعات الخدمات الإضافية
- ربط التحويلات بالمراجع والخدمة
- حساب حصص الأطباء من التحويلات

### 📊 نظام التقارير
- **تقرير وارد المركز**: إجمالي الوارد من جميع الأقسام
- **تقرير تحويلات الأطباء**: تحويلات كل طبيب والخدمات المحولة
- **التقرير اليومي**: ملخص شامل لأنشطة اليوم
- **تقرير المراجع التفصيلي**: تفاصيل مراجع معين وجميع مدفوعاته

### 🔐 نظام المستخدمين
- تسجيل دخول آمن
- أدوار مختلفة (مدير، استقبال، كاشير)
- إدارة الصلاحيات
- تتبع آخر تسجيل دخول

## التقنيات المستخدمة

- **Backend**: ASP.NET Core 9.0 MVC
- **Database**: Entity Framework Core مع SQL Server
- **Frontend**: Bootstrap 5, HTML5, CSS3, JavaScript
- **UI**: واجهة باللغة العربية مع دعم RTL

## متطلبات التشغيل

- .NET 9.0 SDK
- SQL Server أو SQL Server LocalDB
- Visual Studio 2022 أو VS Code

## التثبيت والتشغيل

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd MedicalCenterSystem
```

2. **تحديث قاعدة البيانات**
```bash
dotnet ef database update
```

3. **تشغيل المشروع**
```bash
dotnet run
```

4. **الوصول للنظام**
- افتح المتصفح على: `http://localhost:5000`
- استخدم أحد الحسابات التجريبية للدخول

## الحسابات التجريبية

| اسم المستخدم | كلمة المرور | الدور |
|---------------|-------------|-------|
| admin | admin123 | مدير |
| reception | reception123 | استقبال |
| cashier | cashier123 | كاشير |

## هيكل قاعدة البيانات

### الجداول الرئيسية
- **Doctors**: معلومات الأطباء
- **MedicalServices**: الخدمات الطبية
- **DoctorServices**: ربط الأطباء بالخدمات والنسب
- **PatientVisits**: المراجعين
- **MainPayments**: المدفوعات الرئيسية
- **ReferralPayments**: مدفوعات التحويلات
- **UserAccounts**: حسابات المستخدمين

## الميزات المتقدمة

### ترقيم المراجعين التلقائي
- يتم إنشاء رقم مراجع تلقائياً لكل طبيب يومياً
- يبدأ الترقيم من 1 لكل طبيب في كل يوم جديد
- ضمان عدم تكرار الأرقام

### حساب الحصص التلقائي
- حساب حصة الطبيب والمركز تلقائياً
- دعم نسب مختلفة لكل طبيب وخدمة
- مرونة في تحديد النسب

### التقارير المتقدمة
- تقارير قابلة للتخصيص حسب التاريخ
- إحصائيات مفصلة ومرئية
- تجميع البيانات حسب الطبيب والخدمة

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

**تم تطوير هذا النظام لتسهيل إدارة المراكز الطبية وتحسين كفاءة العمل.**
