using System.Drawing;
using System.Drawing.Drawing2D;

namespace MedicalCenterWinForms.Helpers
{
    public static class MaterialDesignHelper
    {
        // Ultra Modern Medical Design Color Palette - 2025 Style
        public static class Colors
        {
            // Primary Colors - Medical Blue with Modern Gradient
            public static readonly Color Primary = Color.FromArgb(37, 99, 235);       // Medical Blue 600
            public static readonly Color PrimaryDark = Color.FromArgb(29, 78, 216);   // Medical Blue 700
            public static readonly Color PrimaryLight = Color.FromArgb(147, 197, 253); // Medical Blue 300
            public static readonly Color PrimaryAccent = Color.FromArgb(96, 165, 250); // Medical Blue 400
            public static readonly Color PrimaryUltraLight = Color.FromArgb(239, 246, 255); // Medical Blue 50

            // Secondary Colors - Modern Teal/Cyan for Medical Feel
            public static readonly Color Secondary = Color.FromArgb(6, 182, 212);     // Cyan 500
            public static readonly Color SecondaryDark = Color.FromArgb(8, 145, 178); // Cyan 600
            public static readonly Color SecondaryLight = Color.FromArgb(165, 243, 252); // Cyan 200
            public static readonly Color SecondaryAccent = Color.FromArgb(34, 211, 238); // Cyan 400

            // Accent Colors - Modern Green for Success/Health
            public static readonly Color Accent = Color.FromArgb(16, 185, 129);       // Emerald 500
            public static readonly Color AccentDark = Color.FromArgb(5, 150, 105);    // Emerald 600
            public static readonly Color AccentLight = Color.FromArgb(167, 243, 208); // Emerald 200
            public static readonly Color AccentUltraLight = Color.FromArgb(236, 253, 245); // Emerald 50

            // Surface Colors - Ultra Clean Medical White/Gray
            public static readonly Color Surface = Color.FromArgb(255, 255, 255);     // Pure White
            public static readonly Color SurfaceElevated = Color.FromArgb(249, 250, 251); // Gray 50
            public static readonly Color Background = Color.FromArgb(243, 244, 246);  // Gray 100
            public static readonly Color BackgroundSecondary = Color.FromArgb(229, 231, 235); // Gray 200
            public static readonly Color Card = Color.FromArgb(255, 255, 255);       // Pure White
            public static readonly Color CardElevated = Color.FromArgb(249, 250, 251); // Gray 50

            // Text Colors - High Contrast for Medical Readability
            public static readonly Color TextPrimary = Color.FromArgb(17, 24, 39);    // Gray 900
            public static readonly Color TextSecondary = Color.FromArgb(75, 85, 99);  // Gray 600
            public static readonly Color TextTertiary = Color.FromArgb(107, 114, 128); // Gray 500
            public static readonly Color TextHint = Color.FromArgb(156, 163, 175);    // Gray 400
            public static readonly Color TextOnPrimary = Color.FromArgb(255, 255, 255); // Pure White
            public static readonly Color TextOnDark = Color.FromArgb(249, 250, 251);  // Gray 50

            // Status Colors - Medical Standard Colors
            public static readonly Color Success = Color.FromArgb(16, 185, 129);      // Emerald 500
            public static readonly Color SuccessLight = Color.FromArgb(167, 243, 208); // Emerald 200
            public static readonly Color Warning = Color.FromArgb(245, 158, 11);      // Amber 500
            public static readonly Color WarningLight = Color.FromArgb(253, 230, 138); // Amber 200
            public static readonly Color Error = Color.FromArgb(239, 68, 68);         // Red 500
            public static readonly Color ErrorLight = Color.FromArgb(252, 165, 165);  // Red 200
            public static readonly Color Info = Color.FromArgb(59, 130, 246);         // Blue 500
            public static readonly Color InfoLight = Color.FromArgb(191, 219, 254);   // Blue 200
            public static readonly Color Danger = Color.FromArgb(239, 68, 68);        // Red 500 (alias for Error)

            // Sidebar Colors - Modern Professional Dark Theme
            public static readonly Color SidebarBg = Color.FromArgb(31, 41, 55);      // Gray 800
            public static readonly Color SidebarBgGradient = Color.FromArgb(17, 24, 39); // Gray 900
            public static readonly Color SidebarHover = Color.FromArgb(55, 65, 81);   // Gray 700
            public static readonly Color SidebarActive = Color.FromArgb(37, 99, 235); // Medical Blue 600
            public static readonly Color SidebarActiveGlow = Color.FromArgb(37, 99, 235, 20); // Medical Blue with opacity
            public static readonly Color SidebarText = Color.FromArgb(209, 213, 219); // Gray 300
            public static readonly Color SidebarTextActive = Color.FromArgb(255, 255, 255); // White
            public static readonly Color SidebarTextHover = Color.FromArgb(243, 244, 246); // Gray 100

            // Divider and Border - Subtle Medical Theme
            public static readonly Color Divider = Color.FromArgb(229, 231, 235);     // Gray 200
            public static readonly Color Border = Color.FromArgb(209, 213, 219);      // Gray 300
            public static readonly Color BorderLight = Color.FromArgb(243, 244, 246); // Gray 100
            public static readonly Color BorderFocus = Color.FromArgb(37, 99, 235);   // Medical Blue 600

            // Hover and Focus States - Interactive Medical Theme
            public static readonly Color Hover = Color.FromArgb(249, 250, 251);       // Gray 50
            public static readonly Color HoverSecondary = Color.FromArgb(243, 244, 246); // Gray 100
            public static readonly Color Focus = Color.FromArgb(37, 99, 235, 15);     // Medical Blue with opacity
            public static readonly Color FocusRing = Color.FromArgb(37, 99, 235, 50); // Medical Blue focus ring
            public static readonly Color Pressed = Color.FromArgb(29, 78, 216);       // Medical Blue 700

            // Gradient Colors for Modern Medical Effects
            public static readonly Color GradientStart = Color.FromArgb(37, 99, 235); // Medical Blue 600
            public static readonly Color GradientMiddle = Color.FromArgb(6, 182, 212); // Cyan 500
            public static readonly Color GradientEnd = Color.FromArgb(16, 185, 129);   // Emerald 500

            // Special Medical Theme Colors
            public static readonly Color MedicalPrimary = Color.FromArgb(37, 99, 235); // Medical Blue
            public static readonly Color MedicalSecondary = Color.FromArgb(6, 182, 212); // Medical Cyan
            public static readonly Color MedicalAccent = Color.FromArgb(16, 185, 129);  // Medical Green
            public static readonly Color MedicalNeutral = Color.FromArgb(107, 114, 128); // Medical Gray
        }

        // Material Design Shadows
        public static class Shadows
        {
            public static readonly Color Shadow1 = Color.FromArgb(20, 0, 0, 0);       // 8% opacity
            public static readonly Color Shadow2 = Color.FromArgb(35, 0, 0, 0);       // 14% opacity
            public static readonly Color Shadow3 = Color.FromArgb(50, 0, 0, 0);       // 20% opacity
        }

        // Material Design Elevations
        public static void ApplyElevation(Control control, int elevation = 2)
        {
            control.Paint += (sender, e) =>
            {
                var rect = control.ClientRectangle;
                using var shadowBrush = new SolidBrush(elevation switch
                {
                    1 => Shadows.Shadow1,
                    2 => Shadows.Shadow2,
                    _ => Shadows.Shadow3
                });

                // Draw shadow
                var shadowRect = new Rectangle(rect.X + elevation, rect.Y + elevation, 
                                             rect.Width - elevation, rect.Height - elevation);
                e.Graphics.FillRectangle(shadowBrush, shadowRect);
            };
        }

        // Create Ultra Modern Medical Button with Advanced Styling
        public static Button CreateMaterialButton(string text, Color backgroundColor, Color textColor,
                                                 EventHandler clickHandler, int width = 140, int height = 48)
        {
            var button = new Button
            {
                Text = text,
                BackColor = backgroundColor,
                ForeColor = textColor,
                FlatStyle = FlatStyle.Flat,
                Font = ArabicFontHelper.GetArabicButtonFont(12F),
                Size = new Size(width, height),
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter,
                Margin = new Padding(8)
            };

            // Ultra modern button styling with no borders
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.BorderColor = backgroundColor;

            // Simplified modern button styling for better performance
            button.Paint += (sender, e) =>
            {
                var rect = button.ClientRectangle;
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // Simple rounded rectangle background
                using var buttonPath = CreateRoundedRectanglePath(rect, 8);
                using var backgroundBrush = new SolidBrush(button.BackColor);
                e.Graphics.FillPath(backgroundBrush, buttonPath);

                // Simple border
                using var borderPen = new Pen(DarkenColor(button.BackColor, 0.1f), 1);
                e.Graphics.DrawPath(borderPen, buttonPath);

                // Draw text
                var textRect = new Rectangle(0, 0, rect.Width, rect.Height);
                TextRenderer.DrawText(e.Graphics, button.Text, button.Font, textRect,
                    button.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
            };

            // Simplified hover effects
            var originalColor = backgroundColor;

            button.MouseEnter += (s, e) =>
            {
                button.BackColor = LightenColor(originalColor, 0.1f);
            };

            button.MouseLeave += (s, e) =>
            {
                button.BackColor = originalColor;
            };

            button.MouseDown += (s, e) =>
            {
                button.BackColor = DarkenColor(originalColor, 0.1f);
            };

            button.MouseUp += (s, e) =>
            {
                button.BackColor = originalColor;
            };

            if (clickHandler != null)
                button.Click += clickHandler;

            return button;
        }

        // Create Simple and Reliable Sidebar Button - COMPLETELY REWRITTEN
        public static Button CreateSidebarButton(string text, string icon, EventHandler clickHandler,
                                               int width = 240, int height = 56)
        {
            var button = new Button
            {
                Text = $"  {icon}  {text}",
                BackColor = Colors.SidebarBg,
                ForeColor = Colors.SidebarText,
                FlatStyle = FlatStyle.Standard, // Use Standard instead of Flat
                Font = ArabicFontHelper.GetArabicButtonFont(13F),
                Size = new Size(width, height),
                UseVisualStyleBackColor = true, // Let system handle base styling
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleLeft,
                Padding = new Padding(24, 0, 16, 0),
                Margin = new Padding(8, 4, 8, 4),
                TabStop = false
            };

            // NO FlatAppearance configuration - let system handle it

            // COMPLETELY REMOVE ALL CUSTOM HOVER EFFECTS
            // Let the system handle hover naturally
            var isActive = false;

            // Simple click handler - no custom styling
            if (clickHandler != null)
            {
                button.Click += clickHandler;
            }

            // Simple active state management through Tag
            button.Tag = new Action<bool>(active =>
            {
                isActive = active;
                // Only change colors, let system handle the rest
                if (active)
                {
                    button.BackColor = Colors.SidebarActive;
                    button.ForeColor = Colors.SidebarTextActive;
                }
                else
                {
                    button.BackColor = Colors.SidebarBg;
                    button.ForeColor = Colors.SidebarText;
                }
            });

            return button;
        }

        // MODERN PANEL-BASED SIDEBAR BUTTON - 2025 Design
        public static Panel CreateModernSidebarPanel(string text, string icon, EventHandler clickHandler,
                                                    int width = 240, int height = 64)
        {
            var mainPanel = new Panel
            {
                Size = new Size(width, height),
                BackColor = Colors.SidebarBg,
                Cursor = Cursors.Hand,
                Margin = new Padding(12, 6, 12, 6),
                Padding = new Padding(0)
            };

            // Icon Panel (Left side)
            var iconPanel = new Panel
            {
                Size = new Size(60, height),
                BackColor = Color.Transparent,
                Dock = DockStyle.Left
            };

            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 18F, FontStyle.Regular),
                ForeColor = Colors.SidebarText,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent
            };

            // Text Panel (Right side)
            var textPanel = new Panel
            {
                BackColor = Color.Transparent,
                Dock = DockStyle.Fill
            };

            var textLabel = new Label
            {
                Text = text,
                Font = ArabicFontHelper.GetArabicButtonFont(14F, FontStyle.Regular),
                ForeColor = Colors.SidebarText,
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill,
                Padding = new Padding(8, 0, 16, 0),
                BackColor = Color.Transparent,
                RightToLeft = RightToLeft.Yes
            };

            // Accent Line (Modern touch)
            var accentLine = new Panel
            {
                Size = new Size(4, height - 16),
                BackColor = Color.Transparent,
                Location = new Point(0, 8),
                Visible = false
            };

            // Assembly
            iconPanel.Controls.Add(iconLabel);
            textPanel.Controls.Add(textLabel);
            mainPanel.Controls.Add(accentLine);
            mainPanel.Controls.Add(textPanel);
            mainPanel.Controls.Add(iconPanel);

            // State management
            var isActive = false;
            var isHovered = false;

            // Modern hover effects with smooth transitions
            Action updateVisuals = () =>
            {
                if (isActive)
                {
                    mainPanel.BackColor = Colors.SidebarActive;
                    iconLabel.ForeColor = Colors.SidebarTextActive;
                    textLabel.ForeColor = Colors.SidebarTextActive;
                    accentLine.BackColor = Colors.MedicalAccent;
                    accentLine.Visible = true;
                }
                else if (isHovered)
                {
                    mainPanel.BackColor = Colors.SidebarHover;
                    iconLabel.ForeColor = Colors.SidebarTextHover;
                    textLabel.ForeColor = Colors.SidebarTextHover;
                    accentLine.Visible = false;
                }
                else
                {
                    mainPanel.BackColor = Colors.SidebarBg;
                    iconLabel.ForeColor = Colors.SidebarText;
                    textLabel.ForeColor = Colors.SidebarText;
                    accentLine.Visible = false;
                }
            };

            // Mouse events for all components
            var controls = new Control[] { mainPanel, iconPanel, iconLabel, textPanel, textLabel };

            foreach (var control in controls)
            {
                control.MouseEnter += (s, e) =>
                {
                    isHovered = true;
                    updateVisuals();
                };

                control.MouseLeave += (s, e) =>
                {
                    isHovered = false;
                    updateVisuals();
                };

                if (clickHandler != null)
                {
                    control.Click += (s, e) => clickHandler(mainPanel, e);
                }
            }

            // Active state management
            mainPanel.Tag = new Action<bool>(active =>
            {
                isActive = active;
                updateVisuals();
            });

            // Initial state
            updateVisuals();

            return mainPanel;
        }

        // Modern Button Creator
        public static Button CreateModernButton(string text, Color backgroundColor, int width = 120, int height = 35)
        {
            var button = new Button
            {
                Text = text,
                Size = new Size(width, height),
                BackColor = backgroundColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = ArabicFontHelper.GetArabicFont(10F),
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = DarkenColor(backgroundColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = DarkenColor(backgroundColor, 0.2f);

            return button;
        }

        // Add Shadow Effect to Panel
        public static void AddShadow(Panel panel)
        {
            // Simple shadow effect using border
            panel.Paint += (sender, e) =>
            {
                var rect = panel.ClientRectangle;
                rect.Width -= 1;
                rect.Height -= 1;

                using (var pen = new Pen(Color.FromArgb(30, 0, 0, 0), 1))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };
        }



        // Helper method to reset all sidebar buttons
        public static void ResetSidebarButtons(params Button[] buttons)
        {
            foreach (var btn in buttons)
            {
                if (btn != null)
                {
                    btn.BackColor = Colors.SidebarBg;
                    btn.ForeColor = Colors.SidebarText;
                }
            }
        }

        // Create Ultra Modern Medical Card with Advanced Styling
        public static Panel CreateMaterialCard(int elevation = 2)
        {
            var panel = new Panel
            {
                BackColor = Colors.Card,
                Padding = new Padding(32),
                BorderStyle = BorderStyle.None,
                Margin = new Padding(16)
            };

            // Simplified card styling for better performance
            panel.Paint += (sender, e) =>
            {
                var rect = panel.ClientRectangle;
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // Simple shadow for elevation
                if (elevation > 0)
                {
                    var shadowRect = new Rectangle(rect.X + 2, rect.Y + 2, rect.Width - 2, rect.Height - 2);
                    using var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0));
                    e.Graphics.FillRectangle(shadowBrush, shadowRect);
                }

                // Main card background
                using var cardPath = CreateRoundedRectanglePath(rect, 8);
                using var cardBrush = new SolidBrush(Colors.Card);
                e.Graphics.FillPath(cardBrush, cardPath);

                // Simple border
                using var borderPen = new Pen(Colors.BorderLight, 1);
                e.Graphics.DrawPath(borderPen, cardPath);

                // Top accent line
                using var accentBrush = new SolidBrush(Colors.MedicalPrimary);
                e.Graphics.FillRectangle(accentBrush, rect.X, rect.Y, rect.Width, 3);
            };

            return panel;
        }

        // Create Ultra Modern Medical TextBox with Advanced Styling
        public static TextBox CreateMaterialTextBox(string placeholder = "", bool isPassword = false)
        {
            var textBox = new TextBox
            {
                Font = ArabicFontHelper.GetArabicTextBoxFont(13F),
                BorderStyle = BorderStyle.None,
                BackColor = Colors.Surface,
                ForeColor = Colors.TextPrimary,
                PlaceholderText = placeholder,
                TextAlign = HorizontalAlignment.Right,
                UseSystemPasswordChar = isPassword,
                Height = 48,
                Padding = new Padding(16, 12, 16, 12),
                Margin = new Padding(8)
            };

            // Create container panel for custom border and styling
            var container = new Panel
            {
                Height = textBox.Height + 8,
                BorderStyle = BorderStyle.None,
                BackColor = Color.Transparent,
                Padding = new Padding(4)
            };

            textBox.Dock = DockStyle.Fill;
            container.Controls.Add(textBox);

            // Advanced styling with focus effects
            var isFocused = false;
            var hasError = false;

            container.Paint += (sender, e) =>
            {
                var rect = container.ClientRectangle;
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // Create rounded rectangle for textbox
                var textBoxRect = new Rectangle(rect.X + 2, rect.Y + 2, rect.Width - 4, rect.Height - 4);
                using var textBoxPath = CreateRoundedRectanglePath(textBoxRect, 12);

                // Draw background with subtle gradient
                using var backgroundBrush = new LinearGradientBrush(
                    textBoxRect,
                    Colors.Surface,
                    Colors.SurfaceElevated,
                    LinearGradientMode.Vertical);
                e.Graphics.FillPath(backgroundBrush, textBoxPath);

                // Draw border based on state
                Color borderColor;
                float borderWidth;

                if (hasError)
                {
                    borderColor = Colors.Error;
                    borderWidth = 2f;
                }
                else if (isFocused)
                {
                    borderColor = Colors.BorderFocus;
                    borderWidth = 2f;

                    // Add focus glow effect
                    using var glowPen = new Pen(Colors.FocusRing, 4f);
                    e.Graphics.DrawPath(glowPen, textBoxPath);
                }
                else
                {
                    borderColor = Colors.Border;
                    borderWidth = 1f;
                }

                using var borderPen = new Pen(borderColor, borderWidth);
                e.Graphics.DrawPath(borderPen, textBoxPath);
            };

            // Enhanced focus effects
            textBox.Enter += (s, e) =>
            {
                isFocused = true;
                textBox.BackColor = Colors.SurfaceElevated;
                container.Invalidate();
            };

            textBox.Leave += (s, e) =>
            {
                isFocused = false;
                textBox.BackColor = Colors.Surface;
                container.Invalidate();
            };

            // Hover effects
            textBox.MouseEnter += (s, e) =>
            {
                if (!isFocused)
                {
                    textBox.BackColor = Colors.Hover;
                }
            };

            textBox.MouseLeave += (s, e) =>
            {
                if (!isFocused)
                {
                    textBox.BackColor = Colors.Surface;
                }
            };

            // Return the textbox (the container will be handled by the calling code if needed)
            return textBox;
        }

        // Create Ultra Modern Medical DataGridView with Advanced Styling
        public static DataGridView CreateMaterialDataGridView()
        {
            var dgv = new DataGridView
            {
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Colors.Background,
                BorderStyle = BorderStyle.None,
                ReadOnly = true,
                MultiSelect = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                RowHeadersVisible = false,
                EnableHeadersVisualStyles = false,
                GridColor = Colors.Divider,
                Font = ArabicFontHelper.GetArabicFont(11F),
                CellBorderStyle = DataGridViewCellBorderStyle.None,
                Margin = new Padding(16)
            };

            // Ultra modern header styling with medical theme
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Colors.MedicalPrimary;
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Colors.TextOnPrimary;
            dgv.ColumnHeadersDefaultCellStyle.Font = ArabicFontHelper.GetArabicHeaderFont(13F, FontStyle.Bold);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersDefaultCellStyle.Padding = new Padding(16, 12, 16, 12);
            dgv.ColumnHeadersDefaultCellStyle.WrapMode = DataGridViewTriState.False;
            dgv.ColumnHeadersHeight = 60;
            dgv.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;

            // Modern row styling with enhanced readability
            dgv.DefaultCellStyle.BackColor = Colors.Surface;
            dgv.DefaultCellStyle.ForeColor = Colors.TextPrimary;
            dgv.DefaultCellStyle.SelectionBackColor = Colors.PrimaryLight;
            dgv.DefaultCellStyle.SelectionForeColor = Colors.TextPrimary;
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgv.DefaultCellStyle.Padding = new Padding(16, 12, 16, 12);
            dgv.DefaultCellStyle.WrapMode = DataGridViewTriState.False;
            dgv.RowTemplate.Height = 64;

            // Enhanced alternating row colors for better readability
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Colors.SurfaceElevated;
            dgv.AlternatingRowsDefaultCellStyle.SelectionBackColor = Colors.PrimaryLight;
            dgv.AlternatingRowsDefaultCellStyle.SelectionForeColor = Colors.TextPrimary;

            // Add custom painting for modern look
            dgv.CellPainting += (sender, e) =>
            {
                if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
                {
                    // Custom cell painting for rounded corners and modern look
                    e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                    // Fill cell background
                    var cellRect = e.CellBounds;
                    using var cellBrush = new SolidBrush(e.CellStyle.BackColor);
                    e.Graphics.FillRectangle(cellBrush, cellRect);

                    // Draw selection highlight - Simplified for better performance
                    if ((e.State & DataGridViewElementStates.Selected) != 0)
                    {
                        var selectionRect = new Rectangle(cellRect.X + 2, cellRect.Y + 1, cellRect.Width - 4, cellRect.Height - 2);
                        using var selectionBrush = new SolidBrush(Colors.PrimaryLight);
                        e.Graphics.FillRectangle(selectionBrush, selectionRect);
                    }

                    // Draw cell content
                    if (e.Value != null)
                    {
                        var textRect = new Rectangle(cellRect.X + 16, cellRect.Y, cellRect.Width - 32, cellRect.Height);
                        TextRenderer.DrawText(e.Graphics, e.Value.ToString(), e.CellStyle.Font, textRect,
                            e.CellStyle.ForeColor, TextFormatFlags.Right | TextFormatFlags.VerticalCenter | TextFormatFlags.TextBoxControl);
                    }

                    e.Handled = true;
                }
                else if (e.RowIndex == -1 && e.ColumnIndex >= 0)
                {
                    // Custom header painting
                    e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                    var headerRect = e.CellBounds;

                    // Draw gradient background for header
                    using var headerBrush = new LinearGradientBrush(
                        headerRect,
                        Colors.MedicalPrimary,
                        DarkenColor(Colors.MedicalPrimary, 0.1f),
                        LinearGradientMode.Vertical);
                    e.Graphics.FillRectangle(headerBrush, headerRect);

                    // Draw header text
                    if (e.Value != null)
                    {
                        var textRect = new Rectangle(headerRect.X + 16, headerRect.Y, headerRect.Width - 32, headerRect.Height);
                        TextRenderer.DrawText(e.Graphics, e.Value.ToString(), e.CellStyle.Font, textRect,
                            e.CellStyle.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter | TextFormatFlags.TextBoxControl);
                    }

                    e.Handled = true;
                }
            };

            // Enhanced hover effects - Fixed
            dgv.CellMouseEnter += (sender, e) =>
            {
                if (e.RowIndex >= 0 && e.RowIndex < dgv.Rows.Count)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor = Colors.Hover;
                }
            };

            dgv.CellMouseLeave += (sender, e) =>
            {
                if (e.RowIndex >= 0 && e.RowIndex < dgv.Rows.Count)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor =
                        e.RowIndex % 2 == 0 ? Colors.Surface : Colors.SurfaceElevated;
                }
            };

            ArabicFontHelper.SetupArabicDataGridView(dgv);

            return dgv;
        }

        // Create Modern Medical ComboBox
        public static ComboBox CreateMaterialComboBox()
        {
            var comboBox = new ComboBox
            {
                Font = ArabicFontHelper.GetArabicFont(12F),
                BackColor = Colors.Surface,
                ForeColor = Colors.TextPrimary,
                FlatStyle = FlatStyle.Flat,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Height = 48,
                RightToLeft = RightToLeft.Yes
            };

            return comboBox;
        }

        // Create Modern Medical Label
        public static Label CreateMaterialLabel(string text, bool isHeader = false)
        {
            var label = new Label
            {
                Text = text,
                Font = isHeader ? ArabicFontHelper.GetArabicHeaderFont(14F, FontStyle.Bold) : ArabicFontHelper.GetArabicFont(12F),
                ForeColor = isHeader ? Colors.MedicalPrimary : Colors.TextPrimary,
                BackColor = Color.Transparent,
                AutoSize = true,
                RightToLeft = RightToLeft.Yes,
                TextAlign = ContentAlignment.MiddleRight
            };

            return label;
        }

        // Create Modern Medical Progress Bar
        public static ProgressBar CreateMaterialProgressBar()
        {
            var progressBar = new ProgressBar
            {
                Style = ProgressBarStyle.Continuous,
                Height = 8,
                BackColor = Colors.Background,
                ForeColor = Colors.MedicalPrimary
            };

            // Custom painting for rounded progress bar
            progressBar.Paint += (sender, e) =>
            {
                var rect = progressBar.ClientRectangle;
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // Draw background
                using var backgroundPath = CreateRoundedRectanglePath(rect, 4);
                using var backgroundBrush = new SolidBrush(Colors.Background);
                e.Graphics.FillPath(backgroundBrush, backgroundPath);

                // Draw progress
                if (progressBar.Value > 0)
                {
                    var progressWidth = (int)((double)progressBar.Value / progressBar.Maximum * rect.Width);
                    var progressRect = new Rectangle(rect.X, rect.Y, progressWidth, rect.Height);
                    using var progressPath = CreateRoundedRectanglePath(progressRect, 4);
                    using var progressBrush = new LinearGradientBrush(
                        progressRect,
                        Colors.MedicalPrimary,
                        Colors.MedicalSecondary,
                        LinearGradientMode.Horizontal);
                    e.Graphics.FillPath(progressBrush, progressPath);
                }
            };

            return progressBar;
        }

        // Create Modern Medical Separator
        public static Panel CreateMaterialSeparator(bool isVertical = false)
        {
            var separator = new Panel
            {
                BackColor = Colors.Divider,
                Size = isVertical ? new Size(1, 100) : new Size(100, 1)
            };

            return separator;
        }

        // Helper methods for color manipulation
        public static Color LightenColor(Color color, float factor)
        {
            return Color.FromArgb(
                color.A,
                Math.Min(255, (int)(color.R + (255 - color.R) * factor)),
                Math.Min(255, (int)(color.G + (255 - color.G) * factor)),
                Math.Min(255, (int)(color.B + (255 - color.B) * factor))
            );
        }

        public static Color DarkenColor(Color color, float factor)
        {
            return Color.FromArgb(
                color.A,
                Math.Max(0, (int)(color.R * (1 - factor))),
                Math.Max(0, (int)(color.G * (1 - factor))),
                Math.Max(0, (int)(color.B * (1 - factor)))
            );
        }

        // Create color with opacity
        public static Color WithOpacity(Color color, int alpha)
        {
            return Color.FromArgb(alpha, color.R, color.G, color.B);
        }

        // Create rounded rectangle path
        public static GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
            path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
            path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90);
            path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90);
            path.CloseFigure();
            return path;
        }
    }
}
