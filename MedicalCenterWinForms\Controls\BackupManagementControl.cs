using System;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Threading.Tasks;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class BackupManagementControl : BaseUserControl
    {
        private BackupService _backupService;
        private SettingsService _settingsService;

        // UI Components
        private TabControl mainTabControl;
        private Panel headerPanel;

        // Tab Pages
        private TabPage tabBackup;
        private TabPage tabRestore;
        private TabPage tabSchedule;
        private TabPage tabHistory;

        // Backup Tab Controls
        private Panel backupPanel;
        private TextBox txtBackupPath;
        private Button btnBrowseBackup;
        private TextBox txtBackupDescription;
        private ComboBox cmbBackupType;
        private Button btnCreateBackup;
        private ProgressBar pbBackupProgress;
        private Label lblBackupStatus;

        // Restore Tab Controls
        private Panel restorePanel;
        private TextBox txtRestoreFile;
        private Button btnBrowseRestore;
        private Button btnVerifyBackup;
        private Button btnRestoreBackup;
        private Label lblRestoreInfo;
        private ProgressBar pbRestoreProgress;
        private Label lblRestoreStatus;

        // History Tab Controls
        private DataGridView dgvBackupHistory;
        private Button btnRefreshHistory;
        private Button btnDeleteBackup;
        private Button btnCleanOldBackups;

        public BackupManagementControl() : base()
        {
            InitializeComponent();
            InitializeServices();
            LoadData();
        }

        public BackupManagementControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            InitializeServices();
            LoadData();
        }

        private void InitializeServices()
        {
            _settingsService = new SettingsService(DatabaseService);
            _backupService = new BackupService(DatabaseService, _settingsService);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Set control properties
            this.Size = new Size(1200, 800);
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);
            this.RightToLeft = RightToLeft.Yes;

            CreateHeaderPanel();
            CreateMainTabControl();
            CreateTabPages();

            this.ResumeLayout(false);
        }

        private void CreateHeaderPanel()
        {
            headerPanel = new Panel
            {
                Location = new Point(10, 10),
                Size = new Size(1180, 60),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblTitle = new Label
            {
                Text = "💾 إدارة النسخ الاحتياطي والاستعادة",
                Location = new Point(20, 15),
                Size = new Size(300, 30),
                Font = ArabicFontHelper.GetArabicFont(14F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Primary
            };

            var lblLastBackup = new Label
            {
                Text = "آخر نسخة احتياطية: لم يتم إنشاء نسخة بعد",
                Location = new Point(400, 20),
                Size = new Size(300, 20),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary
            };

            headerPanel.Controls.AddRange(new Control[] { lblTitle, lblLastBackup });
            this.Controls.Add(headerPanel);
        }

        private void CreateMainTabControl()
        {
            mainTabControl = new TabControl
            {
                Location = new Point(10, 80),
                Size = new Size(1180, 710),
                Font = ArabicFontHelper.GetArabicFont(10F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                Appearance = TabAppearance.FlatButtons,
                SizeMode = TabSizeMode.Fixed,
                ItemSize = new Size(120, 30)
            };

            this.Controls.Add(mainTabControl);
        }

        private void CreateTabPages()
        {
            // Backup Tab
            tabBackup = new TabPage("إنشاء نسخة احتياطية");
            CreateBackupTab();
            mainTabControl.TabPages.Add(tabBackup);

            // Restore Tab
            tabRestore = new TabPage("استعادة النسخة");
            CreateRestoreTab();
            mainTabControl.TabPages.Add(tabRestore);

            // Schedule Tab
            tabSchedule = new TabPage("الجدولة");
            CreateScheduleTab();
            mainTabControl.TabPages.Add(tabSchedule);

            // History Tab
            tabHistory = new TabPage("السجل");
            CreateHistoryTab();
            mainTabControl.TabPages.Add(tabHistory);
        }

        private void CreateBackupTab()
        {
            backupPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Backup Path
            var lblBackupPath = new Label
            {
                Text = "مسار النسخة الاحتياطية:",
                Location = new Point(20, 20),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            txtBackupPath = ModernMedicalTheme.Components.CreateAdvancedTextBox("اختر مجلد النسخ الاحتياطية");
            txtBackupPath.Location = new Point(180, 20);
            txtBackupPath.Size = new Size(400, 25);

            btnBrowseBackup = ModernMedicalTheme.Components.CreateAdvancedButton(
                "تصفح", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnBrowseBackup.Location = new Point(590, 20);
            btnBrowseBackup.Size = new Size(80, 25);
            btnBrowseBackup.Click += BtnBrowseBackup_Click;

            // Backup Type
            var lblBackupType = new Label
            {
                Text = "نوع النسخة الاحتياطية:",
                Location = new Point(20, 60),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            cmbBackupType = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbBackupType.Location = new Point(180, 60);
            cmbBackupType.Size = new Size(200, 25);
            cmbBackupType.Items.AddRange(new object[] { "نسخة كاملة", "قاعدة البيانات فقط", "الإعدادات فقط" });
            cmbBackupType.SelectedIndex = 0;

            // Description
            var lblDescription = new Label
            {
                Text = "وصف النسخة الاحتياطية:",
                Location = new Point(20, 100),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            txtBackupDescription = ModernMedicalTheme.Components.CreateAdvancedTextBox("وصف اختياري للنسخة الاحتياطية");
            txtBackupDescription.Location = new Point(180, 100);
            txtBackupDescription.Size = new Size(400, 25);

            // Create Backup Button
            btnCreateBackup = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔄 إنشاء نسخة احتياطية", ModernMedicalTheme.Components.ButtonStyle.Success);
            btnCreateBackup.Location = new Point(20, 150);
            btnCreateBackup.Size = new Size(150, 40);
            btnCreateBackup.Click += BtnCreateBackup_Click;

            // Progress
            pbBackupProgress = new ProgressBar
            {
                Location = new Point(20, 210),
                Size = new Size(650, 25),
                Style = ProgressBarStyle.Continuous,
                Visible = false
            };

            lblBackupStatus = new Label
            {
                Text = "",
                Location = new Point(20, 245),
                Size = new Size(650, 25),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary
            };

            backupPanel.Controls.AddRange(new Control[]
            {
                lblBackupPath, txtBackupPath, btnBrowseBackup,
                lblBackupType, cmbBackupType,
                lblDescription, txtBackupDescription,
                btnCreateBackup, pbBackupProgress, lblBackupStatus
            });

            tabBackup.Controls.Add(backupPanel);
        }

        private void CreateRestoreTab()
        {
            restorePanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Restore File
            var lblRestoreFile = new Label
            {
                Text = "ملف النسخة الاحتياطية:",
                Location = new Point(20, 20),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            txtRestoreFile = ModernMedicalTheme.Components.CreateAdvancedTextBox("اختر ملف النسخة الاحتياطية");
            txtRestoreFile.Location = new Point(180, 20);
            txtRestoreFile.Size = new Size(400, 25);

            btnBrowseRestore = ModernMedicalTheme.Components.CreateAdvancedButton(
                "تصفح", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnBrowseRestore.Location = new Point(590, 20);
            btnBrowseRestore.Size = new Size(80, 25);
            btnBrowseRestore.Click += BtnBrowseRestore_Click;

            // Verify Button
            btnVerifyBackup = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔍 التحقق من النسخة", ModernMedicalTheme.Components.ButtonStyle.Secondary);
            btnVerifyBackup.Location = new Point(20, 70);
            btnVerifyBackup.Size = new Size(130, 35);
            btnVerifyBackup.Click += BtnVerifyBackup_Click;

            // Restore Button
            btnRestoreBackup = ModernMedicalTheme.Components.CreateAdvancedButton(
                "⚠️ استعادة النسخة", ModernMedicalTheme.Components.ButtonStyle.Warning);
            btnRestoreBackup.Location = new Point(160, 70);
            btnRestoreBackup.Size = new Size(130, 35);
            btnRestoreBackup.Click += BtnRestoreBackup_Click;
            btnRestoreBackup.Enabled = false;

            // Info Label
            lblRestoreInfo = new Label
            {
                Text = "يرجى اختيار ملف النسخة الاحتياطية والتحقق منه قبل الاستعادة",
                Location = new Point(20, 120),
                Size = new Size(650, 50),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary
            };

            // Progress
            pbRestoreProgress = new ProgressBar
            {
                Location = new Point(20, 180),
                Size = new Size(650, 25),
                Style = ProgressBarStyle.Continuous,
                Visible = false
            };

            lblRestoreStatus = new Label
            {
                Text = "",
                Location = new Point(20, 215),
                Size = new Size(650, 25),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary
            };

            restorePanel.Controls.AddRange(new Control[]
            {
                lblRestoreFile, txtRestoreFile, btnBrowseRestore,
                btnVerifyBackup, btnRestoreBackup, lblRestoreInfo,
                pbRestoreProgress, lblRestoreStatus
            });

            tabRestore.Controls.Add(restorePanel);
        }

        private void CreateScheduleTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            var lblScheduleInfo = new Label
            {
                Text = "ميزة الجدولة التلقائية ستكون متاحة في الإصدار القادم",
                Location = new Point(20, 20),
                Size = new Size(400, 25),
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary
            };

            panel.Controls.Add(lblScheduleInfo);
            tabSchedule.Controls.Add(panel);
        }

        private void CreateHistoryTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Action Buttons
            btnRefreshHistory = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔄 تحديث", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnRefreshHistory.Location = new Point(20, 20);
            btnRefreshHistory.Size = new Size(80, 30);
            btnRefreshHistory.Click += BtnRefreshHistory_Click;

            btnDeleteBackup = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🗑️ حذف", ModernMedicalTheme.Components.ButtonStyle.Error);
            btnDeleteBackup.Location = new Point(110, 20);
            btnDeleteBackup.Size = new Size(80, 30);
            btnDeleteBackup.Click += BtnDeleteBackup_Click;
            btnDeleteBackup.Enabled = false;

            btnCleanOldBackups = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🧹 تنظيف القديم", ModernMedicalTheme.Components.ButtonStyle.Secondary);
            btnCleanOldBackups.Location = new Point(200, 20);
            btnCleanOldBackups.Size = new Size(100, 30);
            btnCleanOldBackups.Click += BtnCleanOldBackups_Click;

            // History Grid
            dgvBackupHistory = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(1120, 600),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = ArabicFontHelper.GetArabicFont(9F),
                RightToLeft = RightToLeft.Yes,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Configure columns
            dgvBackupHistory.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "BackupDate", HeaderText = "تاريخ النسخة", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "BackupType", HeaderText = "النوع", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Description", HeaderText = "الوصف", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "FileSize", HeaderText = "حجم الملف", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "FilePath", HeaderText = "المسار", Width = 300 },
                new DataGridViewTextBoxColumn { Name = "Status", HeaderText = "الحالة", Width = 80 }
            });

            dgvBackupHistory.SelectionChanged += DgvBackupHistory_SelectionChanged;

            panel.Controls.AddRange(new Control[]
            {
                btnRefreshHistory, btnDeleteBackup, btnCleanOldBackups, dgvBackupHistory
            });

            tabHistory.Controls.Add(panel);
        }

        private async void LoadData()
        {
            try
            {
                // Load default backup path from settings
                var defaultPath = await _settingsService.GetSetting("BackupLocation", @"C:\Backups\MedicalCenter");
                txtBackupPath.Text = defaultPath;

                // Load backup history
                await LoadBackupHistory();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        private async Task LoadBackupHistory()
        {
            try
            {
                dgvBackupHistory.Rows.Clear();

                var backupPath = txtBackupPath.Text;
                if (Directory.Exists(backupPath))
                {
                    var backupFiles = Directory.GetFiles(backupPath, "*.zip");
                    
                    foreach (var file in backupFiles.OrderByDescending(f => File.GetCreationTime(f)))
                    {
                        var fileInfo = new FileInfo(file);
                        var backupInfo = await _backupService.GetBackupInfo(file);
                        
                        dgvBackupHistory.Rows.Add(
                            backupInfo?.BackupDate.ToString("yyyy-MM-dd HH:mm") ?? fileInfo.CreationTime.ToString("yyyy-MM-dd HH:mm"),
                            backupInfo?.BackupType.ToString() ?? "غير معروف",
                            backupInfo?.Description ?? Path.GetFileNameWithoutExtension(file),
                            FormatFileSize(fileInfo.Length),
                            file,
                            File.Exists(file) ? "موجود" : "مفقود"
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل سجل النسخ الاحتياطية: {ex.Message}");
            }
        }

        // Event Handlers
        private void BtnBrowseBackup_Click(object sender, EventArgs e)
        {
            using var folderDialog = new FolderBrowserDialog
            {
                Description = "اختر مجلد النسخ الاحتياطية",
                ShowNewFolderButton = true
            };

            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                txtBackupPath.Text = folderDialog.SelectedPath;
            }
        }

        private void BtnBrowseRestore_Click(object sender, EventArgs e)
        {
            using var openDialog = new OpenFileDialog
            {
                Title = "اختر ملف النسخة الاحتياطية",
                Filter = "ملفات النسخ الاحتياطية (*.zip)|*.zip|جميع الملفات (*.*)|*.*",
                CheckFileExists = true
            };

            if (openDialog.ShowDialog() == DialogResult.OK)
            {
                txtRestoreFile.Text = openDialog.FileName;
            }
        }

        private async void BtnCreateBackup_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtBackupPath.Text))
            {
                ShowWarning("يرجى اختيار مسار النسخة الاحتياطية");
                return;
            }

            try
            {
                btnCreateBackup.Enabled = false;
                pbBackupProgress.Visible = true;
                pbBackupProgress.Style = ProgressBarStyle.Marquee;
                lblBackupStatus.Text = "جاري إنشاء النسخة الاحتياطية...";

                var result = await _backupService.CreateFullBackup(txtBackupPath.Text, txtBackupDescription.Text);

                if (result.Success)
                {
                    ShowSuccess(result.Message);
                    await LoadBackupHistory();
                }
                else
                {
                    ShowError(result.Message);
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
            }
            finally
            {
                btnCreateBackup.Enabled = true;
                pbBackupProgress.Visible = false;
                lblBackupStatus.Text = "";
            }
        }

        private async void BtnVerifyBackup_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtRestoreFile.Text))
            {
                ShowWarning("يرجى اختيار ملف النسخة الاحتياطية");
                return;
            }

            try
            {
                SetLoadingState(true);
                var result = await _backupService.VerifyBackup(txtRestoreFile.Text);

                if (result.IsValid)
                {
                    ShowSuccess("النسخة الاحتياطية صحيحة ويمكن استعادتها");
                    btnRestoreBackup.Enabled = true;
                    
                    var backupInfo = await _backupService.GetBackupInfo(txtRestoreFile.Text);
                    if (backupInfo != null)
                    {
                        lblRestoreInfo.Text = $"تاريخ النسخة: {backupInfo.BackupDate:yyyy-MM-dd HH:mm}\n" +
                                            $"النوع: {backupInfo.BackupType}\n" +
                                            $"الوصف: {backupInfo.Description}";
                    }
                }
                else
                {
                    ShowError($"النسخة الاحتياطية غير صحيحة: {result.Message}");
                    btnRestoreBackup.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في التحقق من النسخة الاحتياطية: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async void BtnRestoreBackup_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "تحذير: ستؤدي عملية الاستعادة إلى استبدال جميع البيانات الحالية.\nهل أنت متأكد من المتابعة؟",
                "تأكيد الاستعادة",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result != DialogResult.Yes) return;

            try
            {
                pbRestoreProgress.Visible = true;
                pbRestoreProgress.Style = ProgressBarStyle.Marquee;
                lblRestoreStatus.Text = "جاري استعادة النسخة الاحتياطية...";

                var restoreResult = await _backupService.RestoreFromBackup(txtRestoreFile.Text, true);

                if (restoreResult.Success)
                {
                    ShowSuccess("تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق.");
                }
                else
                {
                    ShowError(restoreResult.Message);
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}");
            }
            finally
            {
                pbRestoreProgress.Visible = false;
                lblRestoreStatus.Text = "";
            }
        }

        private async void BtnRefreshHistory_Click(object sender, EventArgs e)
        {
            await LoadBackupHistory();
        }

        private void BtnDeleteBackup_Click(object sender, EventArgs e)
        {
            if (dgvBackupHistory.SelectedRows.Count == 0) return;

            var selectedRow = dgvBackupHistory.SelectedRows[0];
            var filePath = selectedRow.Cells["FilePath"].Value.ToString();

            var result = MessageBox.Show(
                "هل أنت متأكد من حذف هذه النسخة الاحتياطية؟",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                        ShowSuccess("تم حذف النسخة الاحتياطية");
                        LoadBackupHistory();
                    }
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف النسخة الاحتياطية: {ex.Message}");
                }
            }
        }

        private async void BtnCleanOldBackups_Click(object sender, EventArgs e)
        {
            var retentionDays = await _settingsService.GetSetting("RetentionDays", 30);
            
            var result = MessageBox.Show(
                $"سيتم حذف جميع النسخ الاحتياطية الأقدم من {retentionDays} يوم. هل تريد المتابعة؟",
                "تأكيد التنظيف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    SetLoadingState(true);
                    var cleanupResult = await _backupService.CleanOldBackups(txtBackupPath.Text, retentionDays);
                    
                    if (cleanupResult.Success)
                    {
                        ShowSuccess($"{cleanupResult.Message}. تم توفير {FormatFileSize(cleanupResult.FreedSpace)} من المساحة");
                        await LoadBackupHistory();
                    }
                    else
                    {
                        ShowError(cleanupResult.Message);
                    }
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في تنظيف النسخ الاحتياطية: {ex.Message}");
                }
                finally
                {
                    SetLoadingState(false);
                }
            }
        }

        private void DgvBackupHistory_SelectionChanged(object sender, EventArgs e)
        {
            btnDeleteBackup.Enabled = dgvBackupHistory.SelectedRows.Count > 0;
        }

        // Helper Methods
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
