﻿#pragma checksum "..\..\..\..\Controls\CloudStorageControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8A3C2079CA8A0B25704E98CBB5D5E6B66E871DB5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Controls {
    
    
    /// <summary>
    /// CloudStorageControl
    /// </summary>
    public partial class CloudStorageControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 99 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusCard;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.PackIcon StatusIcon;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusDescription;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border UserInfoCard;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserName;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserEmail;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConnectButton;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DisconnectButton;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card StorageStatsCard;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalFilesText;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSizeText;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastSyncText;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card SyncedFilesCard;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshFilesButton;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\Controls\CloudStorageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView SyncedFilesList;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/controls/cloudstoragecontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\CloudStorageControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StatusCard = ((System.Windows.Controls.Border)(target));
            return;
            case 2:
            this.StatusIcon = ((MaterialDesignThemes.Wpf.PackIcon)(target));
            return;
            case 3:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.StatusDescription = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.UserInfoCard = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.UserName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.UserEmail = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ConnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\Controls\CloudStorageControl.xaml"
            this.ConnectButton.Click += new System.Windows.RoutedEventHandler(this.ConnectButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.DisconnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 155 "..\..\..\..\Controls\CloudStorageControl.xaml"
            this.DisconnectButton.Click += new System.Windows.RoutedEventHandler(this.DisconnectButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\..\Controls\CloudStorageControl.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.StorageStatsCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 12:
            this.TotalFilesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TotalSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.LastSyncText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.SyncedFilesCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 16:
            this.RefreshFilesButton = ((System.Windows.Controls.Button)(target));
            
            #line 256 "..\..\..\..\Controls\CloudStorageControl.xaml"
            this.RefreshFilesButton.Click += new System.Windows.RoutedEventHandler(this.RefreshFilesButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.SyncedFilesList = ((System.Windows.Controls.ListView)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

