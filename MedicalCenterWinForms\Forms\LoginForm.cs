#nullable disable
#pragma warning disable CS0414
using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Data;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Controls;
using MedicalCenterWinForms.Animations;
using System.Drawing.Drawing2D;

namespace MedicalCenterWinForms.Forms
{
    public partial class LoginForm : Form
    {
        private readonly DatabaseService _databaseService;
        private UserAccount? _currentUser;

        // Animation variables
        private int _animationStep = 0;
        private bool _isAnimating = false;
        private bool _isLoading = false;
        private float _cardOpacity = 0f;
        private int _cardTargetY = 100;
        private int _cardCurrentY = 200;

        // Modern UI state
        private bool _showPassword = false;
        private bool _rememberMe = false;

        public UserAccount? CurrentUser => _currentUser;

        public LoginForm()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();

            // Modern form setup
            SetupModernForm();
            SetupEventHandlers();
            SetupPasswordFeatures();

            // Setup Arabic fonts and layout
            ArabicFontHelper.SetupArabicForm(this);
        }

        public LoginForm(DatabaseService databaseService)
        {
            InitializeComponent();
            _databaseService = databaseService;

            // Modern form setup
            SetupModernForm();
            SetupEventHandlers();
            SetupPasswordFeatures();

            // Setup Arabic fonts and layout
            ArabicFontHelper.SetupArabicForm(this);
        }

        private void SetupModernForm()
        {
            // Modern form properties
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle; // Changed from None to FixedSingle for visibility
            this.WindowState = FormWindowState.Normal;
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.DoubleBuffered = true;
            this.TopMost = true; // Make sure window appears on top

            // Enable modern visual effects
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer |
                         ControlStyles.ResizeRedraw, true);
        }

        private void SetupEventHandlers()
        {
            // Keyboard shortcuts
            this.KeyPreview = true;
            this.KeyDown += LoginForm_KeyDown;

            // Form events
            this.Shown += LoginForm_Shown;
            this.FormClosing += LoginForm_FormClosing;

            // Modern drag functionality
            this.pnlLoginCard.MouseDown += PnlLoginCard_MouseDown;
            this.pnlHeader.MouseDown += PnlLoginCard_MouseDown;
        }

        private void SetupPasswordFeatures()
        {
            // Password visibility toggle
            this.chkShowPassword = new CheckBox
            {
                Text = "إظهار كلمة المرور",
                Location = new Point(250, 310),
                Font = ArabicFontHelper.GetArabicFont(9F),
                RightToLeft = RightToLeft.Yes,
                BackColor = Color.Transparent
            };
            this.chkShowPassword.CheckedChanged += ChkShowPassword_CheckedChanged;
            this.pnlLoginCard.Controls.Add(this.chkShowPassword);

            // Set password character
            this.txtPassword.UseSystemPasswordChar = true;
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                ShowModernError("يرجى إدخال اسم المستخدم وكلمة المرور");
                return;
            }

            await StartLoginAnimation();

            try
            {
                // Test database connection first
                if (!await _databaseService.TestConnectionAsync())
                {
                    ShowModernError("لا يمكن الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات الاتصال.");
                    return;
                }

                // Ensure database is created
                await _databaseService.EnsureDatabaseCreatedAsync();

                using var context = _databaseService.GetDbContext();
                
                // Check if this is first run (no users exist)
                var userCount = await context.UserAccounts.CountAsync();
                if (userCount == 0)
                {
                    await SeedInitialDataAsync(context);
                }

                // Authenticate user
                var user = await context.UserAccounts
                    .FirstOrDefaultAsync(u => u.Username == txtUsername.Text && 
                                            u.HashedPassword == txtPassword.Text && 
                                            u.IsActive);

                if (user != null)
                {
                    _currentUser = user;
                    user.LastLoginDate = DateTime.Now;
                    await context.SaveChangesAsync();

                    await ShowSuccessAnimation();
                    await Task.Delay(1000); // Show success for a moment

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    ShowModernError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    await StopLoginAnimation();
                }
            }
            catch (Exception ex)
            {
                ShowModernError($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}");
                await StopLoginAnimation();
            }
        }

        private async Task SeedInitialDataAsync(ApplicationDbContext context)
        {
            // Add default users
            var users = new[]
            {
                new UserAccount { Username = "admin", HashedPassword = "admin123", Role = "Admin", IsActive = true },
                new UserAccount { Username = "reception", HashedPassword = "reception123", Role = "Reception", IsActive = true },
                new UserAccount { Username = "cashier", HashedPassword = "cashier123", Role = "Cashier", IsActive = true }
            };

            context.UserAccounts.AddRange(users);

            // Add sample doctors
            var doctors = new[]
            {
                new Doctor { FullName = "د. أحمد محمد", Specialty = "طب عام", IsActive = true },
                new Doctor { FullName = "د. فاطمة علي", Specialty = "أطفال", IsActive = true },
                new Doctor { FullName = "د. محمد حسن", Specialty = "باطنية", IsActive = true }
            };

            context.Doctors.AddRange(doctors);

            // Add sample medical services
            var services = new[]
            {
                new MedicalService { ServiceName = "كشفية", ServiceType = "Direct", IsCenterService = true, DefaultPrice = 50000 },
                new MedicalService { ServiceName = "فحص شامل", ServiceType = "Direct", IsCenterService = true, DefaultPrice = 100000 },
                new MedicalService { ServiceName = "تحليل دم", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 75000 },
                new MedicalService { ServiceName = "أشعة سينية", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 125000 },
                new MedicalService { ServiceName = "سونار", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 150000 }
            };

            context.MedicalServices.AddRange(services);

            await context.SaveChangesAsync();
        }

        private void btnSettings_Click(object sender, EventArgs e)
        {
            var settingsControl = new DatabaseSettingsControl(_databaseService);

            var settingsForm = new Form
            {
                Text = "إعدادات قاعدة البيانات",
                Size = new Size(550, 500),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            settingsControl.Dock = DockStyle.Fill;
            settingsForm.Controls.Add(settingsControl);
            settingsForm.ShowDialog();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // Set default test credentials
            txtUsername.Text = "admin";
            txtPassword.Text = "admin123";

            // Setup logo if available
            SetupLogo();
        }

        #region Modern UI Methods

        private void SetupLogo()
        {
            try
            {
                // Try to load logo from resources or create a modern placeholder
                this.picLogo.BackColor = Color.Transparent;
                this.picLogo.Paint += (s, e) =>
                {
                    var rect = this.picLogo.ClientRectangle;
                    e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                    // Draw modern medical cross icon
                    using var brush = new SolidBrush(Color.White);
                    var crossSize = Math.Min(rect.Width, rect.Height) / 3;
                    var centerX = rect.Width / 2;
                    var centerY = rect.Height / 2;

                    // Horizontal bar
                    e.Graphics.FillRectangle(brush,
                        centerX - crossSize, centerY - crossSize/4,
                        crossSize * 2, crossSize/2);

                    // Vertical bar
                    e.Graphics.FillRectangle(brush,
                        centerX - crossSize/4, centerY - crossSize,
                        crossSize/2, crossSize * 2);
                };
            }
            catch
            {
                // Fallback if logo loading fails
                this.picLogo.Visible = false;
            }
        }

        private void ShowModernError(string message)
        {
            // Create modern error notification
            var errorPanel = new Panel
            {
                Size = new Size(350, 60),
                Location = new Point(50, 500),
                BackColor = MaterialDesignHelper.Colors.ErrorLight,
                Visible = false
            };

            var errorLabel = new Label
            {
                Text = message,
                Font = ArabicFontHelper.GetArabicFont(10F),
                ForeColor = MaterialDesignHelper.Colors.Error,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            errorPanel.Controls.Add(errorLabel);
            this.pnlLoginCard.Controls.Add(errorPanel);

            // Animate error appearance
            AnimateErrorPanel(errorPanel);
        }

        private async void AnimateErrorPanel(Panel errorPanel)
        {
            errorPanel.Visible = true;

            // Slide in animation
            for (int i = 0; i <= 10; i++)
            {
                errorPanel.Location = new Point(50, 500 - (i * 5));
                await Task.Delay(20);
            }

            // Wait and fade out
            await Task.Delay(3000);

            for (int i = 10; i >= 0; i--)
            {
                errorPanel.Location = new Point(50, 450 + ((10 - i) * 5));
                await Task.Delay(20);
            }

            errorPanel.Dispose();
        }

        private async Task StartLoginAnimation()
        {
            _isLoading = true;

            // Disable login button and show loading
            btnLogin.Enabled = false;
            btnLogin.Text = "جاري التحقق...";

            // Create and show loading indicator
            ShowLoadingIndicator();

            // Animate button
            await AnimateButtonLoading();
        }

        private async Task StopLoginAnimation()
        {
            _isLoading = false;

            // Hide loading indicator
            HideLoadingIndicator();

            // Reset button
            btnLogin.Enabled = true;
            btnLogin.Text = "تسجيل الدخول";

            // Shake animation for error
            await ShakeLoginCard();
        }

        private async Task ShowSuccessAnimation()
        {
            // Change button to success state
            btnLogin.BackColor = MaterialDesignHelper.Colors.Success;
            btnLogin.Text = "✓ تم بنجاح";

            // Pulse animation
            for (int i = 0; i < 3; i++)
            {
                btnLogin.Size = new Size(360, 60);
                await Task.Delay(100);
                btnLogin.Size = new Size(350, 55);
                await Task.Delay(100);
            }
        }

        private void ShowLoadingIndicator()
        {
            if (pnlLoading == null)
            {
                pnlLoading = new Panel
                {
                    Size = new Size(350, 10),
                    Location = new Point(50, 420),
                    BackColor = Color.Transparent
                };

                progressLogin = new ProgressBar
                {
                    Style = ProgressBarStyle.Marquee,
                    MarqueeAnimationSpeed = 30,
                    Dock = DockStyle.Fill,
                    BackColor = MaterialDesignHelper.Colors.Background,
                    ForeColor = MaterialDesignHelper.Colors.Primary
                };

                pnlLoading.Controls.Add(progressLogin);
                this.pnlLoginCard.Controls.Add(pnlLoading);
            }

            pnlLoading.Visible = true;
        }

        private void HideLoadingIndicator()
        {
            if (pnlLoading != null)
            {
                pnlLoading.Visible = false;
            }
        }

        private async Task AnimateButtonLoading()
        {
            var originalColor = btnLogin.BackColor;

            // Pulse animation while loading
            while (_isLoading)
            {
                btnLogin.BackColor = MaterialDesignHelper.LightenColor(originalColor, 0.2f);
                await Task.Delay(500);
                if (_isLoading)
                {
                    btnLogin.BackColor = originalColor;
                    await Task.Delay(500);
                }
            }
        }

        private async Task ShakeLoginCard()
        {
            var originalLocation = pnlLoginCard.Location;

            // Shake animation
            for (int i = 0; i < 6; i++)
            {
                pnlLoginCard.Location = new Point(originalLocation.X + (i % 2 == 0 ? 5 : -5), originalLocation.Y);
                await Task.Delay(50);
            }

            pnlLoginCard.Location = originalLocation;
        }

        #endregion

        #region Animation and Interaction Events

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            if (_isAnimating)
            {
                _animationStep++;

                // Entrance animation
                if (_cardCurrentY > _cardTargetY)
                {
                    _cardCurrentY -= 5;
                    pnlLoginCard.Location = new Point(pnlLoginCard.Location.X, _cardCurrentY);
                }

                // Opacity animation (simulated with color blending)
                if (_cardOpacity < 1.0f)
                {
                    _cardOpacity += 0.05f;
                    // Apply opacity effect through color manipulation
                }

                // Stop animation when complete
                if (_cardCurrentY <= _cardTargetY && _cardOpacity >= 1.0f)
                {
                    _isAnimating = false;
                    animationTimer.Stop();
                }
            }
        }

        private void StartEntranceAnimation()
        {
            _isAnimating = true;
            _animationStep = 0;
            _cardOpacity = 0f;
            _cardCurrentY = 200;
            animationTimer.Start();
        }

        private void LoginForm_Shown(object sender, EventArgs e)
        {
            // Start entrance animation when form is shown
            StartEntranceAnimation();
        }

        private void LoginForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (!_isLoading)
                        btnLogin_Click(btnLogin, EventArgs.Empty);
                    break;
                case Keys.Escape:
                    btnExit_Click(btnExit, EventArgs.Empty);
                    break;
                case Keys.F1:
                    btnSettings_Click(btnSettings, EventArgs.Empty);
                    break;
            }
        }

        private void LoginForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // Cleanup animations
            if (animationTimer != null && animationTimer.Enabled)
            {
                animationTimer.Stop();
            }
        }

        private void ChkShowPassword_CheckedChanged(object sender, EventArgs e)
        {
            _showPassword = chkShowPassword.Checked;
            txtPassword.UseSystemPasswordChar = !_showPassword;

            // Update icon
            lblPasswordIcon.Text = _showPassword ? "🔓" : "🔒";
        }

        // Modern drag functionality
        private Point _lastPoint;
        private bool _isDragging = false;

        private void PnlLoginCard_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                _isDragging = true;
                _lastPoint = e.Location;
                this.Cursor = Cursors.SizeAll;
            }
        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            if (_isDragging)
            {
                this.Location = new Point(
                    this.Location.X + e.X - _lastPoint.X,
                    this.Location.Y + e.Y - _lastPoint.Y);
            }
            base.OnMouseMove(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                this.Cursor = Cursors.Default;
            }
            base.OnMouseUp(e);
        }

        #endregion
    }
}
