using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Data;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Controls;

namespace MedicalCenterWinForms.Forms
{
    public partial class LoginForm : Form
    {
        private readonly DatabaseService _databaseService;
        private UserAccount? _currentUser;

        public UserAccount? CurrentUser => _currentUser;

        public LoginForm()
        {
            InitializeComponent();
            _databaseService = new DatabaseService();
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Setup Arabic fonts and layout
            ArabicFontHelper.SetupArabicForm(this);
        }

        public LoginForm(DatabaseService databaseService)
        {
            InitializeComponent();
            _databaseService = databaseService;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Setup Arabic fonts and layout
            ArabicFontHelper.SetupArabicForm(this);
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم وكلمة المرور", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            btnLogin.Enabled = false;
            btnLogin.Text = "جاري التحقق...";

            try
            {
                // Test database connection first
                if (!await _databaseService.TestConnectionAsync())
                {
                    MessageBox.Show("لا يمكن الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات الاتصال.", "خطأ في الاتصال", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Ensure database is created
                await _databaseService.EnsureDatabaseCreatedAsync();

                using var context = _databaseService.GetDbContext();
                
                // Check if this is first run (no users exist)
                var userCount = await context.UserAccounts.CountAsync();
                if (userCount == 0)
                {
                    await SeedInitialDataAsync(context);
                }

                // Authenticate user
                var user = await context.UserAccounts
                    .FirstOrDefaultAsync(u => u.Username == txtUsername.Text && 
                                            u.HashedPassword == txtPassword.Text && 
                                            u.IsActive);

                if (user != null)
                {
                    _currentUser = user;
                    user.LastLoginDate = DateTime.Now;
                    await context.SaveChangesAsync();

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnLogin.Enabled = true;
                btnLogin.Text = "تسجيل الدخول";
            }
        }

        private async Task SeedInitialDataAsync(ApplicationDbContext context)
        {
            // Add default users
            var users = new[]
            {
                new UserAccount { Username = "admin", HashedPassword = "admin123", Role = "Admin", IsActive = true },
                new UserAccount { Username = "reception", HashedPassword = "reception123", Role = "Reception", IsActive = true },
                new UserAccount { Username = "cashier", HashedPassword = "cashier123", Role = "Cashier", IsActive = true }
            };

            context.UserAccounts.AddRange(users);

            // Add sample doctors
            var doctors = new[]
            {
                new Doctor { FullName = "د. أحمد محمد", Specialty = "طب عام", IsActive = true },
                new Doctor { FullName = "د. فاطمة علي", Specialty = "أطفال", IsActive = true },
                new Doctor { FullName = "د. محمد حسن", Specialty = "باطنية", IsActive = true }
            };

            context.Doctors.AddRange(doctors);

            // Add sample medical services
            var services = new[]
            {
                new MedicalService { ServiceName = "كشفية", ServiceType = "Direct", IsCenterService = true, DefaultPrice = 50000 },
                new MedicalService { ServiceName = "فحص شامل", ServiceType = "Direct", IsCenterService = true, DefaultPrice = 100000 },
                new MedicalService { ServiceName = "تحليل دم", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 75000 },
                new MedicalService { ServiceName = "أشعة سينية", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 125000 },
                new MedicalService { ServiceName = "سونار", ServiceType = "Referral", IsCenterService = true, DefaultPrice = 150000 }
            };

            context.MedicalServices.AddRange(services);

            await context.SaveChangesAsync();
        }

        private void btnSettings_Click(object sender, EventArgs e)
        {
            var settingsControl = new DatabaseSettingsControl(_databaseService);

            var settingsForm = new Form
            {
                Text = "إعدادات قاعدة البيانات",
                Size = new Size(550, 500),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            settingsControl.Dock = DockStyle.Fill;
            settingsForm.Controls.Add(settingsControl);
            settingsForm.ShowDialog();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // Set default test credentials
            txtUsername.Text = "admin";
            txtPassword.Text = "admin123";
        }
    }
}
