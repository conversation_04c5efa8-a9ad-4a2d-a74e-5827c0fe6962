using System;
using System.Drawing;
using System.Windows.Forms;
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Forms
{
    partial class MainForm
    {
        private System.ComponentModel.IContainer components = null;

        // Modern UI Components
        private Panel mainContainer;
        private Panel topBar;
        private Panel sideNavigation;
        private Panel contentArea;
        private Panel contentPanel;
        private Panel headerPanel;
        private Panel statusPanel;

        // Top Bar Controls
        private Label appTitle;
        private Panel windowControls;
        private Button btnMinimize;
        private Button btnMaximize;
        private Button btnClose;

        // Header Controls
        private Label pageTitle;
        private Label pageSubtitle;
        private Panel breadcrumbPanel;

        // Navigation Controls
        private Panel navDashboard;
        private Panel navPatients;
        private Panel navDoctors;
        private Panel navServices;
        private Panel navDoctorServices;
        private Panel navPayments;
        private Panel navReports;
        private Panel navSettings;
        private Panel navLogout;

        // Status Bar Controls
        private Label statusTime;
        private Label statusUser;
        private Label statusConnection;
        private System.Windows.Forms.Timer statusTimer;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();

            // Initialize all components
            InitializeMainContainer();
            InitializeTopBar();
            InitializeSideNavigation();
            InitializeContentArea();
            InitializeStatusBar();
            InitializeTimer();

            // Configure main form
            ConfigureMainForm();
        }

        private void InitializeMainContainer()
        {
            this.mainContainer = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = MaterialDesignHelper.Colors.Background,
                Padding = new Padding(0)
            };

            this.Controls.Add(this.mainContainer);
        }

        private void InitializeTopBar()
        {
            // Top Bar Container
            this.topBar = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = MaterialDesignHelper.Colors.Primary,
                Padding = new Padding(20, 0, 20, 0)
            };

            // App Title
            this.appTitle = new Label
            {
                Text = "🏥 نظام إدارة المركز الطبي",
                Font = ArabicFontHelper.GetArabicFont(16F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Anchor = AnchorStyles.Right | AnchorStyles.Top,
                Location = new Point(20, 18)
            };

            // Window Controls Panel
            this.windowControls = new Panel
            {
                Width = 120,
                Height = 40,
                Dock = DockStyle.Left,
                BackColor = Color.Transparent
            };

            // Window Control Buttons
            this.btnClose = CreateWindowButton("✕", Color.FromArgb(231, 76, 60));
            this.btnMaximize = CreateWindowButton("🗖", Color.FromArgb(52, 152, 219));
            this.btnMinimize = CreateWindowButton("🗕", Color.FromArgb(241, 196, 15));

            this.btnClose.Location = new Point(0, 10);
            this.btnMaximize.Location = new Point(40, 10);
            this.btnMinimize.Location = new Point(80, 10);

            this.btnClose.Click += btnClose_Click;
            this.btnMaximize.Click += btnMaximize_Click;
            this.btnMinimize.Click += btnMinimize_Click;

            // Add controls to top bar
            this.windowControls.Controls.AddRange(new Control[] { btnClose, btnMaximize, btnMinimize });
            this.topBar.Controls.AddRange(new Control[] { appTitle, windowControls });
            this.mainContainer.Controls.Add(this.topBar);
        }

        private Button CreateWindowButton(string text, Color hoverColor)
        {
            var button = new Button
            {
                Text = text,
                Size = new Size(30, 30),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F),
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = hoverColor;

            return button;
        }

        private void InitializeSideNavigation()
        {
            // Side Navigation Container
            this.sideNavigation = new Panel
            {
                Width = 280,
                Dock = DockStyle.Right,
                BackColor = MaterialDesignHelper.Colors.SidebarBg,
                Padding = new Padding(0, 20, 0, 20)
            };

            // Create navigation buttons
            this.navDashboard = CreateNavButton("📊", "لوحة التحكم", navDashboard_Click);
            this.navPatients = CreateNavButton("🧑‍🦽", "إدارة المراجعين", navPatients_Click);
            this.navDoctors = CreateNavButton("👨‍⚕️", "إدارة الأطباء", navDoctors_Click);
            this.navServices = CreateNavButton("🩺", "الخدمات الطبية", navServices_Click);
            this.navDoctorServices = CreateNavButton("🔗", "ربط الأطباء بالخدمات", navDoctorServices_Click);
            this.navPayments = CreateNavButton("💰", "إدارة المدفوعات", navPayments_Click);
            this.navReports = CreateNavButton("📈", "التقارير", navReports_Click);
            this.navSettings = CreateNavButton("⚙️", "الإعدادات", navSettings_Click);
            this.navLogout = CreateNavButton("🚪", "تسجيل الخروج", btnLogout_Click);

            // Position navigation buttons
            int yPos = 20;
            var navButtons = new Panel[] { navDashboard, navPatients, navDoctors, navServices, navDoctorServices, navPayments, navReports, navSettings };

            foreach (var button in navButtons)
            {
                button.Location = new Point(10, yPos);
                yPos += 80;
                this.sideNavigation.Controls.Add(button);
            }

            // Logout button at bottom
            this.navLogout.Location = new Point(10, this.sideNavigation.Height - 100);
            this.navLogout.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            this.sideNavigation.Controls.Add(this.navLogout);

            this.mainContainer.Controls.Add(this.sideNavigation);
        }

        private Panel CreateNavButton(string icon, string text, EventHandler clickHandler)
        {
            return MaterialDesignHelper.CreateModernSidebarPanel(text, icon, clickHandler, 260, 70);
        }

        private void InitializeContentArea()
        {
            // Content Area Container
            this.contentArea = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = MaterialDesignHelper.Colors.Background,
                Padding = new Padding(20)
            };

            // Header Panel
            this.headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                Padding = new Padding(30, 20, 30, 20)
            };

            // Page Title
            this.pageTitle = new Label
            {
                Text = "لوحة التحكم",
                Font = ArabicFontHelper.GetArabicFont(24F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(30, 15)
            };

            // Page Subtitle
            this.pageSubtitle = new Label
            {
                Text = "نظرة عامة على النظام",
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary,
                AutoSize = true,
                Location = new Point(30, 45)
            };

            // Content Panel
            this.contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(0),
                Margin = new Padding(0, 10, 0, 0)
            };

            // Add controls to header
            this.headerPanel.Controls.AddRange(new Control[] { pageTitle, pageSubtitle });

            // Add controls to content area
            this.contentArea.Controls.AddRange(new Control[] { headerPanel, contentPanel });
            this.mainContainer.Controls.Add(this.contentArea);
        }

        private void InitializeStatusBar()
        {
            // Status Panel
            this.statusPanel = new Panel
            {
                Height = 30,
                Dock = DockStyle.Bottom,
                BackColor = MaterialDesignHelper.Colors.Primary,
                Padding = new Padding(20, 5, 20, 5)
            };

            // Status Time
            this.statusTime = new Label
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss"),
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = Color.White,
                AutoSize = true,
                Dock = DockStyle.Left
            };

            // Status User
            this.statusUser = new Label
            {
                Text = "المستخدم: المدير العام",
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = Color.White,
                AutoSize = true,
                Dock = DockStyle.Right
            };

            // Status Connection
            this.statusConnection = new Label
            {
                Text = "🟢 متصل",
                Font = ArabicFontHelper.GetArabicFont(9F),
                ForeColor = Color.White,
                AutoSize = true,
                Anchor = AnchorStyles.Top | AnchorStyles.Right,
                Location = new Point(150, 7)
            };

            this.statusPanel.Controls.AddRange(new Control[] { statusTime, statusConnection, statusUser });
            this.mainContainer.Controls.Add(this.statusPanel);
        }

        private void InitializeTimer()
        {
            this.statusTimer = new System.Windows.Forms.Timer(this.components)
            {
                Interval = 1000,
                Enabled = true
            };
            this.statusTimer.Tick += statusTimer_Tick;
        }

        private void ConfigureMainForm()
        {
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1400, 900);
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "نظام إدارة المركز الطبي - 2025";
        }
    }
}