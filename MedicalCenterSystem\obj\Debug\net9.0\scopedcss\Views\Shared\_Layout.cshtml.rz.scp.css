/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-mjqybc6di4] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-mjqybc6di4] {
  color: #0077cc;
}

.btn-primary[b-mjqybc6di4] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-mjqybc6di4], .nav-pills .show > .nav-link[b-mjqybc6di4] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-mjqybc6di4] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-mjqybc6di4] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-mjqybc6di4] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-mjqybc6di4] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-mjqybc6di4] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
