using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace MedicalCenterWinForms.Services
{
    public class HelpService
    {
        private List<HelpTopic> _helpTopics;

        public HelpService()
        {
            InitializeHelpContent();
        }

        public List<HelpTopic> GetAllHelpTopics()
        {
            return _helpTopics;
        }

        public HelpTopic GetTopicById(string id)
        {
            return _helpTopics.FirstOrDefault(t => t.Id == id);
        }

        public List<HelpTopic> SearchTopics(string searchTerm, string category = "")
        {
            var query = _helpTopics.AsEnumerable();

            if (!string.IsNullOrEmpty(category) && category != "جميع الفئات")
            {
                query = query.Where(t => t.Category == category);
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                var terms = searchTerm.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);
                query = query.Where(t => 
                    terms.Any(term => 
                        t.Title.ToLower().Contains(term) ||
                        t.Content.ToLower().Contains(term) ||
                        t.Keywords.Any(k => k.To<PERSON>ower().Contains(term))
                    )
                );
            }

            return query.OrderBy(t => t.Category).ThenBy(t => t.Order).ToList();
        }

        public string GetTopicContent(HelpTopic topic)
        {
            var html = new StringBuilder();
            
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='utf-8'>");
            html.AppendLine("<style>");
            html.AppendLine(GetHelpStyles());
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            
            // Breadcrumb
            html.AppendLine($"<div class='breadcrumb'>{topic.Category} &gt; {topic.Title}</div>");
            
            // Title
            html.AppendLine($"<h1>{topic.Title}</h1>");
            
            // Content
            html.AppendLine($"<div class='content'>{topic.Content}</div>");
            
            // Related topics
            if (topic.RelatedTopics.Any())
            {
                html.AppendLine("<div class='related-topics'>");
                html.AppendLine("<h3>مواضيع ذات صلة</h3>");
                html.AppendLine("<ul>");
                foreach (var relatedId in topic.RelatedTopics)
                {
                    var relatedTopic = GetTopicById(relatedId);
                    if (relatedTopic != null)
                    {
                        html.AppendLine($"<li><a href='help://{relatedId}'>{relatedTopic.Title}</a></li>");
                    }
                }
                html.AppendLine("</ul>");
                html.AppendLine("</div>");
            }
            
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            return html.ToString();
        }

        public string GetHomePageContent()
        {
            var html = new StringBuilder();
            
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='utf-8'>");
            html.AppendLine("<style>");
            html.AppendLine(GetHelpStyles());
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            
            html.AppendLine("<h1>مرحباً بك في دليل المستخدم</h1>");
            html.AppendLine("<p>هذا الدليل التفاعلي سيساعدك في استخدام نظام إدارة المركز الطبي بكفاءة.</p>");
            
            html.AppendLine("<div class='quick-start'>");
            html.AppendLine("<h2>البداية السريعة</h2>");
            html.AppendLine("<div class='quick-links'>");
            html.AppendLine("<a href='help://getting-started' class='quick-link'>🚀 البدء مع النظام</a>");
            html.AppendLine("<a href='help://patient-management' class='quick-link'>👥 إدارة المرضى</a>");
            html.AppendLine("<a href='help://payment-processing' class='quick-link'>💰 معالجة المدفوعات</a>");
            html.AppendLine("<a href='help://reports' class='quick-link'>📊 التقارير</a>");
            html.AppendLine("</div>");
            html.AppendLine("</div>");
            
            html.AppendLine("<div class='features'>");
            html.AppendLine("<h2>الميزات الرئيسية</h2>");
            html.AppendLine("<ul>");
            html.AppendLine("<li>إدارة شاملة للمرضى والأطباء</li>");
            html.AppendLine("<li>نظام مدفوعات متقدم مع حساب الحصص</li>");
            html.AppendLine("<li>تقارير مالية تفصيلية</li>");
            html.AppendLine("<li>نسخ احتياطي تلقائي</li>");
            html.AppendLine("<li>واجهة سهلة الاستخدام</li>");
            html.AppendLine("</ul>");
            html.AppendLine("</div>");
            
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            return html.ToString();
        }

        public string GenerateSearchResultsHtml(List<HelpTopic> results, string searchTerm)
        {
            var html = new StringBuilder();
            
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='utf-8'>");
            html.AppendLine("<style>");
            html.AppendLine(GetHelpStyles());
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            
            html.AppendLine($"<h1>نتائج البحث عن: \"{searchTerm}\"</h1>");
            html.AppendLine($"<p>تم العثور على {results.Count} نتيجة</p>");
            
            html.AppendLine("<div class='search-results'>");
            foreach (var result in results)
            {
                html.AppendLine("<div class='search-result'>");
                html.AppendLine($"<h3><a href='help://{result.Id}'>{result.Title}</a></h3>");
                html.AppendLine($"<p class='category'>{result.Category}</p>");
                
                // Extract snippet
                var snippet = ExtractSnippet(result.Content, searchTerm);
                html.AppendLine($"<p class='snippet'>{snippet}</p>");
                
                html.AppendLine("</div>");
            }
            html.AppendLine("</div>");
            
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            return html.ToString();
        }

        private string ExtractSnippet(string content, string searchTerm)
        {
            var plainText = System.Text.RegularExpressions.Regex.Replace(content, "<.*?>", "");
            var index = plainText.ToLower().IndexOf(searchTerm.ToLower());
            
            if (index == -1) return plainText.Substring(0, Math.Min(200, plainText.Length)) + "...";
            
            var start = Math.Max(0, index - 100);
            var length = Math.Min(200, plainText.Length - start);
            var snippet = plainText.Substring(start, length);
            
            if (start > 0) snippet = "..." + snippet;
            if (start + length < plainText.Length) snippet += "...";
            
            return snippet;
        }

        private string GetHelpStyles()
        {
            return @"
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    direction: rtl;
                    text-align: right;
                    margin: 20px;
                    line-height: 1.6;
                    color: #333;
                }
                
                h1 {
                    color: #2c3e50;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 10px;
                }
                
                h2 {
                    color: #34495e;
                    margin-top: 30px;
                }
                
                h3 {
                    color: #7f8c8d;
                }
                
                .breadcrumb {
                    background-color: #ecf0f1;
                    padding: 8px 12px;
                    border-radius: 4px;
                    margin-bottom: 20px;
                    font-size: 14px;
                    color: #7f8c8d;
                }
                
                .content {
                    margin: 20px 0;
                }
                
                .quick-start {
                    background-color: #e8f5e8;
                    padding: 20px;
                    border-radius: 8px;
                    margin: 20px 0;
                }
                
                .quick-links {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10px;
                    margin-top: 15px;
                }
                
                .quick-link {
                    display: inline-block;
                    background-color: #3498db;
                    color: white;
                    padding: 10px 15px;
                    text-decoration: none;
                    border-radius: 5px;
                    transition: background-color 0.3s;
                }
                
                .quick-link:hover {
                    background-color: #2980b9;
                }
                
                .features {
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    margin: 20px 0;
                }
                
                .related-topics {
                    background-color: #fff3cd;
                    padding: 15px;
                    border-radius: 5px;
                    margin-top: 30px;
                }
                
                .search-results {
                    margin-top: 20px;
                }
                
                .search-result {
                    border: 1px solid #ddd;
                    padding: 15px;
                    margin-bottom: 15px;
                    border-radius: 5px;
                    background-color: #fafafa;
                }
                
                .search-result h3 {
                    margin-top: 0;
                    margin-bottom: 5px;
                }
                
                .search-result .category {
                    color: #666;
                    font-size: 12px;
                    margin-bottom: 10px;
                }
                
                .search-result .snippet {
                    color: #555;
                    font-size: 14px;
                }
                
                a {
                    color: #3498db;
                    text-decoration: none;
                }
                
                a:hover {
                    text-decoration: underline;
                }
                
                ul {
                    padding-right: 20px;
                }
                
                li {
                    margin-bottom: 5px;
                }
                
                .note {
                    background-color: #d1ecf1;
                    border: 1px solid #bee5eb;
                    padding: 10px;
                    border-radius: 4px;
                    margin: 15px 0;
                }
                
                .warning {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    padding: 10px;
                    border-radius: 4px;
                    margin: 15px 0;
                }
                
                .step {
                    background-color: #f8f9fa;
                    border-right: 4px solid #3498db;
                    padding: 10px 15px;
                    margin: 10px 0;
                }
                
                .step-number {
                    background-color: #3498db;
                    color: white;
                    border-radius: 50%;
                    width: 25px;
                    height: 25px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 10px;
                    font-weight: bold;
                }
                
                code {
                    background-color: #f4f4f4;
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-family: 'Courier New', monospace;
                }
                
                .screenshot {
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    max-width: 100%;
                    margin: 15px 0;
                }
            ";
        }

        private void InitializeHelpContent()
        {
            _helpTopics = new List<HelpTopic>
            {
                new HelpTopic
                {
                    Id = "getting-started",
                    Title = "البدء مع النظام",
                    Category = "البداية السريعة",
                    Order = 1,
                    Content = @"
                        <h2>مرحباً بك في نظام إدارة المركز الطبي</h2>
                        <p>هذا الدليل سيساعدك في البدء مع النظام خطوة بخطوة.</p>
                        
                        <div class='step'>
                            <span class='step-number'>1</span>
                            <strong>تسجيل الدخول:</strong> استخدم اسم المستخدم وكلمة المرور المقدمة من المدير.
                        </div>
                        
                        <div class='step'>
                            <span class='step-number'>2</span>
                            <strong>التعرف على الواجهة:</strong> الواجهة الرئيسية تحتوي على قائمة جانبية للتنقل بين الأقسام.
                        </div>
                        
                        <div class='step'>
                            <span class='step-number'>3</span>
                            <strong>إعداد البيانات الأساسية:</strong> ابدأ بإضافة الأطباء والخدمات الطبية.
                        </div>
                        
                        <div class='note'>
                            <strong>ملاحظة:</strong> تأكد من إعداد الإعدادات الأساسية قبل البدء في استخدام النظام.
                        </div>
                    ",
                    Keywords = new List<string> { "بداية", "تسجيل دخول", "واجهة", "إعداد" }
                },
                
                new HelpTopic
                {
                    Id = "patient-management",
                    Title = "إدارة المرضى",
                    Category = "إدارة المرضى",
                    Order = 1,
                    Content = @"
                        <h2>إدارة المرضى</h2>
                        <p>تعلم كيفية إضافة وإدارة بيانات المرضى في النظام.</p>
                        
                        <h3>إضافة مريض جديد</h3>
                        <div class='step'>
                            <span class='step-number'>1</span>
                            انتقل إلى قسم 'إدارة المرضى' من القائمة الجانبية
                        </div>
                        
                        <div class='step'>
                            <span class='step-number'>2</span>
                            اضغط على زر 'إضافة مريض جديد'
                        </div>
                        
                        <div class='step'>
                            <span class='step-number'>3</span>
                            املأ البيانات المطلوبة: الاسم، رقم الهاتف، العنوان
                        </div>
                        
                        <div class='step'>
                            <span class='step-number'>4</span>
                            اضغط 'حفظ' لإضافة المريض
                        </div>
                        
                        <div class='warning'>
                            <strong>تحذير:</strong> تأكد من صحة رقم الهاتف لأنه سيستخدم في التواصل مع المريض.
                        </div>
                    ",
                    Keywords = new List<string> { "مريض", "إضافة", "بيانات", "هاتف" }
                },
                
                new HelpTopic
                {
                    Id = "payment-processing",
                    Title = "معالجة المدفوعات",
                    Category = "المدفوعات",
                    Order = 1,
                    Content = @"
                        <h2>معالجة المدفوعات</h2>
                        <p>دليل شامل لمعالجة المدفوعات وحساب الحصص.</p>
                        
                        <h3>أنواع المدفوعات</h3>
                        <ul>
                            <li><strong>المدفوعات الرئيسية:</strong> رسوم الاستشارة والفحص</li>
                            <li><strong>مدفوعات التحويل:</strong> رسوم الخدمات الإضافية</li>
                        </ul>
                        
                        <h3>إضافة دفعة جديدة</h3>
                        <div class='step'>
                            <span class='step-number'>1</span>
                            اختر المريض من القائمة
                        </div>
                        
                        <div class='step'>
                            <span class='step-number'>2</span>
                            حدد الطبيب المعالج
                        </div>
                        
                        <div class='step'>
                            <span class='step-number'>3</span>
                            أدخل المبالغ المطلوبة
                        </div>
                        
                        <div class='step'>
                            <span class='step-number'>4</span>
                            سيتم حساب الحصص تلقائياً
                        </div>
                    ",
                    Keywords = new List<string> { "دفعة", "حصص", "استشارة", "تحويل" }
                }
            };
        }
    }

    // Help Models
    public class HelpTopic
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int Order { get; set; }
        public string Content { get; set; } = string.Empty;
        public List<string> Keywords { get; set; } = new();
        public List<string> RelatedTopics { get; set; } = new();
        public List<HelpTopic> Subtopics { get; set; } = new();
        public string VideoUrl { get; set; } = string.Empty;
        public List<string> Screenshots { get; set; } = new();
    }
}
