using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using MaterialDesignThemes.Wpf;

namespace HR_InvoiceArchiver.Controls
{
    public partial class ToastNotification : UserControl
    {
        private DispatcherTimer _autoCloseTimer = new();
        public event EventHandler? Closed;

        public ToastNotification()
        {
            InitializeComponent();
            SetupAutoCloseTimer();
        }

        public enum ToastType
        {
            Success,
            Error,
            Warning,
            Info
        }

        public static readonly DependencyProperty ToastTypeProperty =
            DependencyProperty.Register(nameof(Type), typeof(ToastType), typeof(ToastNotification),
                new PropertyMetadata(ToastType.Info, OnToastTypeChanged));

        public ToastType Type
        {
            get => (ToastType)GetValue(ToastTypeProperty);
            set => SetValue(ToastTypeProperty, value);
        }

        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register(nameof(Title), typeof(string), typeof(ToastNotification),
                new PropertyMetadata(string.Empty, OnTitleChanged));

        public string Title
        {
            get => (string)GetValue(TitleProperty);
            set => SetValue(TitleProperty, value);
        }

        public static readonly DependencyProperty MessageProperty =
            DependencyProperty.Register(nameof(Message), typeof(string), typeof(ToastNotification),
                new PropertyMetadata(string.Empty, OnMessageChanged));

        public string Message
        {
            get => (string)GetValue(MessageProperty);
            set => SetValue(MessageProperty, value);
        }

        public static readonly DependencyProperty DurationProperty =
            DependencyProperty.Register(nameof(Duration), typeof(TimeSpan), typeof(ToastNotification),
                new PropertyMetadata(TimeSpan.FromSeconds(4)));

        public TimeSpan Duration
        {
            get => (TimeSpan)GetValue(DurationProperty);
            set => SetValue(DurationProperty, value);
        }

        private static void OnToastTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ToastNotification toast)
                toast.UpdateAppearance();
        }

        private static void OnTitleChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ToastNotification toast)
                toast.TitleTextBlock.Text = e.NewValue?.ToString() ?? string.Empty;
        }

        private static void OnMessageChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is ToastNotification toast)
                toast.MessageTextBlock.Text = e.NewValue?.ToString() ?? string.Empty;
        }

        private void UpdateAppearance()
        {
            var (background, icon, iconBrush, textBrush) = Type switch
            {
                ToastType.Success => (new SolidColorBrush(Color.FromRgb(76, 175, 80)), PackIconKind.CheckCircle, 
                                    new SolidColorBrush(Colors.White), new SolidColorBrush(Colors.White)),
                ToastType.Error => (new SolidColorBrush(Color.FromRgb(244, 67, 54)), PackIconKind.AlertCircle,
                                  new SolidColorBrush(Colors.White), new SolidColorBrush(Colors.White)),
                ToastType.Warning => (new SolidColorBrush(Color.FromRgb(255, 152, 0)), PackIconKind.Alert,
                                    new SolidColorBrush(Colors.White), new SolidColorBrush(Colors.White)),
                ToastType.Info => (new SolidColorBrush(Color.FromRgb(33, 150, 243)), PackIconKind.Information,
                                 new SolidColorBrush(Colors.White), new SolidColorBrush(Colors.White)),
                _ => (new SolidColorBrush(Color.FromRgb(33, 150, 243)), PackIconKind.Information,
                     new SolidColorBrush(Colors.White), new SolidColorBrush(Colors.White))
            };

            Resources["ToastBackground"] = background;
            Resources["ToastIconBrush"] = iconBrush;
            Resources["ToastTextBrush"] = textBrush;
            ToastIcon.Kind = icon;
        }

        private void SetupAutoCloseTimer()
        {
            _autoCloseTimer = new DispatcherTimer();
            _autoCloseTimer.Tick += (s, e) => Close();
        }

        public void Show()
        {
            UpdateAppearance();
            
            // Start auto-close timer
            _autoCloseTimer.Interval = Duration;
            _autoCloseTimer.Start();

            // Play slide-in animation
            var slideIn = (Storyboard)Resources["SlideInAnimation"];
            slideIn.Begin(this);
        }

        public void Close()
        {
            _autoCloseTimer?.Stop();
            
            // Play slide-out animation
            var slideOut = (Storyboard)Resources["SlideOutAnimation"];
            slideOut.Completed += (s, e) => Closed?.Invoke(this, EventArgs.Empty);
            slideOut.Begin(this);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            _autoCloseTimer?.Stop();
            // Clean up resources
        }
    }
}
