﻿#pragma checksum "..\..\..\..\Windows\SupplierStatementWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9BFEA59B08D6840A22743E903E0BDEF1695ACBCB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Windows {
    
    
    /// <summary>
    /// SupplierStatementWindow
    /// </summary>
    public partial class SupplierStatementWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 112 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierNameValueTextBlock;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContactPersonTextBlock;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PhoneTextBlock;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EmailTextBlock;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TaxNumberTextBlock;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInvoicesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueInvoicesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastInvoiceDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoiceCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/windows/supplierstatementwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SupplierNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.SupplierNameValueTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ContactPersonTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.PhoneTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.EmailTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TaxNumberTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TotalInvoicesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TotalAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.PaidAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.RemainingAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.OverdueInvoicesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.LastInvoiceDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.InvoiceCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 16:
            this.PaymentCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 18:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 292 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 294 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 296 "..\..\..\..\Windows\SupplierStatementWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

