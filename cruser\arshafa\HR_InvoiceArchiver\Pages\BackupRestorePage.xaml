<UserControl x:Class="HR_InvoiceArchiver.Pages.BackupRestorePage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <materialDesign:PackIcon Grid.Column="0" Kind="Backup" 
                                       Width="32" Height="32" 
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                <StackPanel Grid.Column="1" Margin="16,0,0,0" VerticalAlignment="Center">
                    <TextBlock Text="النسخ الاحتياطي والاستعادة" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                    <TextBlock Text="إدارة النسخ الاحتياطية واستعادة البيانات" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Opacity="0.7"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="StatisticsButton" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="StatisticsButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartLine" Margin="0,0,8,0"/>
                            <TextBlock Text="الإحصائيات"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="ScheduleButton" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Click="ScheduleButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Schedule" Margin="0,0,8,0"/>
                            <TextBlock Text="الجدولة"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Content -->
        <TabControl Grid.Row="1" 
                  Style="{StaticResource MaterialDesignTabControl}"
                  Margin="16,0,16,16">

            <!-- Backup Tab -->
            <TabItem Header="النسخ الاحتياطي">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Margin="0,0,8,0"/>
                            <TextBlock Text="النسخ الاحتياطي"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                    <StackPanel>
                        <!-- Backup Options -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock Text="خيارات النسخ الاحتياطي" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Backup Type -->
                                    <ComboBox x:Name="BackupTypeComboBox"
                                            Grid.Row="0" Grid.Column="0"
                                            materialDesign:HintAssist.Hint="نوع النسخة الاحتياطية"
                                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                            Margin="0,0,8,16">
                                        <ComboBoxItem Content="نسخة كاملة" Tag="Full" IsSelected="True"/>
                                        <ComboBoxItem Content="نسخة تزايدية" Tag="Incremental"/>
                                        <ComboBoxItem Content="نسخة تفاضلية" Tag="Differential"/>
                                    </ComboBox>

                                    <!-- Backup Path -->
                                    <Grid Grid.Row="0" Grid.Column="1" Margin="8,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBox x:Name="BackupPathTextBox"
                                               Grid.Column="0"
                                               materialDesign:HintAssist.Hint="مسار النسخة الاحتياطية (اختياري)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,0"/>
                                        
                                        <Button x:Name="BrowseBackupPathButton"
                                              Grid.Column="1"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="BrowseBackupPathButton_Click">
                                            <materialDesign:PackIcon Kind="FolderOpen"/>
                                        </Button>
                                    </Grid>

                                    <!-- Description -->
                                    <TextBox x:Name="BackupDescriptionTextBox"
                                           Grid.Row="1" Grid.ColumnSpan="2"
                                           materialDesign:HintAssist.Hint="وصف النسخة الاحتياطية (اختياري)"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           Margin="0,0,0,16"/>

                                    <!-- Options Row 1 -->
                                    <StackPanel Grid.Row="2" Grid.Column="0" Orientation="Vertical" Margin="0,0,8,16">
                                        <CheckBox x:Name="IncludeAttachmentsCheckBox"
                                                Content="تضمين المرفقات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                IsChecked="True"
                                                Margin="0,0,0,8"/>

                                        <CheckBox x:Name="IncludeSettingsCheckBox"
                                                Content="تضمين الإعدادات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                IsChecked="True"
                                                Margin="0,0,0,8"/>

                                        <CheckBox x:Name="IncludeLogsCheckBox"
                                                Content="تضمين السجلات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,0,8"/>
                                    </StackPanel>

                                    <!-- Options Row 2 -->
                                    <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Vertical" Margin="8,0,0,16">
                                        <CheckBox x:Name="CompressBackupCheckBox"
                                                Content="ضغط النسخة الاحتياطية"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                IsChecked="True"
                                                Margin="0,0,0,8"/>

                                        <CheckBox x:Name="EncryptBackupCheckBox"
                                                Content="تشفير النسخة الاحتياطية"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,0,8"/>

                                        <PasswordBox x:Name="BackupPasswordBox"
                                                   materialDesign:HintAssist.Hint="كلمة مرور التشفير"
                                                   Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                                                   IsEnabled="{Binding IsChecked, ElementName=EncryptBackupCheckBox}"/>
                                    </StackPanel>

                                    <!-- Compression Level -->
                                    <ComboBox x:Name="CompressionLevelComboBox"
                                            Grid.Row="3" Grid.Column="0"
                                            materialDesign:HintAssist.Hint="مستوى الضغط"
                                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                            IsEnabled="{Binding IsChecked, ElementName=CompressBackupCheckBox}"
                                            Margin="0,0,8,0">
                                        <ComboBoxItem Content="بدون ضغط" Tag="None"/>
                                        <ComboBoxItem Content="سريع" Tag="Fastest"/>
                                        <ComboBoxItem Content="متوازن" Tag="Optimal" IsSelected="True"/>
                                        <ComboBoxItem Content="أقصى ضغط" Tag="Maximum"/>
                                    </ComboBox>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Backup Actions -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock Text="إجراءات النسخ الاحتياطي" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button x:Name="CreateFullBackupButton"
                                          Style="{StaticResource MaterialDesignRaisedButton}"
                                          Margin="0,0,8,0"
                                          Click="CreateFullBackupButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ContentSave" Margin="0,0,8,0"/>
                                            <TextBlock Text="إنشاء نسخة كاملة"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="CreateIncrementalBackupButton"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="8,0,8,0"
                                          Click="CreateIncrementalBackupButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ContentSaveMove" Margin="0,0,8,0"/>
                                            <TextBlock Text="نسخة تزايدية"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="CreateDifferentialBackupButton"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="8,0,0,0"
                                          Click="CreateDifferentialBackupButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ContentSaveEdit" Margin="0,0,8,0"/>
                                            <TextBlock Text="نسخة تفاضلية"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Progress -->
                        <materialDesign:Card x:Name="ProgressCard" Padding="16" Visibility="Collapsed">
                            <StackPanel>
                                <TextBlock Text="تقدم العملية" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <ProgressBar x:Name="BackupProgressBar"
                                           Style="{StaticResource MaterialDesignLinearProgressBar}"
                                           Height="8"
                                           Margin="0,0,0,8"/>
                                
                                <TextBlock x:Name="ProgressTextBlock"
                                         Text="جاري المعالجة..."
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         HorizontalAlignment="Center"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- Restore Tab -->
            <TabItem Header="الاستعادة">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Restore" Margin="0,0,8,0"/>
                            <TextBlock Text="الاستعادة"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                    <StackPanel>
                        <!-- Available Backups -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <Grid Margin="0,0,0,16">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0"
                                             Text="النسخ الاحتياطية المتاحة" 
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>
                                    
                                    <Button Grid.Column="1"
                                          x:Name="RefreshBackupsButton"
                                          Style="{StaticResource MaterialDesignIconButton}"
                                          Click="RefreshBackupsButton_Click">
                                        <materialDesign:PackIcon Kind="Refresh"/>
                                    </Button>
                                </Grid>
                                
                                <DataGrid x:Name="BackupsDataGrid"
                                        Style="{StaticResource MaterialDesignDataGrid}"
                                        AutoGenerateColumns="False"
                                        IsReadOnly="True"
                                        SelectionMode="Single"
                                        MaxHeight="300">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="*"/>
                                        <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="100"/>
                                        <DataGridTextColumn Header="الحجم" Binding="{Binding SizeBytes, StringFormat={}{0:N0} بايت}" Width="120"/>
                                        <DataGridTextColumn Header="التاريخ" Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm}" Width="140"/>
                                        <DataGridCheckBoxColumn Header="صحيح" Binding="{Binding IsValid}" Width="60"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Restore Options -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock Text="خيارات الاستعادة" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Restore Mode -->
                                    <ComboBox x:Name="RestoreModeComboBox"
                                            Grid.Row="0" Grid.Column="0"
                                            materialDesign:HintAssist.Hint="نمط الاستعادة"
                                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                            Margin="0,0,8,16">
                                        <ComboBoxItem Content="استعادة كاملة" Tag="Complete" IsSelected="True"/>
                                        <ComboBoxItem Content="البيانات فقط" Tag="DataOnly"/>
                                        <ComboBoxItem Content="الإعدادات فقط" Tag="SettingsOnly"/>
                                    </ComboBox>

                                    <!-- Password for encrypted backups -->
                                    <PasswordBox x:Name="RestorePasswordBox"
                                               Grid.Row="0" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="كلمة مرور فك التشفير (إن وجدت)"
                                               Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                                               Margin="8,0,0,16"/>

                                    <!-- Options -->
                                    <StackPanel Grid.Row="1" Grid.ColumnSpan="2" Orientation="Horizontal">
                                        <CheckBox x:Name="CreateBackupBeforeRestoreCheckBox"
                                                Content="إنشاء نسخة احتياطية قبل الاستعادة"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                IsChecked="True"
                                                Margin="0,0,16,0"/>

                                        <CheckBox x:Name="VerifyIntegrityCheckBox"
                                                Content="التحقق من سلامة البيانات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                IsChecked="True"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Restore Actions -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock Text="إجراءات الاستعادة" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button x:Name="TestRestoreButton"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="0,0,8,0"
                                          Click="TestRestoreButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="TestTube" Margin="0,0,8,0"/>
                                            <TextBlock Text="اختبار الاستعادة"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="ValidateBackupButton"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          Margin="8,0,8,0"
                                          Click="ValidateBackupButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="CheckCircle" Margin="0,0,8,0"/>
                                            <TextBlock Text="التحقق من النسخة"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="RestoreButton"
                                          Style="{StaticResource MaterialDesignRaisedButton}"
                                          Margin="8,0,0,0"
                                          Click="RestoreButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Restore" Margin="0,0,8,0"/>
                                            <TextBlock Text="استعادة"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- Management Tab -->
            <TabItem Header="الإدارة">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Settings" Margin="0,0,8,0"/>
                            <TextBlock Text="الإدارة"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                    <StackPanel>
                        <!-- Cleanup -->
                        <materialDesign:Card Padding="16">
                            <StackPanel>
                                <TextBlock Text="تنظيف النسخ الاحتياطية" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="0,0,0,16"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBox x:Name="CleanupDaysTextBox"
                                           Grid.Column="0"
                                           materialDesign:HintAssist.Hint="عدد الأيام للاحتفاظ بالنسخ"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           Text="30"
                                           Margin="0,0,8,0"/>
                                    
                                    <Button x:Name="CleanupButton"
                                          Grid.Column="1"
                                          Style="{StaticResource MaterialDesignRaisedButton}"
                                          Click="CleanupButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Delete" Margin="0,0,8,0"/>
                                            <TextBlock Text="تنظيف"/>
                                        </StackPanel>
                                    </Button>
                                </Grid>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- Loading Indicator -->
        <Grid Grid.RowSpan="2" x:Name="LoadingGrid" 
              Background="{DynamicResource MaterialDesignPaper}" 
              Opacity="0.8" 
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="48" Height="48"
                           Margin="0,0,0,16"/>
                <TextBlock x:Name="LoadingTextBlock" 
                         Text="جاري المعالجة..."
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
