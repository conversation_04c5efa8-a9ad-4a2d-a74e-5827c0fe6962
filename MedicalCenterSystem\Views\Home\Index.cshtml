﻿@{
    ViewData["Title"] = "الصفحة الرئيسية";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="jumbotron bg-primary text-white text-center p-5 rounded mb-4">
                <h1 class="display-4">مرحباً بك في نظام إدارة المركز الطبي</h1>
                <p class="lead">نظام شامل لإدارة المراجعين والمدفوعات والتقارير</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quick Actions -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-bolt"></i> الإجراءات السريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a asp-controller="PatientVisits" asp-action="Create" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-user-plus"></i> تسجيل مراجع جديد
                        </a>
                        <a asp-controller="MainPayments" asp-action="Create" class="btn btn-outline-success btn-lg">
                            <i class="fas fa-money-bill-wave"></i> إضافة دفع رئيسي
                        </a>
                        <a asp-controller="ReferralPayments" asp-action="Create" class="btn btn-outline-info btn-lg">
                            <i class="fas fa-exchange-alt"></i> إضافة تحويل
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-list"></i> القوائم الرئيسية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <a asp-controller="PatientVisits" asp-action="Index" class="btn btn-outline-dark w-100 mb-2">
                                <i class="fas fa-users"></i><br>المراجعين
                            </a>
                            <a asp-controller="Doctors" asp-action="Index" class="btn btn-outline-dark w-100 mb-2">
                                <i class="fas fa-user-md"></i><br>الأطباء
                            </a>
                        </div>
                        <div class="col-6">
                            <a asp-controller="MainPayments" asp-action="Index" class="btn btn-outline-dark w-100 mb-2">
                                <i class="fas fa-receipt"></i><br>المدفوعات الرئيسية
                            </a>
                            <a asp-controller="MedicalServices" asp-action="Index" class="btn btn-outline-dark w-100 mb-2">
                                <i class="fas fa-stethoscope"></i><br>الخدمات الطبية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-primary">المراجعين</h4>
                                <p class="text-muted">إدارة شاملة للمراجعين مع ترقيم تلقائي</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-success">المدفوعات</h4>
                                <p class="text-muted">تتبع المدفوعات الرئيسية والتحويلات</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-info">الأطباء</h4>
                                <p class="text-muted">إدارة الأطباء والتخصصات</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-warning">التقارير</h4>
                                <p class="text-muted">تقارير مفصلة للوارد والتحويلات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
}
