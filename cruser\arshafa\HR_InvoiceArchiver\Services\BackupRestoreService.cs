using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Data;

namespace HR_InvoiceArchiver.Services
{
    /// <summary>
    /// تطبيق خدمة النسخ الاحتياطي والاستعادة
    /// </summary>
    public class BackupRestoreService : IBackupRestoreService
    {
        private readonly DatabaseContext _context;
        private readonly ILoggingService _loggingService;
        private readonly IEncryptionService _encryptionService;
        private readonly ISecurityService _securityService;
        private readonly string _backupDirectory;
        private readonly List<BackupInfo> _backupHistory;

        public event EventHandler<BackupProgressEventArgs>? ProgressChanged;

        public BackupRestoreService(
            DatabaseContext context,
            ILoggingService loggingService,
            IEncryptionService encryptionService,
            ISecurityService securityService)
        {
            _context = context;
            _loggingService = loggingService;
            _encryptionService = encryptionService;
            _securityService = securityService;
            _backupHistory = new List<BackupInfo>();

            // إنشاء مجلد النسخ الاحتياطي
            _backupDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), 
                "HR_InvoiceArchiver", "Backups");
            Directory.CreateDirectory(_backupDirectory);
        }

        public async Task<BackupResult> CreateFullBackupAsync(BackupOptions options)
        {
            var result = new BackupResult { Type = BackupType.Full };
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                ReportProgress(0, "بدء النسخ الاحتياطي الكامل...");

                // تحديد مسار النسخة الاحتياطية
                if (string.IsNullOrEmpty(options.BackupPath))
                {
                    var fileName = $"full_backup_{DateTime.Now:yyyyMMdd_HHmmss}.bak";
                    options.BackupPath = Path.Combine(_backupDirectory, fileName);
                }

                // إنشاء مجلد مؤقت للنسخة الاحتياطية
                var tempDir = Path.Combine(Path.GetTempPath(), $"backup_{Guid.NewGuid()}");
                Directory.CreateDirectory(tempDir);

                try
                {
                    ReportProgress(10, "نسخ قاعدة البيانات...");
                    
                    // نسخ قاعدة البيانات
                    var dbBackupPath = await BackupDatabaseAsync(tempDir, options);
                    
                    ReportProgress(40, "نسخ الإعدادات...");
                    
                    // نسخ الإعدادات إذا طُلب ذلك
                    if (options.IncludeSettings)
                    {
                        await BackupSettingsAsync(tempDir);
                    }

                    ReportProgress(60, "نسخ المرفقات...");
                    
                    // نسخ المرفقات إذا طُلب ذلك
                    if (options.IncludeAttachments)
                    {
                        await BackupAttachmentsAsync(tempDir);
                    }

                    ReportProgress(80, "إنشاء البيانات الوصفية...");
                    
                    // إنشاء البيانات الوصفية
                    var metadata = await CreateBackupMetadataAsync(options);
                    var metadataPath = Path.Combine(tempDir, "metadata.json");
                    await File.WriteAllTextAsync(metadataPath, JsonSerializer.Serialize(metadata, new JsonSerializerOptions { WriteIndented = true }));

                    ReportProgress(90, "ضغط النسخة الاحتياطية...");
                    
                    // ضغط النسخة الاحتياطية إذا طُلب ذلك
                    if (options.CompressBackup)
                    {
                        await CompressDirectoryAsync(tempDir, options.BackupPath, options.CompressionLevel);
                    }
                    else
                    {
                        // نقل الملفات بدون ضغط
                        Directory.Move(tempDir, options.BackupPath);
                    }

                    // تشفير النسخة الاحتياطية إذا طُلب ذلك
                    if (options.EncryptBackup && !string.IsNullOrEmpty(options.Password))
                    {
                        ReportProgress(95, "تشفير النسخة الاحتياطية...");
                        options.BackupPath = await EncryptBackupAsync(options.BackupPath, options.Password);
                    }

                    // حساب checksum
                    result.ChecksumMD5 = await CalculateChecksumAsync(options.BackupPath);

                    // حساب النتائج
                    var fileInfo = new FileInfo(options.BackupPath);
                    result.Success = true;
                    result.BackupPath = options.BackupPath;
                    result.BackupSizeBytes = fileInfo.Length;
                    result.Duration = stopwatch.Elapsed;
                    result.Metadata = metadata;

                    // إضافة إلى السجل
                    await AddToBackupHistoryAsync(result, options);

                    ReportProgress(100, "تم إنشاء النسخة الاحتياطية بنجاح");
                    await _loggingService.LogInformationAsync($"تم إنشاء نسخة احتياطية كاملة: {options.BackupPath}");
                }
                finally
                {
                    // تنظيف المجلد المؤقت
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                await _loggingService.LogErrorAsync("فشل في إنشاء النسخة الاحتياطية الكاملة", ex);
            }

            return result;
        }

        public async Task<BackupResult> CreateIncrementalBackupAsync(BackupOptions options)
        {
            var result = new BackupResult { Type = BackupType.Incremental };
            
            try
            {
                ReportProgress(0, "بدء النسخ الاحتياطي التزايدي...");

                // البحث عن آخر نسخة احتياطية
                var lastBackup = _backupHistory
                    .Where(b => b.Type == BackupType.Full || b.Type == BackupType.Incremental)
                    .OrderByDescending(b => b.CreatedAt)
                    .FirstOrDefault();

                if (lastBackup == null)
                {
                    // إذا لم توجد نسخة سابقة، إنشاء نسخة كاملة
                    return await CreateFullBackupAsync(options);
                }

                // تحديد التغييرات منذ آخر نسخة احتياطية
                options.FromDate = lastBackup.CreatedAt;
                
                // إنشاء النسخة التزايدية (مبسطة)
                result = await CreateFullBackupAsync(options);
                result.Type = BackupType.Incremental;

                await _loggingService.LogInformationAsync($"تم إنشاء نسخة احتياطية تزايدية: {result.BackupPath}");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                await _loggingService.LogErrorAsync("فشل في إنشاء النسخة الاحتياطية التزايدية", ex);
            }

            return result;
        }

        public async Task<BackupResult> CreateDifferentialBackupAsync(BackupOptions options)
        {
            var result = new BackupResult { Type = BackupType.Differential };
            
            try
            {
                ReportProgress(0, "بدء النسخ الاحتياطي التفاضلي...");

                // البحث عن آخر نسخة احتياطية كاملة
                var lastFullBackup = _backupHistory
                    .Where(b => b.Type == BackupType.Full)
                    .OrderByDescending(b => b.CreatedAt)
                    .FirstOrDefault();

                if (lastFullBackup == null)
                {
                    // إذا لم توجد نسخة كاملة، إنشاء نسخة كاملة
                    return await CreateFullBackupAsync(options);
                }

                // تحديد التغييرات منذ آخر نسخة كاملة
                options.FromDate = lastFullBackup.CreatedAt;
                
                // إنشاء النسخة التفاضلية (مبسطة)
                result = await CreateFullBackupAsync(options);
                result.Type = BackupType.Differential;

                await _loggingService.LogInformationAsync($"تم إنشاء نسخة احتياطية تفاضلية: {result.BackupPath}");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                await _loggingService.LogErrorAsync("فشل في إنشاء النسخة الاحتياطية التفاضلية", ex);
            }

            return result;
        }

        public async Task<RestoreResult> RestoreFromBackupAsync(RestoreOptions options)
        {
            var result = new RestoreResult();
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                ReportProgress(0, "بدء عملية الاستعادة...");

                // التحقق من وجود الملف
                if (!File.Exists(options.BackupPath))
                {
                    throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود");
                }

                // إنشاء نسخة احتياطية قبل الاستعادة إذا طُلب ذلك
                if (options.CreateBackupBeforeRestore)
                {
                    ReportProgress(10, "إنشاء نسخة احتياطية قبل الاستعادة...");
                    
                    var preRestoreBackup = await CreateFullBackupAsync(new BackupOptions
                    {
                        Description = "نسخة احتياطية قبل الاستعادة"
                    });
                    
                    if (preRestoreBackup.Success)
                    {
                        result.BackupCreatedBeforeRestore = preRestoreBackup.BackupPath;
                    }
                }

                ReportProgress(30, "فك ضغط النسخة الاحتياطية...");
                
                // فك ضغط النسخة الاحتياطية
                var tempDir = Path.Combine(Path.GetTempPath(), $"restore_{Guid.NewGuid()}");
                await DecompressBackupToDirectoryAsync(options.BackupPath, tempDir);

                try
                {
                    ReportProgress(50, "استعادة قاعدة البيانات...");
                    
                    // استعادة قاعدة البيانات
                    if (options.RestoreData)
                    {
                        await RestoreDatabaseAsync(tempDir);
                        result.TablesRestored = await CountTablesAsync();
                        result.RecordsRestored = await CountRecordsAsync();
                    }

                    ReportProgress(70, "استعادة الإعدادات...");
                    
                    // استعادة الإعدادات
                    if (options.RestoreSettings)
                    {
                        await RestoreSettingsAsync(tempDir);
                    }

                    ReportProgress(90, "استعادة المرفقات...");
                    
                    // استعادة المرفقات
                    if (options.RestoreAttachments)
                    {
                        await RestoreAttachmentsAsync(tempDir);
                    }

                    result.Success = true;
                    result.Duration = stopwatch.Elapsed;

                    ReportProgress(100, "تمت الاستعادة بنجاح");
                    await _loggingService.LogInformationAsync($"تمت استعادة النسخة الاحتياطية: {options.BackupPath}");
                }
                finally
                {
                    // تنظيف المجلد المؤقت
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                await _loggingService.LogErrorAsync("فشل في استعادة النسخة الاحتياطية", ex);
            }

            return result;
        }

        public async Task<BackupStatistics> GetBackupStatisticsAsync()
        {
            try
            {
                var backups = await GetAvailableBackupsAsync();

                return new BackupStatistics
                {
                    TotalBackups = backups.Count,
                    TotalBackupSizeBytes = backups.Sum(b => b.SizeBytes),
                    LastBackupDate = backups.FirstOrDefault()?.CreatedAt,
                    SuccessfulBackups = backups.Count(b => b.IsValid),
                    FailedBackups = backups.Count(b => !b.IsValid),
                    AverageBackupSizeBytes = backups.Any() ? backups.Average(b => b.SizeBytes) : 0,
                    RecentBackups = backups.Take(5).ToList()
                };
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في الحصول على إحصائيات النسخ الاحتياطي", ex);
                return new BackupStatistics();
            }
        }

        public async Task<TestRestoreResult> TestRestoreAsync(string backupPath)
        {
            var result = new TestRestoreResult();

            try
            {
                var validation = await ValidateBackupAsync(backupPath);
                result.CanRestore = validation.IsValid;
                result.Issues.AddRange(validation.Errors);
                result.Warnings.AddRange(validation.Warnings);

                if (result.CanRestore)
                {
                    result.EstimatedRestoreTime = TimeSpan.FromMinutes(5); // تقدير
                }

                await _loggingService.LogInformationAsync($"تم اختبار إمكانية الاستعادة: {backupPath}");
            }
            catch (Exception ex)
            {
                result.CanRestore = false;
                result.Issues.Add($"خطأ في اختبار الاستعادة: {ex.Message}");
                await _loggingService.LogErrorAsync("فشل في اختبار الاستعادة", ex);
            }

            return result;
        }

        #region Helper Methods

        private void ReportProgress(int percentage, string operation)
        {
            ProgressChanged?.Invoke(this, new BackupProgressEventArgs
            {
                PercentageComplete = percentage,
                CurrentOperation = operation
            });
        }

        private async Task<string> BackupDatabaseAsync(string tempDir, BackupOptions options)
        {
            // محاكاة نسخ قاعدة البيانات
            var dbPath = Path.Combine(tempDir, "database.db");
            await File.WriteAllTextAsync(dbPath, "Database backup content");
            return dbPath;
        }

        private async Task BackupSettingsAsync(string tempDir)
        {
            // محاكاة نسخ الإعدادات
            var settingsPath = Path.Combine(tempDir, "settings.json");
            await File.WriteAllTextAsync(settingsPath, "{}");
        }

        private async Task BackupAttachmentsAsync(string tempDir)
        {
            // محاكاة نسخ المرفقات
            var attachmentsDir = Path.Combine(tempDir, "attachments");
            Directory.CreateDirectory(attachmentsDir);
            await File.WriteAllTextAsync(Path.Combine(attachmentsDir, "sample.txt"), "Sample attachment");
        }

        private async Task<BackupMetadata> CreateBackupMetadataAsync(BackupOptions options)
        {
            var user = await _securityService.GetCurrentUserAsync();

            return new BackupMetadata
            {
                ApplicationVersion = "1.0.0",
                DatabaseVersion = "1.0",
                TotalTables = 5,
                TotalRecords = await CountRecordsAsync(),
                IncludesAttachments = options.IncludeAttachments,
                IncludesSettings = options.IncludeSettings,
                IncludesLogs = options.IncludeLogs,
                CreatedBy = user?.Username ?? "System"
            };
        }

        private async Task CompressDirectoryAsync(string sourceDir, string targetPath, CompressionLevel level)
        {
            var compressionLevel = level switch
            {
                CompressionLevel.Fastest => System.IO.Compression.CompressionLevel.Fastest,
                CompressionLevel.Maximum => System.IO.Compression.CompressionLevel.SmallestSize,
                _ => System.IO.Compression.CompressionLevel.Optimal
            };

            await Task.Run(() => ZipFile.CreateFromDirectory(sourceDir, targetPath, compressionLevel, false));
        }

        private async Task DecompressBackupToDirectoryAsync(string backupPath, string targetDir)
        {
            await Task.Run(() =>
            {
                if (Path.GetExtension(backupPath).ToLower() == ".zip")
                {
                    ZipFile.ExtractToDirectory(backupPath, targetDir);
                }
                else
                {
                    // إذا لم يكن مضغوطاً، نسخ الملف
                    Directory.CreateDirectory(targetDir);
                    var targetFile = Path.Combine(targetDir, Path.GetFileName(backupPath));
                    File.Copy(backupPath, targetFile);
                }
            });
        }

        private async Task RestoreDatabaseAsync(string tempDir)
        {
            // محاكاة استعادة قاعدة البيانات
            await Task.Delay(1000);
        }

        private async Task RestoreSettingsAsync(string tempDir)
        {
            // محاكاة استعادة الإعدادات
            await Task.Delay(500);
        }

        private async Task RestoreAttachmentsAsync(string tempDir)
        {
            // محاكاة استعادة المرفقات
            await Task.Delay(500);
        }

        private async Task<int> CountTablesAsync()
        {
            // عدد الجداول في قاعدة البيانات
            return await Task.FromResult(5);
        }

        private async Task<int> CountRecordsAsync()
        {
            try
            {
                var invoiceCount = await _context.Invoices.CountAsync();
                var supplierCount = await _context.Suppliers.CountAsync();
                var paymentCount = await _context.Payments.CountAsync();

                return invoiceCount + supplierCount + paymentCount;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<string> CalculateChecksumAsync(string filePath)
        {
            try
            {
                using var md5 = MD5.Create();
                using var stream = File.OpenRead(filePath);
                var hash = await Task.Run(() => md5.ComputeHash(stream));
                return Convert.ToHexString(hash);
            }
            catch
            {
                return string.Empty;
            }
        }

        private async Task AddToBackupHistoryAsync(BackupResult result, BackupOptions options)
        {
            await Task.Run(() =>
            {
                var backupInfo = new BackupInfo
                {
                    Name = Path.GetFileNameWithoutExtension(result.BackupPath),
                    Path = result.BackupPath,
                    Type = result.Type,
                    SizeBytes = result.BackupSizeBytes,
                    CreatedAt = result.CreatedAt,
                    Description = options.Description,
                    IsCompressed = options.CompressBackup,
                    IsEncrypted = options.EncryptBackup,
                    ChecksumMD5 = result.ChecksumMD5,
                    Metadata = result.Metadata,
                    IsValid = result.Success
                };

                _backupHistory.Add(backupInfo);

                // الاحتفاظ بآخر 100 نسخة في السجل
                if (_backupHistory.Count > 100)
                {
                    _backupHistory.RemoveAt(0);
                }
            });
        }

        #endregion

        #region Missing Interface Methods

        public async Task<ValidationResult> ValidateBackupAsync(string backupPath)
        {
            var result = new ValidationResult();

            try
            {
                if (!File.Exists(backupPath))
                {
                    result.Errors.Add("ملف النسخة الاحتياطية غير موجود");
                    return result;
                }

                var fileInfo = new FileInfo(backupPath);
                if (fileInfo.Length == 0)
                {
                    result.Errors.Add("ملف النسخة الاحتياطية فارغ");
                    return result;
                }

                // التحقق من صحة الملف
                if (Path.GetExtension(backupPath).ToLower() == ".zip")
                {
                    try
                    {
                        using var archive = ZipFile.OpenRead(backupPath);
                        if (archive.Entries.Count == 0)
                        {
                            result.Errors.Add("النسخة الاحتياطية المضغوطة فارغة");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"خطأ في قراءة النسخة الاحتياطية المضغوطة: {ex.Message}");
                    }
                }

                result.IsValid = result.Errors.Count == 0;
                await _loggingService.LogInformationAsync($"تم التحقق من صحة النسخة الاحتياطية: {backupPath}");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في التحقق من النسخة الاحتياطية: {ex.Message}");
                await _loggingService.LogErrorAsync("فشل في التحقق من النسخة الاحتياطية", ex);
            }

            return result;
        }

        public async Task<List<BackupInfo>> GetAvailableBackupsAsync()
        {
            try
            {
                // إرجاع النسخ من السجل المحلي
                var availableBackups = _backupHistory
                    .Where(b => File.Exists(b.Path))
                    .OrderByDescending(b => b.CreatedAt)
                    .ToList();

                await _loggingService.LogInformationAsync($"تم العثور على {availableBackups.Count} نسخة احتياطية متاحة");
                return availableBackups;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في الحصول على قائمة النسخ الاحتياطية", ex);
                return new List<BackupInfo>();
            }
        }

        public async Task<int> CleanupOldBackupsAsync(int daysToKeep = 30)
        {
            var deletedCount = 0;

            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var oldBackups = _backupHistory
                    .Where(b => b.CreatedAt < cutoffDate && File.Exists(b.Path))
                    .ToList();

                foreach (var backup in oldBackups)
                {
                    try
                    {
                        File.Delete(backup.Path);
                        _backupHistory.Remove(backup);
                        deletedCount++;
                    }
                    catch (Exception ex)
                    {
                        await _loggingService.LogWarningAsync($"فشل في حذف النسخة الاحتياطية {backup.Path}: {ex.Message}");
                    }
                }

                await _loggingService.LogInformationAsync($"تم حذف {deletedCount} نسخة احتياطية قديمة");
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في تنظيف النسخ الاحتياطية القديمة", ex);
            }

            return deletedCount;
        }

        public async Task ScheduleAutomaticBackupAsync(BackupSchedule schedule)
        {
            try
            {
                // محاكاة جدولة النسخ الاحتياطي التلقائي
                // في التطبيق الحقيقي، يمكن استخدام Windows Task Scheduler أو Quartz.NET

                await _loggingService.LogInformationAsync($"تم جدولة النسخ الاحتياطي التلقائي: {schedule.Frequency}");
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في جدولة النسخ الاحتياطي التلقائي", ex);
                throw;
            }
        }

        public async Task UnscheduleAutomaticBackupAsync()
        {
            try
            {
                // محاكاة إلغاء جدولة النسخ الاحتياطي التلقائي
                await _loggingService.LogInformationAsync("تم إلغاء جدولة النسخ الاحتياطي التلقائي");
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في إلغاء جدولة النسخ الاحتياطي التلقائي", ex);
                throw;
            }
        }

        public async Task<string> CompressBackupAsync(string backupPath, CompressionLevel compressionLevel)
        {
            try
            {
                var compressedPath = Path.ChangeExtension(backupPath, ".zip");

                if (Directory.Exists(backupPath))
                {
                    await CompressDirectoryAsync(backupPath, compressedPath, compressionLevel);
                }
                else if (File.Exists(backupPath))
                {
                    // ضغط ملف واحد
                    using var archive = ZipFile.Open(compressedPath, ZipArchiveMode.Create);
                    archive.CreateEntryFromFile(backupPath, Path.GetFileName(backupPath));
                }

                await _loggingService.LogInformationAsync($"تم ضغط النسخة الاحتياطية: {compressedPath}");
                return compressedPath;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في ضغط النسخة الاحتياطية", ex);
                throw;
            }
        }

        public async Task<string> DecompressBackupAsync(string compressedBackupPath)
        {
            try
            {
                var extractPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
                Directory.CreateDirectory(extractPath);

                ZipFile.ExtractToDirectory(compressedBackupPath, extractPath);

                await _loggingService.LogInformationAsync($"تم إلغاء ضغط النسخة الاحتياطية إلى: {extractPath}");
                return extractPath;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في إلغاء ضغط النسخة الاحتياطية", ex);
                throw;
            }
        }

        public async Task<string> EncryptBackupAsync(string backupPath, string password)
        {
            try
            {
                // محاكاة تشفير النسخة الاحتياطية
                // في التطبيق الحقيقي، يمكن استخدام AES أو خوارزميات تشفير أخرى

                var encryptedPath = backupPath + ".encrypted";

                // نسخ الملف مع إضافة امتداد التشفير (محاكاة)
                File.Copy(backupPath, encryptedPath, true);

                await _loggingService.LogInformationAsync($"تم تشفير النسخة الاحتياطية: {encryptedPath}");
                return encryptedPath;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في تشفير النسخة الاحتياطية", ex);
                throw;
            }
        }

        public async Task<string> DecryptBackupAsync(string encryptedBackupPath, string password)
        {
            try
            {
                // محاكاة فك تشفير النسخة الاحتياطية
                var decryptedPath = encryptedBackupPath.Replace(".encrypted", "");

                // نسخ الملف مع إزالة امتداد التشفير (محاكاة)
                File.Copy(encryptedBackupPath, decryptedPath, true);

                await _loggingService.LogInformationAsync($"تم فك تشفير النسخة الاحتياطية: {decryptedPath}");
                return decryptedPath;
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في فك تشفير النسخة الاحتياطية", ex);
                throw;
            }
        }

        #endregion
    }
}
