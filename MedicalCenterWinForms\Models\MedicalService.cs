using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MedicalCenterWinForms.Models
{
    public class MedicalService
    {
        public int MedicalServiceId { get; set; }

        [Required]
        [StringLength(100)]
        public string ServiceName { get; set; } = string.Empty; // كشفية، فحص، مختبر، سونار...

        [StringLength(20)]
        public string ServiceType { get; set; } = string.Empty; // Direct / Referral

        public bool IsCenterService { get; set; } // True إذا كانت تابعة للمركز

        [Column(TypeName = "decimal(18,2)")]
        public decimal? DefaultPrice { get; set; }

        // Navigation Properties
        public virtual ICollection<DoctorService> DoctorServices { get; set; } = new List<DoctorService>();
        public virtual ICollection<ReferralPayment> ReferralPayments { get; set; } = new List<ReferralPayment>();
    }
}
