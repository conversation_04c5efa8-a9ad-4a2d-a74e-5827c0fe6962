@model MedicalCenterSystem.Models.DoctorService

@{
    ViewData["Title"] = "إضافة ربط طبيب بخدمة";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إضافة ربط طبيب بخدمة
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Create" id="doctorServiceForm">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <!-- Doctor Selection -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="DoctorId" class="form-label">الطبيب <span class="text-danger">*</span></label>
                                <select asp-for="DoctorId" class="form-select" asp-items="ViewBag.DoctorId" id="doctorSelect">
                                    <option value="">اختر الطبيب...</option>
                                </select>
                                <span asp-validation-for="DoctorId" class="text-danger"></span>
                            </div>

                            <!-- Medical Service Selection -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="MedicalServiceId" class="form-label">الخدمة الطبية <span class="text-danger">*</span></label>
                                <select asp-for="MedicalServiceId" class="form-select" asp-items="ViewBag.MedicalServiceId" id="serviceSelect">
                                    <option value="">اختر الخدمة...</option>
                                </select>
                                <span asp-validation-for="MedicalServiceId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Link Type -->
                            <div class="col-md-6 mb-3">
                                <label asp-for="LinkType" class="form-label">نوع الربط <span class="text-danger">*</span></label>
                                <select asp-for="LinkType" class="form-select" asp-items="ViewBag.LinkTypes">
                                    <option value="">اختر نوع الربط...</option>
                                </select>
                                <span asp-validation-for="LinkType" class="text-danger"></span>
                            </div>

                            <!-- Is Active -->
                            <div class="col-md-6 mb-3 d-flex align-items-end">
                                <div class="form-check">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" checked>
                                    <label asp-for="IsActive" class="form-check-label">نشط</label>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Configuration -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">إعدادات المدفوعات</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- Has Percentage -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check">
                                            <input asp-for="HasPercentage" class="form-check-input" type="checkbox" id="hasPercentageCheck">
                                            <label asp-for="HasPercentage" class="form-check-label">له نسبة مئوية</label>
                                        </div>
                                        <div class="mt-2" id="percentageGroup" style="display: none;">
                                            <label asp-for="Percentage" class="form-label">النسبة %</label>
                                            <input asp-for="Percentage" class="form-control" type="number" step="0.01" min="0" max="100" placeholder="0.00">
                                            <span asp-validation-for="Percentage" class="text-danger"></span>
                                        </div>
                                    </div>

                                    <!-- Is Fixed Amount -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check">
                                            <input asp-for="IsFixedAmount" class="form-check-input" type="checkbox" id="isFixedAmountCheck">
                                            <label asp-for="IsFixedAmount" class="form-check-label">مبلغ مقطوع</label>
                                        </div>
                                        <div class="mt-2" id="fixedAmountGroup" style="display: none;">
                                            <label asp-for="FixedAmount" class="form-label">المبلغ المقطوع</label>
                                            <input asp-for="FixedAmount" class="form-control" type="number" step="0.01" min="0" placeholder="0.00">
                                            <span asp-validation-for="FixedAmount" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Doctor Default Price -->
                                    <div class="col-md-6 mb-3">
                                        <label asp-for="DoctorDefaultPrice" class="form-label">السعر الافتراضي للطبيب</label>
                                        <input asp-for="DoctorDefaultPrice" class="form-control" type="number" step="0.01" min="0" placeholder="0.00">
                                        <span asp-validation-for="DoctorDefaultPrice" class="text-danger"></span>
                                        <div class="form-text">السعر الذي سيظهر افتراضياً لهذا الطبيب في هذه الخدمة</div>
                                    </div>

                                    <!-- Service Cost -->
                                    <div class="col-md-6 mb-3">
                                        <label asp-for="ServiceCost" class="form-label">تكلفة الخدمة</label>
                                        <input asp-for="ServiceCost" class="form-control" type="number" step="0.01" min="0" placeholder="0.00">
                                        <span asp-validation-for="ServiceCost" class="text-danger"></span>
                                        <div class="form-text">التكلفة التي سيتم خصمها من المبلغ قبل حساب النسبة</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Calculation Test -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">اختبار الحساب</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="testAmount" class="form-label">مبلغ الاختبار</label>
                                        <input type="number" class="form-control" id="testAmount" value="1000" step="0.01" min="0">
                                    </div>
                                    <div class="col-md-6 mb-3 d-flex align-items-end">
                                        <button type="button" class="btn btn-info" onclick="calculateTest()">
                                            <i class="fas fa-calculator me-1"></i> احسب
                                        </button>
                                    </div>
                                </div>
                                <div id="calculationResult" class="alert alert-light" style="display: none;">
                                    <strong>نتيجة الحساب:</strong>
                                    <div id="resultText"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-3">
                            <label asp-for="Notes" class="form-label">ملاحظات</label>
                            <textarea asp-for="Notes" class="form-control" rows="3" placeholder="ملاحظات إضافية..."></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i> حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Toggle percentage input
            $('#hasPercentageCheck').change(function() {
                if (this.checked) {
                    $('#percentageGroup').show();
                    $('#isFixedAmountCheck').prop('checked', false);
                    $('#fixedAmountGroup').hide();
                } else {
                    $('#percentageGroup').hide();
                }
            });

            // Toggle fixed amount input
            $('#isFixedAmountCheck').change(function() {
                if (this.checked) {
                    $('#fixedAmountGroup').show();
                    $('#hasPercentageCheck').prop('checked', false);
                    $('#percentageGroup').hide();
                } else {
                    $('#fixedAmountGroup').hide();
                }
            });

            // Form validation
            $('#doctorServiceForm').submit(function(e) {
                const hasPercentage = $('#hasPercentageCheck').is(':checked');
                const isFixedAmount = $('#isFixedAmountCheck').is(':checked');

                if (!hasPercentage && !isFixedAmount) {
                    e.preventDefault();
                    alert('يجب اختيار إما النسبة المئوية أو المبلغ المقطوع');
                    return false;
                }

                if (hasPercentage && !$('#Percentage').val()) {
                    e.preventDefault();
                    alert('يرجى تحديد النسبة المئوية');
                    $('#Percentage').focus();
                    return false;
                }

                if (isFixedAmount && !$('#FixedAmount').val()) {
                    e.preventDefault();
                    alert('يرجى تحديد المبلغ المقطوع');
                    $('#FixedAmount').focus();
                    return false;
                }
            });
        });

        function calculateTest() {
            const testAmount = parseFloat($('#testAmount').val()) || 0;
            const hasPercentage = $('#hasPercentageCheck').is(':checked');
            const isFixedAmount = $('#isFixedAmountCheck').is(':checked');
            const percentage = parseFloat($('#Percentage').val()) || 0;
            const fixedAmount = parseFloat($('#FixedAmount').val()) || 0;
            const serviceCost = parseFloat($('#ServiceCost').val()) || 0;

            let doctorShare = 0;
            let centerShare = testAmount;

            if (isFixedAmount) {
                doctorShare = fixedAmount;
            } else if (hasPercentage) {
                const netAmount = testAmount - serviceCost;
                if (netAmount > 0) {
                    doctorShare = netAmount * (percentage / 100);
                }
            }

            centerShare = testAmount - doctorShare;

            const resultHtml = `
                <div class="row">
                    <div class="col-md-4">
                        <strong>المبلغ الكلي:</strong> ${testAmount.toFixed(2)} ريال
                    </div>
                    <div class="col-md-4">
                        <strong>حصة الطبيب:</strong> ${doctorShare.toFixed(2)} ريال
                    </div>
                    <div class="col-md-4">
                        <strong>حصة المركز:</strong> ${centerShare.toFixed(2)} ريال
                    </div>
                </div>
            `;

            $('#resultText').html(resultHtml);
            $('#calculationResult').show();
        }
    </script>
}

@section Styles {
    <style>
        .card {
            border: none;
            border-radius: 10px;
        }
        
        .card-header {
            border-radius: 10px 10px 0 0;
        }
        
        .form-check-input:checked {
            background-color: #198754;
            border-color: #198754;
        }
        
        .alert-light {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }
        
        .form-text {
            font-size: 0.875em;
            color: #6c757d;
        }
    </style>
}
