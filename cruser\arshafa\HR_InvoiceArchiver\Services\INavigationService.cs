using System;
using System.Windows.Controls;

namespace HR_InvoiceArchiver.Services
{
    public interface INavigationService
    {
        event EventHandler<NavigationEventArgs> Navigated;
        
        void NavigateTo<T>() where T : UserControl, new();
        void NavigateTo<T>(object parameter) where T : UserControl, new();
        void NavigateTo(Type pageType);
        void NavigateTo(Type pageType, object parameter);
        void GoBack();
        bool CanGoBack { get; }
        
        UserControl? CurrentPage { get; }
        string CurrentPageTitle { get; }
    }

    public class NavigationEventArgs : EventArgs
    {
        public UserControl Page { get; set; } = null!;
        public string Title { get; set; } = string.Empty;
        public object? Parameter { get; set; }
    }
}
