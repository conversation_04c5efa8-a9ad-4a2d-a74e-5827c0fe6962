using HR_InvoiceArchiver.Data.Repositories;
using HR_InvoiceArchiver.Models;
using System.IO;

namespace HR_InvoiceArchiver.Services
{
    public interface IPaymentService
    {
        Task<IEnumerable<Payment>> GetAllPaymentsAsync();
        Task<Payment?> GetPaymentByIdAsync(int id);
        Task<Payment?> GetPaymentByReceiptNumberAsync(string receiptNumber);
        Task<IEnumerable<Payment>> GetPaymentsByInvoiceAsync(int invoiceId);
        Task<IEnumerable<Payment>> GetPaymentsBySupplierAsync(int supplierId);
        Task<Payment> CreatePaymentAsync(Payment payment);
        Task<Payment> UpdatePaymentAsync(Payment payment);
        Task<bool> DeletePaymentAsync(int id);
        Task<bool> PaymentExistsAsync(int id);
        Task<bool> ReceiptNumberExistsAsync(string receiptNumber, int? excludeId = null);
        Task<IEnumerable<Payment>> SearchPaymentsAsync(string searchTerm);
        Task<IEnumerable<Payment>> GetFilteredPaymentsAsync(PaymentFilter filter);
        Task<IEnumerable<Payment>> GetRecentPaymentsAsync(int count = 10);
        Task<bool> ValidatePaymentAsync(Payment payment);
        Task<PaymentStatistics> GetPaymentStatisticsAsync();
        Task<IEnumerable<MonthlyPaymentStatistics>> GetMonthlyPaymentStatisticsAsync(int months = 12);
        Task<DailyPaymentStatistics> GetTodayPaymentStatisticsAsync();
        Task<MonthlyPaymentStatistics> GetCurrentMonthPaymentStatisticsAsync();
        Task<bool> SaveMultiPaymentAsync(MultiPaymentModel multiPayment);
    }

    public class PaymentStatistics
    {
        public int TotalPayments { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AveragePaymentAmount { get; set; }
        public DateTime? LastPaymentDate { get; set; }
        public IEnumerable<PaymentMethodStatistics> PaymentMethodStats { get; set; } = new List<PaymentMethodStatistics>();
        public IEnumerable<SupplierPaymentStatistics> TopSuppliersByPayments { get; set; } = new List<SupplierPaymentStatistics>();
    }

    public class PaymentMethodStatistics
    {
        public PaymentMethod Method { get; set; }
        public string MethodName { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal Percentage { get; set; }
    }

    public class SupplierPaymentStatistics
    {
        public string SupplierName { get; set; } = string.Empty;
        public int PaymentCount { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class MonthlyPaymentStatistics
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public int PaymentCount { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class DailyPaymentStatistics
    {
        public DateTime Date { get; set; }
        public int PaymentCount { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class PaymentService : IPaymentService
    {
        private readonly IPaymentRepository _paymentRepository;
        private readonly IInvoiceRepository _invoiceRepository;

        public PaymentService(
            IPaymentRepository paymentRepository,
            IInvoiceRepository invoiceRepository)
        {
            _paymentRepository = paymentRepository;
            _invoiceRepository = invoiceRepository;
        }

        public async Task<IEnumerable<Payment>> GetAllPaymentsAsync()
        {
            return await _paymentRepository.GetAllAsync();
        }

        public async Task<Payment?> GetPaymentByIdAsync(int id)
        {
            return await _paymentRepository.GetByIdAsync(id);
        }

        public async Task<Payment?> GetPaymentByReceiptNumberAsync(string receiptNumber)
        {
            return await _paymentRepository.GetByReceiptNumberAsync(receiptNumber);
        }

        public async Task<IEnumerable<Payment>> GetPaymentsByInvoiceAsync(int invoiceId)
        {
            return await _paymentRepository.GetByInvoiceIdAsync(invoiceId);
        }

        public async Task<IEnumerable<Payment>> GetPaymentsBySupplierAsync(int supplierId)
        {
            return await _paymentRepository.GetBySupplierIdAsync(supplierId);
        }

        public async Task<Payment> CreatePaymentAsync(Payment payment)
        {
            if (!await ValidatePaymentAsync(payment))
                throw new ArgumentException("بيانات الوصل غير صحيحة");

            if (await _paymentRepository.ExistsByReceiptNumberAsync(payment.ReceiptNumber))
                throw new ArgumentException("يوجد وصل بنفس الرقم مسبقاً");

            var invoice = await _invoiceRepository.GetByIdAsync(payment.InvoiceId);
            if (invoice == null)
                throw new ArgumentException("الفاتورة غير موجودة");

            if (!invoice.CanAddPayment())
                throw new ArgumentException("الفاتورة مسددة بالكامل أو لا تحتاج لمدفوعات إضافية");

            if (payment.Amount > invoice.RemainingAmount)
                throw new ArgumentException($"مبلغ الوصل أكبر من المبلغ المتبقي ({invoice.RemainingAmount:N0} د.ع)");

            var createdPayment = await _paymentRepository.AddAsync(payment);

            // Update invoice status after adding payment
            invoice.UpdateStatus();
            await _invoiceRepository.UpdateAsync(invoice);

            return createdPayment;
        }

        public async Task<Payment> UpdatePaymentAsync(Payment payment)
        {
            if (!await ValidatePaymentAsync(payment))
                throw new ArgumentException("بيانات الوصل غير صحيحة");

            if (!await _paymentRepository.ExistsAsync(payment.Id))
                throw new ArgumentException("الوصل غير موجود");

            if (await _paymentRepository.ExistsByReceiptNumberAsync(payment.ReceiptNumber, payment.Id))
                throw new ArgumentException("يوجد وصل آخر بنفس الرقم");

            var invoice = await _invoiceRepository.GetByIdAsync(payment.InvoiceId);
            if (invoice == null)
                throw new ArgumentException("الفاتورة غير موجودة");

            // Get the original payment to calculate the difference
            var originalPayment = await _paymentRepository.GetByIdAsync(payment.Id);
            if (originalPayment == null)
                throw new ArgumentException("الوصل الأصلي غير موجود");

            var amountDifference = payment.Amount - originalPayment.Amount;
            var newRemainingAmount = invoice.RemainingAmount - amountDifference;

            if (newRemainingAmount < 0)
                throw new ArgumentException($"المبلغ الجديد يتجاوز المبلغ المتبقي للفاتورة");

            var updatedPayment = await _paymentRepository.UpdateAsync(payment);

            // Update invoice status after updating payment
            invoice.UpdateStatus();
            await _invoiceRepository.UpdateAsync(invoice);

            return updatedPayment;
        }

        public async Task<bool> DeletePaymentAsync(int id)
        {
            var payment = await _paymentRepository.GetByIdAsync(id);
            if (payment == null)
                return false;

            var result = await _paymentRepository.DeleteAsync(id);

            if (result)
            {
                // Update invoice status after deleting payment
                var invoice = await _invoiceRepository.GetByIdAsync(payment.InvoiceId);
                if (invoice != null)
                {
                    invoice.UpdateStatus();
                    await _invoiceRepository.UpdateAsync(invoice);
                }
            }

            return result;
        }

        public async Task<bool> PaymentExistsAsync(int id)
        {
            return await _paymentRepository.ExistsAsync(id);
        }

        public async Task<bool> ReceiptNumberExistsAsync(string receiptNumber, int? excludeId = null)
        {
            return await _paymentRepository.ExistsByReceiptNumberAsync(receiptNumber, excludeId);
        }

        public async Task<IEnumerable<Payment>> SearchPaymentsAsync(string searchTerm)
        {
            return await _paymentRepository.SearchAsync(searchTerm);
        }

        public async Task<IEnumerable<Payment>> GetFilteredPaymentsAsync(PaymentFilter filter)
        {
            return await _paymentRepository.GetFilteredAsync(filter);
        }

        public async Task<IEnumerable<Payment>> GetRecentPaymentsAsync(int count = 10)
        {
            return await _paymentRepository.GetRecentPaymentsAsync(count);
        }

        public async Task<bool> ValidatePaymentAsync(Payment payment)
        {
            if (string.IsNullOrWhiteSpace(payment.ReceiptNumber))
                return false;

            if (payment.Amount <= 0)
                return false;

            if (payment.InvoiceId <= 0)
                return false;

            if (payment.PaymentDate > DateTime.Now.AddDays(1)) // Allow future dates within 1 day
                return false;

            return await Task.FromResult(true);
        }

        public async Task<PaymentStatistics> GetPaymentStatisticsAsync()
        {
            var allPayments = await _paymentRepository.GetAllAsync();
            var paymentsList = allPayments.ToList();

            var totalPayments = paymentsList.Count;
            var totalAmount = paymentsList.Sum(p => p.Amount);
            var averageAmount = totalPayments > 0 ? totalAmount / totalPayments : 0;
            var lastPaymentDate = paymentsList.OrderByDescending(p => p.PaymentDate).FirstOrDefault()?.PaymentDate;

            var paymentMethodStats = paymentsList
                .GroupBy(p => p.Method)
                .Select(g => new PaymentMethodStatistics
                {
                    Method = g.Key,
                    MethodName = GetPaymentMethodName(g.Key),
                    Count = g.Count(),
                    TotalAmount = g.Sum(p => p.Amount),
                    Percentage = totalAmount > 0 ? (g.Sum(p => p.Amount) / totalAmount) * 100 : 0
                })
                .OrderByDescending(s => s.TotalAmount);

            var topSuppliers = paymentsList
                .GroupBy(p => p.Invoice.Supplier.Name)
                .Select(g => new SupplierPaymentStatistics
                {
                    SupplierName = g.Key,
                    PaymentCount = g.Count(),
                    TotalAmount = g.Sum(p => p.Amount)
                })
                .OrderByDescending(s => s.TotalAmount)
                .Take(5);

            return new PaymentStatistics
            {
                TotalPayments = totalPayments,
                TotalAmount = totalAmount,
                AveragePaymentAmount = averageAmount,
                LastPaymentDate = lastPaymentDate,
                PaymentMethodStats = paymentMethodStats,
                TopSuppliersByPayments = topSuppliers
            };
        }

        public async Task<IEnumerable<MonthlyPaymentStatistics>> GetMonthlyPaymentStatisticsAsync(int months = 12)
        {
            var allPayments = await _paymentRepository.GetAllAsync();
            var startDate = DateTime.Now.AddMonths(-months);

            var monthlyStats = allPayments
                .Where(p => p.PaymentDate >= startDate)
                .GroupBy(p => new { p.PaymentDate.Year, p.PaymentDate.Month })
                .Select(g => new MonthlyPaymentStatistics
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    MonthName = GetArabicMonthName(g.Key.Month),
                    PaymentCount = g.Count(),
                    TotalAmount = g.Sum(p => p.Amount)
                })
                .OrderBy(s => s.Year)
                .ThenBy(s => s.Month);

            return monthlyStats;
        }

        public async Task<DailyPaymentStatistics> GetTodayPaymentStatisticsAsync()
        {
            var today = DateTime.Today;
            var allPayments = await _paymentRepository.GetAllAsync();

            var todayPayments = allPayments
                .Where(p => p.PaymentDate.Date == today)
                .ToList();

            return new DailyPaymentStatistics
            {
                Date = today,
                PaymentCount = todayPayments.Count,
                TotalAmount = todayPayments.Sum(p => p.Amount)
            };
        }

        public async Task<MonthlyPaymentStatistics> GetCurrentMonthPaymentStatisticsAsync()
        {
            var currentDate = DateTime.Now;
            var firstDayOfMonth = new DateTime(currentDate.Year, currentDate.Month, 1);
            var allPayments = await _paymentRepository.GetAllAsync();

            var monthPayments = allPayments
                .Where(p => p.PaymentDate >= firstDayOfMonth && p.PaymentDate.Month == currentDate.Month && p.PaymentDate.Year == currentDate.Year)
                .ToList();

            return new MonthlyPaymentStatistics
            {
                Year = currentDate.Year,
                Month = currentDate.Month,
                MonthName = GetArabicMonthName(currentDate.Month),
                PaymentCount = monthPayments.Count,
                TotalAmount = monthPayments.Sum(p => p.Amount)
            };
        }

        private string GetPaymentMethodName(PaymentMethod method)
        {
            return method switch
            {
                PaymentMethod.Cash => "نقدي",
                PaymentMethod.Check => "شيك",
                PaymentMethod.BankTransfer => "تحويل بنكي",
                PaymentMethod.CreditCard => "بطاقة ائتمان",
                PaymentMethod.Other => "أخرى",
                _ => "غير محدد"
            };
        }

        private string GetArabicMonthName(int month)
        {
            return month switch
            {
                1 => "يناير",
                2 => "فبراير",
                3 => "مارس",
                4 => "أبريل",
                5 => "مايو",
                6 => "يونيو",
                7 => "يوليو",
                8 => "أغسطس",
                9 => "سبتمبر",
                10 => "أكتوبر",
                11 => "نوفمبر",
                12 => "ديسمبر",
                _ => "غير محدد"
            };
        }

        public async Task<bool> SaveMultiPaymentAsync(MultiPaymentModel multiPayment)
        {
            if (multiPayment == null || !multiPayment.SelectedInvoices.Any())
                throw new ArgumentException("بيانات الدفع المتعدد غير صحيحة");

            if (string.IsNullOrWhiteSpace(multiPayment.ReceiptNumber))
                throw new ArgumentException("رقم الوصل مطلوب");

            if (await _paymentRepository.ExistsByReceiptNumberAsync(multiPayment.ReceiptNumber))
                throw new ArgumentException("يوجد وصل بنفس الرقم مسبقاً");

            if (multiPayment.FinalAmount <= 0)
                throw new ArgumentException("المبلغ النهائي يجب أن يكون أكبر من صفر");

            try
            {
                // التحقق من صحة الفواتير المختارة
                foreach (var selectedInvoice in multiPayment.SelectedInvoices)
                {
                    var invoice = await _invoiceRepository.GetByIdAsync(selectedInvoice.InvoiceId);
                    if (invoice == null)
                        throw new ArgumentException($"الفاتورة رقم {selectedInvoice.InvoiceNumber} غير موجودة");

                    if (!invoice.CanAddPayment())
                        throw new ArgumentException($"الفاتورة رقم {selectedInvoice.InvoiceNumber} مسددة بالكامل");
                }

                // حساب المبلغ المخصص لكل فاتورة بناءً على النسبة
                var totalInvoicesAmount = multiPayment.SelectedInvoices.Sum(i => i.RemainingAmount);
                var paymentRatio = multiPayment.FinalAmount / totalInvoicesAmount;

                // إنشاء وصل دفع لكل فاتورة
                var createdPayments = new List<Payment>();

                for (int i = 0; i < multiPayment.SelectedInvoices.Count; i++)
                {
                    var selectedInvoice = multiPayment.SelectedInvoices[i];
                    var allocatedAmount = selectedInvoice.RemainingAmount * paymentRatio;

                    // التأكد من عدم تجاوز المبلغ المتبقي
                    allocatedAmount = Math.Min(allocatedAmount, selectedInvoice.RemainingAmount);

                    var payment = new Payment
                    {
                        ReceiptNumber = $"{multiPayment.ReceiptNumber}-{i + 1:D2}",
                        InvoiceId = selectedInvoice.InvoiceId,
                        Amount = Math.Round(allocatedAmount, 0), // تقريب للعدد الصحيح
                        PaymentDate = multiPayment.PaymentDate,
                        Method = ConvertPaymentMethod(multiPayment.PaymentMethod),
                        Notes = $"دفع متعدد - {multiPayment.Notes}",
                        AttachmentPath = await CopyAttachmentFile(multiPayment.AttachmentPath, multiPayment.ReceiptNumber)
                    };

                    var createdPayment = await _paymentRepository.AddAsync(payment);
                    createdPayments.Add(createdPayment);
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الدفع المتعدد: {ex.Message}");
            }
        }

        private PaymentMethod ConvertPaymentMethod(string paymentMethodText)
        {
            return paymentMethodText switch
            {
                "نقدي" => PaymentMethod.Cash,
                "بطاقة بنكية" => PaymentMethod.CreditCard,
                "شيك" => PaymentMethod.Check,
                "تحويل بنكي" => PaymentMethod.BankTransfer,
                _ => PaymentMethod.Cash
            };
        }

        private async Task<string?> CopyAttachmentFile(string? sourcePath, string receiptNumber)
        {
            if (string.IsNullOrWhiteSpace(sourcePath) || !File.Exists(sourcePath))
                return null;

            try
            {
                // إنشاء مجلد المرفقات إذا لم يكن موجوداً
                var attachmentsDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Attachments", "MultiPayments");
                Directory.CreateDirectory(attachmentsDir);

                // إنشاء اسم ملف فريد
                var fileExtension = Path.GetExtension(sourcePath);
                var fileName = $"{receiptNumber}_{DateTime.Now:yyyyMMdd_HHmmss}{fileExtension}";
                var destinationPath = Path.Combine(attachmentsDir, fileName);

                // نسخ الملف
                await Task.Run(() => File.Copy(sourcePath, destinationPath, true));

                return destinationPath;
            }
            catch (Exception ex)
            {
                // في حالة فشل نسخ الملف، نتجاهل الخطأ ونكمل العملية
                System.Diagnostics.Debug.WriteLine($"فشل في نسخ المرفق: {ex.Message}");
                return null;
            }
        }
    }
}
