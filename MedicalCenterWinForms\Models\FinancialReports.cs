using System;
using System.Collections.Generic;

namespace MedicalCenterWinForms.Models
{
    // Daily Financial Summary
    public class DailyFinancialSummary
    {
        public DateTime Date { get; set; }
        public int MainPaymentsCount { get; set; }
        public int ReferralPaymentsCount { get; set; }
        public int TotalPayments { get; set; }
        public decimal TotalMainPayments { get; set; }
        public decimal TotalReferralPayments { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalDoctorShares { get; set; }
        public decimal TotalCenterShares { get; set; }
        public List<MainPayment> MainPaymentDetails { get; set; } = new();
        public List<ReferralPayment> ReferralPaymentDetails { get; set; } = new();
    }

    // Monthly Financial Summary
    public class MonthlyFinancialSummary
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName => new DateTime(Year, Month, 1).ToString("MMMM yyyy");
        public List<DailyFinancialSummary> DailySummaries { get; set; } = new();
        public decimal TotalRevenue { get; set; }
        public decimal TotalMainPayments { get; set; }
        public decimal TotalReferralPayments { get; set; }
        public decimal TotalDoctorShares { get; set; }
        public decimal TotalCenterShares { get; set; }
        public int TotalPaymentsCount { get; set; }
        public int WorkingDays { get; set; }
        public decimal AverageDailyRevenue { get; set; }
    }

    // Doctor Performance Report
    public class DoctorPerformanceReport
    {
        public Doctor Doctor { get; set; } = null!;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int MainPaymentsCount { get; set; }
        public int ReferralPaymentsCount { get; set; }
        public int TotalPatients { get; set; }
        public decimal TotalMainPayments { get; set; }
        public decimal TotalReferralPayments { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalDoctorShares { get; set; }
        public decimal AverageRevenuePerPatient { get; set; }
        public List<MainPayment> MainPaymentDetails { get; set; } = new();
        public List<ReferralPayment> ReferralPaymentDetails { get; set; } = new();
    }

    // Service Performance Report
    public class ServicePerformanceReport
    {
        public MedicalService Service { get; set; } = null!;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalPayments { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalDoctorShares { get; set; }
        public decimal TotalCenterShares { get; set; }
        public decimal AverageRevenuePerPayment { get; set; }
        public List<ReferralPayment> PaymentDetails { get; set; } = new();
    }

    // Financial Trends Report
    public class FinancialTrendsReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<MonthlyFinancialSummary> MonthlyData { get; set; } = new();
        public decimal TotalRevenue { get; set; }
        public decimal TotalDoctorShares { get; set; }
        public decimal TotalCenterShares { get; set; }
        public int TotalPayments { get; set; }
        public decimal AverageMonthlyRevenue { get; set; }
        public decimal RevenueGrowthRate { get; set; }
        public decimal PaymentGrowthRate { get; set; }
    }

    // Payment Type Analysis
    public class PaymentTypeAnalysis
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int MainPaymentsCount { get; set; }
        public decimal MainPaymentsRevenue { get; set; }
        public int ReferralPaymentsCount { get; set; }
        public decimal ReferralPaymentsRevenue { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal MainPaymentPercentage { get; set; }
        public decimal ReferralPaymentPercentage { get; set; }
    }

    // Doctor Revenue Summary
    public class DoctorRevenueSummary
    {
        public Doctor Doctor { get; set; } = null!;
        public decimal TotalRevenue { get; set; }
        public decimal TotalShares { get; set; }
        public int TotalPatients { get; set; }
        public decimal SharePercentage { get; set; }
    }

    // Service Revenue Summary
    public class ServiceRevenueSummary
    {
        public MedicalService Service { get; set; } = null!;
        public decimal TotalRevenue { get; set; }
        public int TotalUsage { get; set; }
        public decimal AveragePrice { get; set; }
        public decimal RevenuePercentage { get; set; }
    }

    // Financial Dashboard Data
    public class FinancialDashboardData
    {
        public DateTime Date { get; set; }
        public decimal TodayRevenue { get; set; }
        public decimal WeekRevenue { get; set; }
        public decimal MonthRevenue { get; set; }
        public decimal YearRevenue { get; set; }
        public int TodayPayments { get; set; }
        public int WeekPayments { get; set; }
        public int MonthPayments { get; set; }
        public int YearPayments { get; set; }
        public decimal TodayDoctorShares { get; set; }
        public decimal WeekDoctorShares { get; set; }
        public decimal MonthDoctorShares { get; set; }
        public decimal YearDoctorShares { get; set; }
        public List<DoctorRevenueSummary> TopDoctors { get; set; } = new();
        public List<ServiceRevenueSummary> TopServices { get; set; } = new();
    }

    // Comparative Report
    public class ComparativeFinancialReport
    {
        public DateTime CurrentPeriodStart { get; set; }
        public DateTime CurrentPeriodEnd { get; set; }
        public DateTime PreviousPeriodStart { get; set; }
        public DateTime PreviousPeriodEnd { get; set; }
        
        public decimal CurrentPeriodRevenue { get; set; }
        public decimal PreviousPeriodRevenue { get; set; }
        public decimal RevenueChange { get; set; }
        public decimal RevenueChangePercentage { get; set; }
        
        public int CurrentPeriodPayments { get; set; }
        public int PreviousPeriodPayments { get; set; }
        public int PaymentChange { get; set; }
        public decimal PaymentChangePercentage { get; set; }
        
        public decimal CurrentPeriodDoctorShares { get; set; }
        public decimal PreviousPeriodDoctorShares { get; set; }
        public decimal DoctorSharesChange { get; set; }
        public decimal DoctorSharesChangePercentage { get; set; }
    }

    // Revenue Breakdown
    public class RevenueBreakdown
    {
        public DateTime Date { get; set; }
        public decimal ConsultationRevenue { get; set; }
        public decimal ExamRevenue { get; set; }
        public decimal ReferralRevenue { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal ConsultationPercentage { get; set; }
        public decimal ExamPercentage { get; set; }
        public decimal ReferralPercentage { get; set; }
    }

    // Payment Method Analysis
    public class PaymentMethodAnalysis
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal CashPayments { get; set; }
        public decimal CardPayments { get; set; }
        public decimal BankTransfers { get; set; }
        public decimal CheckPayments { get; set; }
        public decimal TotalPayments { get; set; }
        public decimal CashPercentage { get; set; }
        public decimal CardPercentage { get; set; }
        public decimal BankTransferPercentage { get; set; }
        public decimal CheckPercentage { get; set; }
    }

    // Hourly Revenue Analysis
    public class HourlyRevenueAnalysis
    {
        public DateTime Date { get; set; }
        public Dictionary<int, decimal> HourlyRevenue { get; set; } = new();
        public Dictionary<int, int> HourlyPayments { get; set; } = new();
        public int PeakHour { get; set; }
        public decimal PeakHourRevenue { get; set; }
        public decimal AverageHourlyRevenue { get; set; }
    }
}
