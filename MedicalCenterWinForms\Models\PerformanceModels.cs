using System;
using System.Collections.Generic;

namespace MedicalCenterWinForms.Models
{
    // Performance Metrics
    public class PerformanceMetrics
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalDoctorShares { get; set; }
        public decimal TotalCenterShares { get; set; }
        public int TotalPayments { get; set; }
        public int UniquePatients { get; set; }
        public int ActiveDoctors { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public decimal RevenuePerPatient { get; set; }
        public decimal RevenuePerDoctor { get; set; }
        public decimal DoctorSharePercentage { get; set; }
        public decimal CenterSharePercentage { get; set; }
    }

    // Daily Performance
    public class DailyPerformance
    {
        public DateTime Date { get; set; }
        public decimal Revenue { get; set; }
        public int Payments { get; set; }
        public int Patients { get; set; }
        public decimal DoctorShares { get; set; }
        public decimal CenterShares { get; set; }
        public decimal AverageTransaction { get; set; }
    }

    // Doctor Performance Comparison
    public class DoctorPerformanceComparison
    {
        public Doctor Doctor { get; set; } = null!;
        public decimal TotalRevenue { get; set; }
        public decimal TotalShares { get; set; }
        public int TotalPatients { get; set; }
        public int MainPayments { get; set; }
        public int ReferralPayments { get; set; }
        public decimal AverageRevenuePerPatient { get; set; }
        public decimal SharePercentage { get; set; }
    }

    // Service Performance Analysis
    public class ServicePerformanceAnalysis
    {
        public MedicalService Service { get; set; } = null!;
        public decimal TotalRevenue { get; set; }
        public int TotalUsage { get; set; }
        public int UniquePatients { get; set; }
        public int UniqueDoctors { get; set; }
        public decimal AveragePrice { get; set; }
        public decimal RevenuePerPatient { get; set; }
        public decimal UsagePerDoctor { get; set; }
    }

    // Revenue Forecast
    public class RevenueForecast
    {
        public DateTime ForecastDate { get; set; }
        public int DaysToForecast { get; set; }
        public decimal PredictedRevenue { get; set; }
        public decimal ConfidenceLevel { get; set; }
        public string TrendDirection { get; set; } = string.Empty;
        public decimal HistoricalAverage { get; set; }
        public decimal TrendValue { get; set; }
    }

    // Performance Alert
    public class PerformanceAlert
    {
        public string Type { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public decimal Value { get; set; }
        public decimal PreviousValue { get; set; }
    }

    // KPI (Key Performance Indicator)
    public class KPI
    {
        public string Name { get; set; } = string.Empty;
        public decimal CurrentValue { get; set; }
        public decimal TargetValue { get; set; }
        public decimal PreviousValue { get; set; }
        public string Unit { get; set; } = string.Empty;
        public string TrendDirection { get; set; } = string.Empty;
        public decimal PercentageChange { get; set; }
        public decimal TargetAchievement { get; set; }
        public string Status { get; set; } = string.Empty; // "Good", "Warning", "Critical"
    }

    // Performance Dashboard Data
    public class PerformanceDashboardData
    {
        public DateTime Date { get; set; }
        public List<KPI> KPIs { get; set; } = new();
        public List<PerformanceAlert> Alerts { get; set; } = new();
        public PerformanceMetrics TodayMetrics { get; set; } = null!;
        public PerformanceMetrics WeekMetrics { get; set; } = null!;
        public PerformanceMetrics MonthMetrics { get; set; } = null!;
        public RevenueForecast Forecast { get; set; } = null!;
        public List<DailyPerformance> RecentPerformance { get; set; } = new();
        public List<DoctorPerformanceComparison> TopDoctors { get; set; } = new();
        public List<ServicePerformanceAnalysis> TopServices { get; set; } = new();
    }

    // Benchmark Comparison
    public class BenchmarkComparison
    {
        public string MetricName { get; set; } = string.Empty;
        public decimal CurrentValue { get; set; }
        public decimal BenchmarkValue { get; set; }
        public decimal Variance { get; set; }
        public decimal VariancePercentage { get; set; }
        public string PerformanceLevel { get; set; } = string.Empty; // "Above", "At", "Below"
        public string Recommendation { get; set; } = string.Empty;
    }

    // Financial Health Score
    public class FinancialHealthScore
    {
        public DateTime CalculationDate { get; set; }
        public decimal OverallScore { get; set; } // 0-100
        public decimal RevenueScore { get; set; }
        public decimal GrowthScore { get; set; }
        public decimal EfficiencyScore { get; set; }
        public decimal StabilityScore { get; set; }
        public string HealthLevel { get; set; } = string.Empty; // "Excellent", "Good", "Fair", "Poor"
        public List<string> StrengthAreas { get; set; } = new();
        public List<string> ImprovementAreas { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    // Profitability Analysis
    public class ProfitabilityAnalysis
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public decimal OperatingExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal NetProfitMargin { get; set; }
        public decimal ROI { get; set; } // Return on Investment
        public List<CostBreakdown> CostBreakdowns { get; set; } = new();
        public List<RevenueStream> RevenueStreams { get; set; } = new();
    }

    // Cost Breakdown
    public class CostBreakdown
    {
        public string Category { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    // Revenue Stream
    public class RevenueStream
    {
        public string Source { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageValue { get; set; }
    }

    // Efficiency Metrics
    public class EfficiencyMetrics
    {
        public DateTime Date { get; set; }
        public decimal RevenuePerHour { get; set; }
        public decimal RevenuePerEmployee { get; set; }
        public decimal PatientThroughput { get; set; }
        public decimal AverageServiceTime { get; set; }
        public decimal ResourceUtilization { get; set; }
        public decimal CostPerPatient { get; set; }
        public decimal RevenuePerSquareMeter { get; set; }
    }

    // Growth Analysis
    public class GrowthAnalysis
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal RevenueGrowthRate { get; set; }
        public decimal PatientGrowthRate { get; set; }
        public decimal ServiceGrowthRate { get; set; }
        public decimal DoctorProductivityGrowth { get; set; }
        public decimal MarketShareGrowth { get; set; }
        public List<GrowthTrend> MonthlyTrends { get; set; } = new();
        public List<GrowthDriver> GrowthDrivers { get; set; } = new();
    }

    // Growth Trend
    public class GrowthTrend
    {
        public DateTime Month { get; set; }
        public decimal Revenue { get; set; }
        public decimal GrowthRate { get; set; }
        public int PatientCount { get; set; }
        public decimal AverageTransactionValue { get; set; }
    }

    // Growth Driver
    public class GrowthDriver
    {
        public string Factor { get; set; } = string.Empty;
        public decimal Impact { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
    }

    // Risk Assessment
    public class RiskAssessment
    {
        public DateTime AssessmentDate { get; set; }
        public decimal OverallRiskScore { get; set; } // 0-100
        public List<RiskFactor> RiskFactors { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public string RiskLevel { get; set; } = string.Empty; // "Low", "Medium", "High", "Critical"
    }

    // Risk Factor
    public class RiskFactor
    {
        public string Name { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public string Level { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Mitigation { get; set; } = string.Empty;
        public decimal Impact { get; set; }
        public decimal Probability { get; set; }
    }
}
