using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Models;
using System;
using System.IO;
using System.Threading.Tasks;

namespace HR_InvoiceArchiver.Tests.Services
{
    public class SettingsServiceTests : IDisposable
    {
        private readonly SettingsService _settingsService;
        private readonly string _testDirectory;

        public SettingsServiceTests()
        {
            _testDirectory = Path.Combine(Path.GetTempPath(), "HR_InvoiceArchiver_Settings_Tests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);
            
            // تعيين مجلد البيانات للاختبار
            Environment.SetEnvironmentVariable("APPDATA", _testDirectory);
            
            _settingsService = new SettingsService();
        }

        [Fact]
        public async Task LoadSettingsAsync_FirstTime_ShouldReturnDefaultSettings()
        {
            // Act
            var settings = await _settingsService.LoadSettingsAsync();

            // Assert
            settings.Should().NotBeNull();
            settings.ApplicationName.Should().Be("نظام أرشفة فواتير الموارد البشرية");
            settings.EnableNotifications.Should().BeTrue();
            settings.BackupIntervalHours.Should().Be(24);
        }

        [Fact]
        public async Task SaveSettingsAsync_WithValidSettings_ShouldSaveSuccessfully()
        {
            // Arrange
            var settings = new SettingsModel
            {
                ApplicationName = "تطبيق اختبار",
                CompanyName = "شركة اختبار",
                EnableNotifications = false,
                BackupIntervalHours = 12
            };

            // Act
            await _settingsService.SaveSettingsAsync(settings);

            // Assert
            var loadedSettings = await _settingsService.LoadSettingsAsync();
            loadedSettings.ApplicationName.Should().Be("تطبيق اختبار");
            loadedSettings.CompanyName.Should().Be("شركة اختبار");
            loadedSettings.EnableNotifications.Should().BeFalse();
            loadedSettings.BackupIntervalHours.Should().Be(12);
        }

        [Fact]
        public async Task ValidateSettingsAsync_WithValidSettings_ShouldReturnValid()
        {
            // Arrange
            var settings = new SettingsModel();
            settings.ResetToDefaults();

            // Act
            var result = await _settingsService.ValidateSettingsAsync(settings);

            // Assert
            result.IsValid.Should().BeTrue();
            result.Errors.Should().BeEmpty();
        }

        [Fact]
        public async Task ValidateSettingsAsync_WithInvalidSettings_ShouldReturnErrors()
        {
            // Arrange
            var settings = new SettingsModel
            {
                ApplicationName = "", // اسم فارغ
                BackupIntervalHours = 0, // قيمة غير صحيحة
                MaxBackupFiles = 0, // قيمة غير صحيحة
                CompanyEmail = "invalid-email" // بريد إلكتروني غير صحيح
            };

            // Act
            var result = await _settingsService.ValidateSettingsAsync(settings);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().NotBeEmpty();
        }

        [Fact]
        public async Task ResetToDefaultsAsync_ShouldResetAllSettings()
        {
            // Arrange
            var customSettings = new SettingsModel
            {
                ApplicationName = "تطبيق مخصص",
                CompanyName = "شركة مخصصة",
                EnableNotifications = false
            };
            await _settingsService.SaveSettingsAsync(customSettings);

            // Act
            await _settingsService.ResetToDefaultsAsync();

            // Assert
            var settings = await _settingsService.LoadSettingsAsync();
            settings.ApplicationName.Should().Be("نظام أرشفة فواتير الموارد البشرية");
            settings.CompanyName.Should().Be("");
            settings.EnableNotifications.Should().BeTrue();
        }

        [Fact]
        public async Task ExportSettingsAsync_ShouldCreateExportFile()
        {
            // Arrange
            var settings = new SettingsModel
            {
                ApplicationName = "تطبيق للتصدير",
                CompanyName = "شركة للتصدير"
            };
            await _settingsService.SaveSettingsAsync(settings);

            // Act
            var exportPath = await _settingsService.ExportSettingsAsync();

            // Assert
            exportPath.Should().NotBeNullOrEmpty();
            File.Exists(exportPath).Should().BeTrue();
            
            var exportedContent = await File.ReadAllTextAsync(exportPath);
            exportedContent.Should().Contain("تطبيق للتصدير");
            exportedContent.Should().Contain("شركة للتصدير");
        }

        [Fact]
        public async Task ImportSettingsAsync_WithValidFile_ShouldImportSuccessfully()
        {
            // Arrange
            var originalSettings = new SettingsModel
            {
                ApplicationName = "تطبيق أصلي",
                CompanyName = "شركة أصلية"
            };
            await _settingsService.SaveSettingsAsync(originalSettings);
            
            var exportPath = await _settingsService.ExportSettingsAsync();

            // تغيير الإعدادات
            var changedSettings = new SettingsModel
            {
                ApplicationName = "تطبيق مختلف",
                CompanyName = "شركة مختلفة"
            };
            await _settingsService.SaveSettingsAsync(changedSettings);

            // Act
            var success = await _settingsService.ImportSettingsAsync(exportPath);

            // Assert
            success.Should().BeTrue();
            
            var importedSettings = await _settingsService.LoadSettingsAsync();
            importedSettings.ApplicationName.Should().Be("تطبيق أصلي");
            importedSettings.CompanyName.Should().Be("شركة أصلية");
        }

        [Fact]
        public async Task ImportSettingsAsync_WithNonExistentFile_ShouldReturnFalse()
        {
            // Arrange
            var nonExistentPath = Path.Combine(_testDirectory, "non-existent.json");

            // Act
            var success = await _settingsService.ImportSettingsAsync(nonExistentPath);

            // Assert
            success.Should().BeFalse();
        }

        [Fact]
        public async Task GetSettingAsync_WithExistingSetting_ShouldReturnValue()
        {
            // Arrange
            var settings = new SettingsModel { ApplicationName = "تطبيق اختبار" };
            await _settingsService.SaveSettingsAsync(settings);

            // Act
            var value = await _settingsService.GetSettingAsync<string>("ApplicationName");

            // Assert
            value.Should().Be("تطبيق اختبار");
        }

        [Fact]
        public async Task GetSettingAsync_WithNonExistentSetting_ShouldReturnDefault()
        {
            // Act
            var value = await _settingsService.GetSettingAsync<string>("NonExistentSetting", "قيمة افتراضية");

            // Assert
            value.Should().Be("قيمة افتراضية");
        }

        [Fact]
        public async Task SetSettingAsync_ShouldUpdateSetting()
        {
            // Arrange
            var settings = new SettingsModel();
            await _settingsService.SaveSettingsAsync(settings);

            // Act
            await _settingsService.SetSettingAsync("ApplicationName", "اسم جديد");

            // Assert
            var updatedSettings = await _settingsService.LoadSettingsAsync();
            updatedSettings.ApplicationName.Should().Be("اسم جديد");
        }

        [Fact]
        public async Task SettingExistsAsync_WithExistingSetting_ShouldReturnTrue()
        {
            // Act
            var exists = await _settingsService.SettingExistsAsync("ApplicationName");

            // Assert
            exists.Should().BeTrue();
        }

        [Fact]
        public async Task SettingExistsAsync_WithNonExistentSetting_ShouldReturnFalse()
        {
            // Act
            var exists = await _settingsService.SettingExistsAsync("NonExistentSetting");

            // Assert
            exists.Should().BeFalse();
        }

        [Fact]
        public async Task CreateBackupAsync_ShouldCreateBackupFile()
        {
            // Arrange
            var settings = new SettingsModel { ApplicationName = "تطبيق للنسخ الاحتياطي" };
            await _settingsService.SaveSettingsAsync(settings);

            // Act
            var backupPath = await _settingsService.CreateBackupAsync();

            // Assert
            backupPath.Should().NotBeNullOrEmpty();
            File.Exists(backupPath).Should().BeTrue();
            
            var backupContent = await File.ReadAllTextAsync(backupPath);
            backupContent.Should().Contain("تطبيق للنسخ الاحتياطي");
        }

        [Fact]
        public async Task RestoreFromBackupAsync_WithValidBackup_ShouldRestoreSuccessfully()
        {
            // Arrange
            var originalSettings = new SettingsModel { ApplicationName = "تطبيق أصلي" };
            await _settingsService.SaveSettingsAsync(originalSettings);
            
            var backupPath = await _settingsService.CreateBackupAsync();

            // تغيير الإعدادات
            var changedSettings = new SettingsModel { ApplicationName = "تطبيق مختلف" };
            await _settingsService.SaveSettingsAsync(changedSettings);

            // Act
            var success = await _settingsService.RestoreFromBackupAsync(backupPath);

            // Assert
            success.Should().BeTrue();
            
            var restoredSettings = await _settingsService.LoadSettingsAsync();
            restoredSettings.ApplicationName.Should().Be("تطبيق أصلي");
        }

        [Fact]
        public async Task GetSystemInfoAsync_ShouldReturnSystemInfo()
        {
            // Act
            var systemInfo = await _settingsService.GetSystemInfoAsync();

            // Assert
            systemInfo.Should().NotBeNull();
            systemInfo.ApplicationVersion.Should().NotBeNullOrEmpty();
            systemInfo.OperatingSystem.Should().NotBeNullOrEmpty();
            systemInfo.DotNetVersion.Should().NotBeNullOrEmpty();
            systemInfo.MemoryUsage.Should().BeGreaterThan(0);
        }

        public void Dispose()
        {
            // تنظيف مجلد الاختبار
            if (Directory.Exists(_testDirectory))
            {
                try
                {
                    Directory.Delete(_testDirectory, true);
                }
                catch
                {
                    // تجاهل أخطاء التنظيف
                }
            }
        }
    }
}
