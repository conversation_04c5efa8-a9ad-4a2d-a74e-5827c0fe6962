using System.ComponentModel.DataAnnotations;

namespace MedicalCenterSystem.Models
{
    public class UserAccount
    {
        public int UserAccountId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        [Display(Name = "كلمة المرور")]
        public string HashedPassword { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        [Display(Name = "الدور")]
        public string Role { get; set; } = string.Empty; // Admin / Reception / Cashier

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "آخر تسجيل دخول")]
        public DateTime? LastLoginDate { get; set; }
    }
}
