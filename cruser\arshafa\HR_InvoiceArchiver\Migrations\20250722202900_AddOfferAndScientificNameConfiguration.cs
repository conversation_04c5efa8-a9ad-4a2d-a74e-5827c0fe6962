﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HR_InvoiceArchiver.Migrations
{
    /// <inheritdoc />
    public partial class AddOfferAndScientificNameConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "Price",
                table: "Offers",
                type: "decimal(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "TEXT");

            migrationBuilder.CreateIndex(
                name: "IX_ScientificNames_Name",
                table: "ScientificNames",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Offers_CreatedAt",
                table: "Offers",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Offers_RepresentativeName",
                table: "Offers",
                column: "RepresentativeName");

            migrationBuilder.CreateIndex(
                name: "IX_Offers_ScientificName",
                table: "Offers",
                column: "ScientificName");

            migrationBuilder.CreateIndex(
                name: "IX_Offers_TradeName",
                table: "Offers",
                column: "TradeName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ScientificNames_Name",
                table: "ScientificNames");

            migrationBuilder.DropIndex(
                name: "IX_Offers_CreatedAt",
                table: "Offers");

            migrationBuilder.DropIndex(
                name: "IX_Offers_RepresentativeName",
                table: "Offers");

            migrationBuilder.DropIndex(
                name: "IX_Offers_ScientificName",
                table: "Offers");

            migrationBuilder.DropIndex(
                name: "IX_Offers_TradeName",
                table: "Offers");

            migrationBuilder.AlterColumn<decimal>(
                name: "Price",
                table: "Offers",
                type: "TEXT",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");
        }
    }
}
