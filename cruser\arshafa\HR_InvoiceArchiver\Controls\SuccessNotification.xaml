<UserControl x:Class="HR_InvoiceArchiver.Controls.SuccessNotification"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">

    <UserControl.Resources>
        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="SuccessGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#00C851" Offset="0"/>
            <GradientStop Color="#007E33" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="IconGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FFFFFF" Offset="0"/>
            <GradientStop Color="#F8F9FA" Offset="1"/>
        </LinearGradientBrush>

        <!-- Enhanced Slide Down Animation with Bounce -->
        <Storyboard x:Key="SlideDownAnimation">
            <DoubleAnimation Storyboard.TargetName="NotificationCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                           From="-150" To="0" Duration="0:0:0.8">
                <DoubleAnimation.EasingFunction>
                    <ElasticEase EasingMode="EaseOut" Oscillations="2" Springiness="8"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="NotificationCard"
                           Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.4"/>
            <DoubleAnimation Storyboard.TargetName="NotificationCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                           From="0.3" To="1" Duration="0:0:0.6">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.5"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="NotificationCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                           From="0.3" To="1" Duration="0:0:0.6">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.5"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!-- Enhanced Slide Up Animation -->
        <Storyboard x:Key="SlideUpAnimation" Completed="SlideUpAnimation_Completed">
            <DoubleAnimation Storyboard.TargetName="NotificationCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.Y)"
                           From="0" To="-150" Duration="0:0:0.5">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseIn" Amplitude="0.4"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="NotificationCard"
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.4"/>
            <DoubleAnimation Storyboard.TargetName="NotificationCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                           From="1" To="0.8" Duration="0:0:0.4"/>
            <DoubleAnimation Storyboard.TargetName="NotificationCard"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                           From="1" To="0.8" Duration="0:0:0.4"/>
        </Storyboard>

        <!-- Enhanced Icon Animation with Rotation -->
        <Storyboard x:Key="IconAnimation" RepeatBehavior="1x">
            <DoubleAnimation Storyboard.TargetName="CheckIcon"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                           From="0" To="1" Duration="0:0:0.4" BeginTime="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.6"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="CheckIcon"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                           From="0" To="1" Duration="0:0:0.4" BeginTime="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.6"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="CheckIcon"
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(RotateTransform.Angle)"
                           From="-180" To="0" Duration="0:0:0.5" BeginTime="0:0:0.2">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!-- Progress Bar Animation -->
        <Storyboard x:Key="ProgressAnimation">
            <DoubleAnimation Storyboard.TargetName="ProgressBar"
                           Storyboard.TargetProperty="Width"
                           From="0" To="300" Duration="0:0:4" BeginTime="0:0:0.5"/>
        </Storyboard>

        <!-- Enhanced Effects -->
        <DropShadowEffect x:Key="ModernShadow" Color="#000000" BlurRadius="25" ShadowDepth="8" Opacity="0.3"/>
        <DropShadowEffect x:Key="GlowEffect" Color="#00C851" BlurRadius="30" ShadowDepth="0" Opacity="0.8"/>
        <BlurEffect x:Key="BackgroundBlur" Radius="2"/>
    </UserControl.Resources>

    <!-- Modern Notification Container -->
    <Grid HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0,30,0,0">

        <!-- Background Blur Effect -->
        <Rectangle Fill="#01000000" Width="400" Height="120" RadiusX="20" RadiusY="20"
                  Effect="{StaticResource BackgroundBlur}" Opacity="0.1"/>

        <!-- Main Notification Card -->
        <Border x:Name="NotificationCard"
                Background="{StaticResource SuccessGradient}"
                CornerRadius="16"
                Padding="24,20"
                MinWidth="350"
                MaxWidth="450"
                Effect="{StaticResource ModernShadow}">

            <Border.RenderTransform>
                <TransformGroup>
                    <TranslateTransform/>
                    <ScaleTransform/>
                </TransformGroup>
            </Border.RenderTransform>

            <!-- Inner Content -->
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header Row -->
                <Grid Grid.Row="0" Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Enhanced Success Icon -->
                    <Border Grid.Column="0"
                           Background="{StaticResource IconGradient}"
                           CornerRadius="25"
                           Width="50" Height="50"
                           Margin="0,0,16,0"
                           Effect="{StaticResource GlowEffect}">

                        <materialDesign:PackIcon x:Name="CheckIcon"
                                               Kind="CheckCircle"
                                               Width="30" Height="30"
                                               Foreground="#00C851"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center">
                            <materialDesign:PackIcon.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform/>
                                    <RotateTransform/>
                                </TransformGroup>
                            </materialDesign:PackIcon.RenderTransform>
                        </materialDesign:PackIcon>
                    </Border>

                    <!-- Enhanced Message Content -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock x:Name="TitleTextBlock"
                                  Text="✨ نجحت العملية بامتياز!"
                                  FontSize="18"
                                  FontWeight="Bold"
                                  Foreground="White"
                                  Margin="0,0,0,6"
                                  TextWrapping="Wrap"/>

                        <TextBlock x:Name="MessageTextBlock"
                                  Text="تم حفظ الفاتورة بنجاح وإضافتها إلى النظام"
                                  FontSize="14"
                                  Foreground="White"
                                  Opacity="0.95"
                                  TextWrapping="Wrap"/>
                    </StackPanel>

                    <!-- Modern Close Button -->
                    <Button Grid.Column="2"
                           Click="CloseButton_Click"
                           Width="36" Height="36"
                           Margin="12,0,0,0"
                           Background="Transparent"
                           BorderBrush="White"
                           BorderThickness="1"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Foreground="White"
                           Opacity="0.9">
                        <materialDesign:PackIcon Kind="Close" Width="18" Height="18"/>
                    </Button>
                </Grid>

                <!-- Progress Indicator -->
                <Grid Grid.Row="1" Margin="0,8,0,0">
                    <Border Background="White" Opacity="0.3" Height="3" CornerRadius="2"/>
                    <Border x:Name="ProgressBar" Background="White" Height="3" CornerRadius="2"
                           HorizontalAlignment="Left" Width="0"/>
                </Grid>

                <!-- Action Buttons Row -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left"
                           Margin="0,12,0,0">
                    <Button Content="عرض الفاتورة"
                           Background="White"
                           Foreground="#00C851"
                           FontWeight="SemiBold"
                           Padding="16,8"
                           Margin="0,0,12,0"
                           BorderThickness="0"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Click="ViewInvoiceButton_Click"/>

                    <Button Content="إضافة أخرى"
                           Background="Transparent"
                           Foreground="White"
                           BorderBrush="White"
                           BorderThickness="1"
                           FontWeight="SemiBold"
                           Padding="16,8"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Click="AddAnotherButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
