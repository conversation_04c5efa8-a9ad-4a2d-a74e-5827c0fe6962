using System;
using System.Collections.Generic;

namespace MedicalCenterWinForms.Models
{
    // Backup Information
    public class BackupInfo
    {
        public string BackupId { get; set; } = string.Empty;
        public DateTime BackupDate { get; set; }
        public BackupType BackupType { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string FilePath { get; set; } = string.Empty;
        public string CreatedBy { get; set; } = "System";
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    // Backup Types
    public enum BackupType
    {
        Full,
        Incremental,
        Differential,
        Settings,
        Database
    }

    // Backup Result
    public class BackupResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public BackupInfo? BackupInfo { get; set; }
        public Exception? Exception { get; set; }
        public TimeSpan Duration { get; set; }
    }

    // Restore Result
    public class RestoreResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public BackupInfo? BackupInfo { get; set; }
        public BackupInfo? PreRestoreBackup { get; set; }
        public bool RequiresConfirmation { get; set; }
        public Exception? Exception { get; set; }
        public TimeSpan Duration { get; set; }
    }

    // Backup Verification Result
    public class BackupVerificationResult
    {
        public bool IsValid { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> Issues { get; set; } = new();
        public BackupInfo? BackupInfo { get; set; }
    }

    // Cleanup Result
    public class CleanupResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int DeletedCount { get; set; }
        public long FreedSpace { get; set; }
        public List<string> DeletedFiles { get; set; } = new();
    }

    // Database Backup Data Structure
    public class DatabaseBackupData
    {
        public List<Doctor>? Doctors { get; set; }
        public List<MedicalService>? MedicalServices { get; set; }
        public List<DoctorService>? DoctorServices { get; set; }
        public List<PatientVisit>? PatientVisits { get; set; }
        public List<MainPayment>? MainPayments { get; set; }
        public List<ReferralPayment>? ReferralPayments { get; set; }
        public List<UserAccount>? UserAccounts { get; set; }
        public List<SystemSetting>? SystemSettings { get; set; }
    }

    // Backup Schedule
    public class BackupSchedule
    {
        public int ScheduleId { get; set; }
        public string Name { get; set; } = string.Empty;
        public BackupType BackupType { get; set; }
        public ScheduleFrequency Frequency { get; set; }
        public TimeSpan ScheduledTime { get; set; }
        public string BackupPath { get; set; } = string.Empty;
        public int RetentionDays { get; set; } = 30;
        public bool IsEnabled { get; set; } = true;
        public DateTime? LastRun { get; set; }
        public DateTime? NextRun { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    // Schedule Frequency
    public enum ScheduleFrequency
    {
        Daily,
        Weekly,
        Monthly,
        Custom
    }

    // Backup Job
    public class BackupJob
    {
        public string JobId { get; set; } = string.Empty;
        public BackupSchedule Schedule { get; set; } = null!;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public BackupJobStatus Status { get; set; }
        public string? ErrorMessage { get; set; }
        public BackupResult? Result { get; set; }
        public int Progress { get; set; }
        public string CurrentStep { get; set; } = string.Empty;
    }

    // Backup Job Status
    public enum BackupJobStatus
    {
        Pending,
        Running,
        Completed,
        Failed,
        Cancelled
    }

    // Backup Settings
    public class BackupSettings
    {
        public string DefaultBackupPath { get; set; } = string.Empty;
        public int DefaultRetentionDays { get; set; } = 30;
        public bool AutoBackupEnabled { get; set; } = true;
        public ScheduleFrequency AutoBackupFrequency { get; set; } = ScheduleFrequency.Daily;
        public TimeSpan AutoBackupTime { get; set; } = new TimeSpan(2, 0, 0); // 2:00 AM
        public bool CompressBackups { get; set; } = true;
        public bool EncryptBackups { get; set; } = false;
        public string? EncryptionKey { get; set; }
        public bool NotifyOnSuccess { get; set; } = false;
        public bool NotifyOnFailure { get; set; } = true;
        public string? NotificationEmail { get; set; }
        public long MaxBackupSize { get; set; } = 1024 * 1024 * 1024; // 1 GB
        public int MaxBackupCount { get; set; } = 10;
    }

    // Backup History Entry
    public class BackupHistoryEntry
    {
        public int Id { get; set; }
        public string BackupId { get; set; } = string.Empty;
        public DateTime BackupDate { get; set; }
        public BackupType BackupType { get; set; }
        public string Description { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string FilePath { get; set; } = string.Empty;
        public bool IsSuccessful { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public bool IsDeleted { get; set; }
        public DateTime? DeletedDate { get; set; }
    }

    // Restore Point
    public class RestorePoint
    {
        public string RestorePointId { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public string Description { get; set; } = string.Empty;
        public string BackupFilePath { get; set; } = string.Empty;
        public BackupInfo BackupInfo { get; set; } = null!;
        public bool IsAutomatic { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public Dictionary<string, object> SystemState { get; set; } = new();
    }

    // Backup Progress Info
    public class BackupProgressInfo
    {
        public string JobId { get; set; } = string.Empty;
        public int OverallProgress { get; set; }
        public string CurrentStep { get; set; } = string.Empty;
        public int StepProgress { get; set; }
        public string StatusMessage { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan? EstimatedTimeRemaining { get; set; }
        public long ProcessedBytes { get; set; }
        public long TotalBytes { get; set; }
        public int ProcessedItems { get; set; }
        public int TotalItems { get; set; }
    }

    // Backup Validation Rule
    public class BackupValidationRule
    {
        public string RuleName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsEnabled { get; set; } = true;
        public ValidationSeverity Severity { get; set; }
        public Func<BackupInfo, Task<ValidationResult>>? ValidationFunction { get; set; }
    }

    // Validation Severity
    public enum ValidationSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    // Validation Result
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public ValidationSeverity Severity { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new();
    }

    // Backup Statistics
    public class BackupStatistics
    {
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
        public int TotalBackups { get; set; }
        public int SuccessfulBackups { get; set; }
        public int FailedBackups { get; set; }
        public long TotalBackupSize { get; set; }
        public TimeSpan TotalBackupTime { get; set; }
        public TimeSpan AverageBackupTime { get; set; }
        public decimal SuccessRate { get; set; }
        public List<BackupHistoryEntry> RecentBackups { get; set; } = new();
        public Dictionary<BackupType, int> BackupsByType { get; set; } = new();
        public Dictionary<string, int> BackupsByDay { get; set; } = new();
    }

    // Backup Configuration
    public class BackupConfiguration
    {
        public BackupSettings Settings { get; set; } = new();
        public List<BackupSchedule> Schedules { get; set; } = new();
        public List<BackupValidationRule> ValidationRules { get; set; } = new();
        public Dictionary<string, string> CustomSettings { get; set; } = new();
        public DateTime LastModified { get; set; }
        public string ModifiedBy { get; set; } = string.Empty;
        public string Version { get; set; } = "1.0.0";
    }

    // Backup Event
    public class BackupEvent
    {
        public string EventId { get; set; } = string.Empty;
        public DateTime EventDate { get; set; }
        public BackupEventType EventType { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public string? BackupId { get; set; }
        public string? JobId { get; set; }
        public ValidationSeverity Severity { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    // Backup Event Type
    public enum BackupEventType
    {
        BackupStarted,
        BackupCompleted,
        BackupFailed,
        RestoreStarted,
        RestoreCompleted,
        RestoreFailed,
        ScheduleCreated,
        ScheduleModified,
        ScheduleDeleted,
        CleanupCompleted,
        ValidationFailed,
        ConfigurationChanged
    }
}
