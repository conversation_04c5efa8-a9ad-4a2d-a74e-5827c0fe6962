@model IEnumerable<MedicalCenterSystem.Models.MainPayment>

@{
    ViewData["Title"] = "المدفوعات الرئيسية";
}

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2>@ViewData["Title"]</h2>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة دفع رئيسي
            </a>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>التاريخ</th>
                                <th>المريض</th>
                                <th>الطبيب</th>
                                <th>أجرة الكشف</th>
                                <th>أجرة الفحص</th>
                                <th>حصة الطبيب</th>
                                <th>حصة المركز</th>
                                <th>الكاشير</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr>
                                    <td>@item.PaymentDate.ToString("yyyy-MM-dd HH:mm")</td>
                                    <td>@item.PatientVisit?.PatientName</td>
                                    <td>@item.PatientVisit?.Doctor?.FullName</td>
                                    <td>@item.ConsultationFee.ToString("C")</td>
                                    <td>@item.ExamFee.ToString("C")</td>
                                    <td>@item.DoctorShare.ToString("C")</td>
                                    <td>@item.CenterShare.ToString("C")</td>
                                    <td>@item.CashierName</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a asp-action="Details" asp-route-id="@item.MainPaymentId" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@item.MainPaymentId" class="btn btn-warning btn-sm">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.MainPaymentId" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                @if (!Model.Any())
                {
                    <div class="text-center py-4">
                        <p class="text-muted">لا توجد مدفوعات رئيسية مسجلة حالياً</p>
                        <a asp-action="Create" class="btn btn-primary">إضافة أول دفع رئيسي</a>
                    </div>
                }
            </div>
        </div>

        <!-- Summary Card -->
        @if (Model.Any())
        {
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">ملخص المدفوعات الرئيسية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>إجمالي أجور الكشف</h6>
                                <h4 class="text-primary">@Model.Sum(m => m.ConsultationFee).ToString("C")</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>إجمالي أجور الفحص</h6>
                                <h4 class="text-info">@Model.Sum(m => m.ExamFee).ToString("C")</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>إجمالي حصة الأطباء</h6>
                                <h4 class="text-success">@Model.Sum(m => m.DoctorShare).ToString("C")</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>إجمالي حصة المركز</h6>
                                <h4 class="text-warning">@Model.Sum(m => m.CenterShare).ToString("C")</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
}
