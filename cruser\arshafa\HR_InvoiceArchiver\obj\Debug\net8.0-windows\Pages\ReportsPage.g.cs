﻿#pragma checksum "..\..\..\..\Pages\ReportsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "106C8AED3EA7E4373A9272AA70EF3E823A7B0675"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace HR_InvoiceArchiver.Pages {
    
    
    /// <summary>
    /// ReportsPage
    /// </summary>
    public partial class ReportsPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 60 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPaymentsText;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OutstandingAmountText;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InvoicesReportButton;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PaymentsReportButton;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SuppliersReportButton;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MonthlyReportButton;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadDataButton;
        
        #line default
        #line hidden
        
        
        #line 420 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 484 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportTitleText;
        
        #line default
        #line hidden
        
        
        #line 489 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportDateText;
        
        #line default
        #line hidden
        
        
        #line 517 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 543 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearSearchButton;
        
        #line default
        #line hidden
        
        
        #line 557 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ReportDataGrid;
        
        #line default
        #line hidden
        
        
        #line 591 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EmptyStatePanel;
        
        #line default
        #line hidden
        
        
        #line 597 "..\..\..\..\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/HR_InvoiceArchiver;component/pages/reportspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Pages\ReportsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TotalInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TotalPaymentsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.OutstandingAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.InvoicesReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 134 "..\..\..\..\Pages\ReportsPage.xaml"
            this.InvoicesReportButton.Click += new System.Windows.RoutedEventHandler(this.InvoicesReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PaymentsReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 193 "..\..\..\..\Pages\ReportsPage.xaml"
            this.PaymentsReportButton.Click += new System.Windows.RoutedEventHandler(this.PaymentsReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SuppliersReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 252 "..\..\..\..\Pages\ReportsPage.xaml"
            this.SuppliersReportButton.Click += new System.Windows.RoutedEventHandler(this.SuppliersReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.MonthlyReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 311 "..\..\..\..\Pages\ReportsPage.xaml"
            this.MonthlyReportButton.Click += new System.Windows.RoutedEventHandler(this.MonthlyReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.LoadDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 372 "..\..\..\..\Pages\ReportsPage.xaml"
            this.LoadDataButton.Click += new System.Windows.RoutedEventHandler(this.LoadDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 425 "..\..\..\..\Pages\ReportsPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ReportTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ReportDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 523 "..\..\..\..\Pages\ReportsPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ClearSearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 551 "..\..\..\..\Pages\ReportsPage.xaml"
            this.ClearSearchButton.Click += new System.Windows.RoutedEventHandler(this.ClearSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ReportDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 16:
            this.EmptyStatePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

