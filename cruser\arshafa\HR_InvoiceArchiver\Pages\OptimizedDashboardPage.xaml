<UserControl x:Class="HR_InvoiceArchiver.Pages.OptimizedDashboardPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:dashboard="clr-namespace:HR_InvoiceArchiver.Controls.Dashboard"
             FlowDirection="RightToLeft"
             Background="#F8F9FA">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HR_InvoiceArchiver;component/Styles/MaterialDesignStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Simplified Loading Animation -->
            <Storyboard x:Key="LoadingAnimation" RepeatBehavior="Forever">
                <DoubleAnimation Storyboard.TargetName="LoadingIcon"
                               Storyboard.TargetProperty="RenderTransform.Angle"
                               From="0" To="360" Duration="0:0:1"/>
            </Storyboard>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <!-- Loading Overlay -->
        <Grid x:Name="LoadingOverlay" Background="#80FFFFFF" Visibility="Collapsed" Panel.ZIndex="1000">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon x:Name="LoadingIcon" Kind="Loading" Width="32" Height="32"
                                       Foreground="#1976D2">
                    <materialDesign:PackIcon.RenderTransform>
                        <RotateTransform Angle="0"/>
                    </materialDesign:PackIcon.RenderTransform>
                </materialDesign:PackIcon>
                <TextBlock Text="جاري تحميل البيانات..." Margin="0,8,0,0"
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- Main Content -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <StackPanel Margin="20">
                
                <!-- Welcome Header -->
                <materialDesign:Card Style="{StaticResource ModernCardStyle}" Margin="0,0,0,20">
                    <Grid Background="#1976D2" Margin="0">
                        <Grid.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#1976D2" Offset="0"/>
                                <GradientStop Color="#1565C0" Offset="1"/>
                            </LinearGradientBrush>
                        </Grid.Background>
                        <Grid Margin="24">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <materialDesign:PackIcon Grid.Column="0" Kind="ViewDashboard" 
                                                   Width="48" Height="48"
                                                   Foreground="White" VerticalAlignment="Center" 
                                                   Margin="0,0,16,0"/>

                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                <TextBlock x:Name="WelcomeText" Text="مرحباً بك في لوحة التحكم"
                                         FontSize="24" FontWeight="Bold" Foreground="White"/>
                                <TextBlock x:Name="WelcomeSubText" Text="نظرة شاملة على حالة الفواتير والمدفوعات"
                                         FontSize="14" Foreground="#E3F2FD" Margin="0,4,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                <TextBlock x:Name="LastUpdateText" Text="آخر تحديث: الآن"
                                         FontSize="12" Foreground="#E3F2FD" HorizontalAlignment="Right"/>
                                <Button x:Name="RefreshButton" 
                                      Style="{StaticResource MaterialDesignIconButton}"
                                      Foreground="White" Margin="0,4,0,0"
                                      Click="RefreshButton_Click"
                                      ToolTip="تحديث البيانات">
                                    <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </materialDesign:Card>

                <!-- Statistics Cards -->
                <dashboard:StatisticsCardsControl x:Name="StatisticsCards" Margin="0,0,0,20"/>

                <!-- Charts and Recent Activities -->
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Chart Section -->
                    <dashboard:ChartSectionControl x:Name="ChartSection" Grid.Column="0" Margin="0,0,10,0"/>

                    <!-- Status Distribution -->
                    <materialDesign:Card Grid.Column="1" Style="{StaticResource ModernCardStyle}" Margin="10,0,0,0">
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="توزيع حالات الفواتير"
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     Foreground="#1976D2" FontWeight="Bold" Margin="0,0,0,16"/>

                            <StackPanel Grid.Row="1">
                                <!-- Unpaid -->
                                <Border Background="#FFEBEE" CornerRadius="8" Padding="12" Margin="0,0,0,8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <materialDesign:PackIcon Grid.Column="0" Kind="AlertCircle" 
                                                               Width="16" Height="16" Foreground="#F44336"/>
                                        <TextBlock Grid.Column="1" Text="غير مسددة" Margin="8,0,0,0"/>
                                        <TextBlock Grid.Column="2" x:Name="UnpaidCountText" Text="0" 
                                                 FontWeight="Bold" Foreground="#F44336"/>
                                    </Grid>
                                </Border>

                                <!-- Partially Paid -->
                                <Border Background="#FFF3E0" CornerRadius="8" Padding="12" Margin="0,0,0,8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <materialDesign:PackIcon Grid.Column="0" Kind="ClockAlert" 
                                                               Width="16" Height="16" Foreground="#FF9800"/>
                                        <TextBlock Grid.Column="1" Text="مسددة جزئياً" Margin="8,0,0,0"/>
                                        <TextBlock Grid.Column="2" x:Name="PartiallyPaidCountText" Text="0" 
                                                 FontWeight="Bold" Foreground="#FF9800"/>
                                    </Grid>
                                </Border>

                                <!-- Paid -->
                                <Border Background="#E8F5E8" CornerRadius="8" Padding="12" Margin="0,0,0,16">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <materialDesign:PackIcon Grid.Column="0" Kind="CheckCircle" 
                                                               Width="16" Height="16" Foreground="#4CAF50"/>
                                        <TextBlock Grid.Column="1" Text="مسددة" Margin="8,0,0,0"/>
                                        <TextBlock Grid.Column="2" x:Name="PaidCountText" Text="0" 
                                                 FontWeight="Bold" Foreground="#4CAF50"/>
                                    </Grid>
                                </Border>

                                <!-- Payment Rate -->
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="معدل التسديد" FontWeight="Medium"/>
                                    <TextBlock Grid.Column="1" x:Name="PaymentRateText" Text="0%" 
                                             FontWeight="Bold" Foreground="#1976D2"/>
                                </Grid>
                                <ProgressBar x:Name="PaymentRateProgress" Height="6" Value="0" 
                                           Maximum="100" Margin="0,4,0,0"
                                           Foreground="#1976D2" Background="#E0E0E0"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>
                </Grid>

                <!-- Recent Activities -->
                <dashboard:RecentActivitiesControl x:Name="RecentActivities"/>

                <!-- Quick Actions -->
                <materialDesign:Card Style="{StaticResource ModernCardStyle}" Margin="0,20,0,0">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="إجراءات سريعة"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="#1976D2" FontWeight="Bold" Margin="0,0,0,16"/>

                        <UniformGrid Grid.Row="1" Columns="4" Rows="1">
                            <Button x:Name="AddInvoiceButton" Style="{StaticResource ModernActionButtonStyle}"
                                  Click="AddInvoiceButton_Click" Margin="0,0,8,0">
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Plus" Width="24" Height="24" 
                                                           Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="إضافة فاتورة" FontWeight="Medium" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="AddPaymentButton" Style="{StaticResource ModernActionButtonStyle}"
                                  Click="AddPaymentButton_Click" Margin="8,0,8,0">
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="CurrencyUsd" Width="24" Height="24" 
                                                           Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="إضافة دفعة" FontWeight="Medium" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="ViewReportsButton" Style="{StaticResource ModernActionButtonStyle}"
                                  Click="ViewReportsButton_Click" Margin="8,0,8,0">
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="ChartLine" Width="24" Height="24" 
                                                           Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="التقارير" FontWeight="Medium" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="SearchButton" Style="{StaticResource ModernActionButtonStyle}"
                                  Click="SearchButton_Click" Margin="8,0,0,0">
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Magnify" Width="24" Height="24" 
                                                           Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="البحث" FontWeight="Medium" Foreground="White"/>
                                </StackPanel>
                            </Button>
                        </UniformGrid>
                    </Grid>
                </materialDesign:Card>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
