<UserControl x:Class="HR_InvoiceArchiver.Controls.SupplierFormControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">

    <UserControl.Resources>
        <!-- Modern Gradient Brushes -->
        <LinearGradientBrush x:Key="SupplierGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#4CAF50" Offset="0"/>
            <GradientStop Color="#388E3C" Offset="1"/>
        </LinearGradientBrush>
        
        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF9800" Offset="0"/>
            <GradientStop Color="#F57C00" Offset="1"/>
        </LinearGradientBrush>
        
        <!-- Enhanced Slide In Animation -->
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                           From="400" To="0" Duration="0:0:0.6">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.4"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.4"/>
        </Storyboard>
        
        <!-- Enhanced Slide Out Animation -->
        <Storyboard x:Key="SlideOutAnimation" Completed="SlideOutAnimation_Completed">
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(TranslateTransform.X)"
                           From="0" To="400" Duration="0:0:0.4">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseIn" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.3"/>
        </Storyboard>
        
        <!-- Save Success Animation -->
        <Storyboard x:Key="SaveSuccessAnimation">
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleX)"
                           From="1" To="1.05" Duration="0:0:0.2" AutoReverse="True"/>
            <DoubleAnimation Storyboard.TargetName="MainCard" 
                           Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(ScaleTransform.ScaleY)"
                           From="1" To="1.05" Duration="0:0:0.2" AutoReverse="True"/>
        </Storyboard>
        
        <!-- Modern Card Style -->
        <Style x:Key="ModernSupplierCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#000000" BlurRadius="20" ShadowDepth="8" Opacity="0.2"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- Modern Input Style -->
        <Style x:Key="ModernInputStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="{StaticResource SupplierGradient}"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
        </Style>
    </UserControl.Resources>

    <!-- Main Container -->
    <Grid Background="Transparent" MouseLeftButtonDown="BackgroundGrid_MouseLeftButtonDown">

        <!-- Background Overlay -->
        <Rectangle Fill="#40000000" RadiusX="16" RadiusY="16" Opacity="0.3"/>

        <!-- Main Supplier Card -->
        <materialDesign:Card x:Name="MainCard" Style="{StaticResource ModernSupplierCardStyle}"
                            Width="550" MaxHeight="800" HorizontalAlignment="Center"
                            VerticalAlignment="Center" Margin="20">
            
            <materialDesign:Card.RenderTransform>
                <TransformGroup>
                    <TranslateTransform/>
                    <ScaleTransform/>
                </TransformGroup>
            </materialDesign:Card.RenderTransform>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Modern Header -->
                <Border Grid.Row="0" Background="{StaticResource HeaderGradient}" 
                       CornerRadius="16,16,0,0" Padding="24,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Supplier Icon -->
                        <Border Grid.Column="0" Background="White" CornerRadius="20" 
                               Width="40" Height="40" Margin="0,0,16,0">
                            <materialDesign:PackIcon Kind="AccountGroup" 
                                                   Width="24" Height="24" 
                                                   Foreground="{StaticResource HeaderGradient}" 
                                                   HorizontalAlignment="Center" 
                                                   VerticalAlignment="Center"/>
                        </Border>
                        
                        <!-- Title -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock x:Name="FormTitleTextBlock" Text="إضافة مورد جديد" 
                                      FontSize="20" FontWeight="Bold" 
                                      Foreground="White" Margin="0,0,0,4"/>
                            <TextBlock Text="إدارة بيانات الموردين والشركاء التجاريين" 
                                      FontSize="12" Foreground="White" 
                                      Opacity="0.9"/>
                        </StackPanel>
                        
                        <!-- Close Button -->
                        <Button Grid.Column="2" 
                               Click="CloseButton_Click" 
                               Width="36" Height="36" 
                               Background="Transparent"
                               BorderBrush="White"
                               BorderThickness="1"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Foreground="White">
                            <materialDesign:PackIcon Kind="Close" Width="18" Height="18"/>
                        </Button>
                    </Grid>
                </Border>
                
                <!-- Form Content -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="24,20">
                    <StackPanel>
                        <!-- Basic Information Section -->
                        <Border Background="#F8F9FA" CornerRadius="12" Padding="20" Margin="0,0,0,20">
                            <StackPanel>
                                <TextBlock Text="🏢 المعلومات الأساسية" 
                                          FontSize="16" FontWeight="SemiBold" 
                                          Foreground="#FF9800" Margin="0,0,0,16"/>
                                
                                <!-- Supplier Name -->
                                <TextBlock Text="اسم المورد *" FontWeight="Medium" 
                                          Foreground="#424242" Margin="0,0,0,8"/>
                                <TextBox x:Name="NameTextBox"
                                        Style="{StaticResource ModernInputStyle}"
                                        materialDesign:HintAssist.Hint="أدخل اسم المورد أو الشركة"/>
                                
                                <!-- Contact Person -->
                                <TextBlock Text="الشخص المسؤول" FontWeight="Medium"
                                          Foreground="#424242" Margin="0,0,0,8"/>
                                <TextBox x:Name="ContactPersonTextBox"
                                        Style="{StaticResource ModernInputStyle}"
                                        materialDesign:HintAssist.Hint="اسم الشخص المسؤول"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Contact Information Section -->
                        <Border Background="#F0F8FF" CornerRadius="12" Padding="20" Margin="0,0,0,20">
                            <StackPanel>
                                <TextBlock Text="📞 معلومات الاتصال" 
                                          FontSize="16" FontWeight="SemiBold" 
                                          Foreground="#2196F3" Margin="0,0,0,16"/>
                                
                                <!-- Phone -->
                                <TextBlock Text="رقم الهاتف" FontWeight="Medium" 
                                          Foreground="#424242" Margin="0,0,0,8"/>
                                <TextBox x:Name="PhoneTextBox"
                                        Style="{StaticResource ModernInputStyle}"
                                        materialDesign:HintAssist.Hint="رقم الهاتف أو الجوال"/>
                                
                                <!-- Email -->
                                <TextBlock Text="البريد الإلكتروني" FontWeight="Medium" 
                                          Foreground="#424242" Margin="0,0,0,8"/>
                                <TextBox x:Name="EmailTextBox"
                                        Style="{StaticResource ModernInputStyle}"
                                        materialDesign:HintAssist.Hint="البريد الإلكتروني"/>
                                
                                <!-- Address -->
                                <TextBlock Text="العنوان" FontWeight="Medium" 
                                          Foreground="#424242" Margin="0,0,0,8"/>
                                <TextBox x:Name="AddressTextBox" 
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                        materialDesign:HintAssist.Hint="العنوان الكامل"
                                        Height="80" 
                                        TextWrapping="Wrap" 
                                        AcceptsReturn="True"
                                        VerticalScrollBarVisibility="Auto"
                                        FontSize="14"
                                        Padding="16,12"
                                        Margin="0,0,0,20"
                                        materialDesign:TextFieldAssist.TextFieldCornerRadius="8"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Additional Information Section -->
                        <Border Background="#F0FFF0" CornerRadius="12" Padding="20">
                            <StackPanel>
                                <TextBlock Text="📝 معلومات إضافية" 
                                          FontSize="16" FontWeight="SemiBold" 
                                          Foreground="#4CAF50" Margin="0,0,0,16"/>
                                
                                <!-- Notes -->
                                <TextBlock Text="الملاحظات" FontWeight="Medium" 
                                          Foreground="#424242" Margin="0,0,0,8"/>
                                <TextBox x:Name="NotesTextBox" 
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                        materialDesign:HintAssist.Hint="ملاحظات إضافية (اختياري)"
                                        Height="80" 
                                        TextWrapping="Wrap" 
                                        AcceptsReturn="True"
                                        VerticalScrollBarVisibility="Auto"
                                        FontSize="14"
                                        Padding="16,12"
                                        Margin="0,0,0,0"
                                        materialDesign:TextFieldAssist.TextFieldCornerRadius="8"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
                
                <!-- Action Buttons -->
                <Border Grid.Row="2" Background="#FAFAFA" 
                       CornerRadius="0,0,16,16" Padding="24,16">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button x:Name="SaveButton"
                               Click="SaveButton_Click"
                               Style="{StaticResource ModernPrimaryButtonStyle}"
                               Background="{StaticResource SupplierGradient}"
                               BorderBrush="{StaticResource SupplierGradient}"
                               Foreground="White"
                               FontWeight="SemiBold"
                               MinWidth="120"
                               Height="44"
                               Margin="0,0,16,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="حفظ"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="CancelButton"
                               Click="CancelButton_Click"
                               Style="{StaticResource ModernSecondaryButtonStyle}"
                               BorderBrush="#757575"
                               Foreground="#757575"
                               FontWeight="SemiBold"
                               MinWidth="120"
                               Height="44">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cancel" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="إلغاء"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="DeleteButton"
                               Click="DeleteButton_Click"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Background="#F44336"
                               BorderBrush="#F44336"
                               Foreground="White"
                               FontWeight="SemiBold"
                               MinWidth="120"
                               Height="44"
                               Margin="16,0,0,0"
                               Visibility="Collapsed">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Delete" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="حذف"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Border>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
