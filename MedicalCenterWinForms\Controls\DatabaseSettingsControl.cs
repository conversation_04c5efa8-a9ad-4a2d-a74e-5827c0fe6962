using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using Microsoft.Data.SqlClient;

namespace MedicalCenterWinForms.Controls
{
    public partial class DatabaseSettingsControl : BaseUserControl
    {
        public event EventHandler? SettingsSaved;

        public DatabaseSettingsControl() : base()
        {
            InitializeComponent();
            LoadCurrentSettings();
        }

        public DatabaseSettingsControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            LoadCurrentSettings();
        }

        private void LoadCurrentSettings()
        {
            try
            {
                chkUseNetwork.Checked = DatabaseService.IsUsingNetworkDatabase();

                if (chkUseNetwork.Checked)
                {
                    var connectionString = DatabaseService.GetConnectionString();
                    var builder = new SqlConnectionStringBuilder(connectionString);

                    txtServerName.Text = builder.DataSource;
                    txtDatabaseName.Text = builder.InitialCatalog;
                    txtUsername.Text = builder.UserID;
                    txtPassword.Text = builder.Password;
                }

                UpdateControlsVisibility();
            }
            catch (Exception ex)
            {
                ShowWarning($"خطأ في تحميل الإعدادات: {ex.Message}");
            }
        }

        private void chkUseNetwork_CheckedChanged(object sender, EventArgs e)
        {
            UpdateControlsVisibility();
        }

        private void UpdateControlsVisibility()
        {
            pnlNetworkSettings.Visible = chkUseNetwork.Checked;
            
            if (!chkUseNetwork.Checked)
            {
                // Clear network settings when switching to local
                txtServerName.Clear();
                txtDatabaseName.Clear();
                txtUsername.Clear();
                txtPassword.Clear();
            }
        }

        private async void btnTestConnection_Click(object sender, EventArgs e)
        {
            if (!chkUseNetwork.Checked)
            {
                ShowWarning("اختبار الاتصال متاح فقط لقاعدة البيانات الشبكية");
                return;
            }

            if (!ValidateNetworkSettings())
                return;

            try
            {
                SetLoadingState(true);
                btnTestConnection.Text = "جاري الاختبار...";

                var connectionString = $"Server={txtServerName.Text.Trim()};Database={txtDatabaseName.Text.Trim()};User Id={txtUsername.Text.Trim()};Password={txtPassword.Text};MultipleActiveResultSets=true;TrustServerCertificate=true;Connection Timeout=10";

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                ShowSuccess("تم الاتصال بنجاح!");
            }
            catch (Exception ex)
            {
                ShowError($"فشل الاتصال:\n{ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
                btnTestConnection.Text = "اختبار الاتصال";
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (chkUseNetwork.Checked)
                {
                    if (!ValidateNetworkSettings())
                        return;

                    DatabaseService.UpdateNetworkSettings(
                        txtServerName.Text.Trim(),
                        txtDatabaseName.Text.Trim(),
                        txtUsername.Text.Trim(),
                        txtPassword.Text
                    );
                }

                DatabaseService.SetUseNetworkDatabase(chkUseNetwork.Checked);

                ShowSuccess("تم حفظ الإعدادات بنجاح");
                SettingsSaved?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            if (ShowConfirmation("هل تريد إعادة تعيين الإعدادات إلى الوضع المحلي؟"))
            {
                chkUseNetwork.Checked = false;
                txtServerName.Clear();
                txtDatabaseName.Clear();
                txtUsername.Clear();
                txtPassword.Clear();
                UpdateControlsVisibility();
            }
        }

        private bool ValidateNetworkSettings()
        {
            if (string.IsNullOrWhiteSpace(txtServerName.Text))
            {
                ShowWarning("يرجى إدخال اسم الخادم");
                txtServerName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtDatabaseName.Text))
            {
                ShowWarning("يرجى إدخال اسم قاعدة البيانات");
                txtDatabaseName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                ShowWarning("يرجى إدخال اسم المستخدم");
                txtUsername.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                ShowWarning("يرجى إدخال كلمة المرور");
                txtPassword.Focus();
                return false;
            }

            return true;
        }

        public void RefreshSettings()
        {
            LoadCurrentSettings();
        }
    }

    public partial class DatabaseSettingsControl
    {
        private CheckBox chkUseNetwork;
        private Panel pnlNetworkSettings;
        private TextBox txtServerName;
        private TextBox txtDatabaseName;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnTestConnection;
        private Button btnSave;
        private Button btnReset;
        private Panel pnlMain;
        private Panel pnlButtons;

        private void InitializeComponent()
        {
            this.pnlMain = CreateStyledPanel();
            this.pnlNetworkSettings = CreateStyledPanel(Color.FromArgb(248, 249, 250));
            this.pnlButtons = CreateStyledPanel();

            this.chkUseNetwork = new CheckBox
            {
                Text = "استخدام قاعدة بيانات الشبكة",
                Font = ArabicFontHelper.GetArabicFont(11F, FontStyle.Bold),
                AutoSize = true
            };

            this.txtServerName = CreateStyledTextBox("مثال: SERVER-NAME\\SQLEXPRESS");
            this.txtDatabaseName = CreateStyledTextBox("مثال: MedicalCenterDB");
            this.txtUsername = CreateStyledTextBox();
            this.txtPassword = CreateStyledTextBox();
            this.txtPassword.PasswordChar = '*';

            this.btnTestConnection = CreateStyledButton("اختبار الاتصال", Color.FromArgb(52, 152, 219), btnTestConnection_Click);
            this.btnSave = CreateStyledButton("حفظ", Color.FromArgb(46, 204, 113), btnSave_Click);
            this.btnReset = CreateStyledButton("إعادة تعيين", Color.FromArgb(231, 76, 60), btnReset_Click);

            this.SuspendLayout();

            // pnlMain
            this.pnlMain.Controls.Add(CreateStyledLabel("إعدادات قاعدة البيانات", true));
            this.pnlMain.Controls.Add(this.chkUseNetwork);
            this.pnlMain.Controls.Add(this.pnlNetworkSettings);
            this.pnlMain.Controls.Add(this.pnlButtons);
            this.pnlMain.Dock = DockStyle.Fill;

            // Title positioning
            var lblTitle = this.pnlMain.Controls.OfType<Label>().First();
            lblTitle.Location = new Point(20, 20);
            lblTitle.Font = ArabicFontHelper.GetArabicTitleFont(14F);
            lblTitle.ForeColor = Color.FromArgb(52, 73, 94);

            // chkUseNetwork
            this.chkUseNetwork.Location = new Point(20, 60);
            this.chkUseNetwork.CheckedChanged += chkUseNetwork_CheckedChanged;

            // pnlNetworkSettings
            this.pnlNetworkSettings.BorderStyle = BorderStyle.FixedSingle;
            this.pnlNetworkSettings.Location = new Point(20, 100);
            this.pnlNetworkSettings.Size = new Size(460, 280);
            this.pnlNetworkSettings.Visible = false;

            this.pnlNetworkSettings.Controls.Add(CreateStyledLabel("اسم الخادم:", true));
            this.pnlNetworkSettings.Controls.Add(this.txtServerName);
            this.pnlNetworkSettings.Controls.Add(CreateStyledLabel("اسم قاعدة البيانات:", true));
            this.pnlNetworkSettings.Controls.Add(this.txtDatabaseName);
            this.pnlNetworkSettings.Controls.Add(CreateStyledLabel("اسم المستخدم:", true));
            this.pnlNetworkSettings.Controls.Add(this.txtUsername);
            this.pnlNetworkSettings.Controls.Add(CreateStyledLabel("كلمة المرور:", true));
            this.pnlNetworkSettings.Controls.Add(this.txtPassword);
            this.pnlNetworkSettings.Controls.Add(this.btnTestConnection);

            // Network settings controls positioning
            var networkLabels = this.pnlNetworkSettings.Controls.OfType<Label>().ToArray();
            var networkControls = new Control[] { txtServerName, txtDatabaseName, txtUsername, txtPassword };

            for (int i = 0; i < networkLabels.Length && i < networkControls.Length; i++)
            {
                int y = 15 + i * 60;
                networkLabels[i].Location = new Point(15, y);
                networkControls[i].Location = new Point(15, y + 20);

                if (i < 2) // Server name and database name - full width
                {
                    networkControls[i].Size = new Size(420, 24);
                }
                else // Username and password - half width
                {
                    networkControls[i].Size = new Size(200, 24);
                    if (i == 3) // Password - position to the right
                    {
                        networkControls[i].Location = new Point(235, y + 20);
                        networkLabels[i].Location = new Point(235, y);
                    }
                }
            }

            this.btnTestConnection.Location = new Point(15, 200);
            this.btnTestConnection.Size = new Size(120, 35);

            // pnlButtons
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnReset);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Height = 60;

            this.btnSave.Location = new Point(280, 10);
            this.btnReset.Location = new Point(380, 10);

            // DatabaseSettingsControl
            this.Controls.Add(this.pnlMain);
            this.Name = "DatabaseSettingsControl";
            this.Size = new Size(500, 450);

            this.ResumeLayout(false);
        }
    }
}
