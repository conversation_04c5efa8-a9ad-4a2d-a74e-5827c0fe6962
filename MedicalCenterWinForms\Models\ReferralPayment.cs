using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MedicalCenterWinForms.Models
{
    public class ReferralPayment
    {
        public int ReferralPaymentId { get; set; }

        [Required]
        public int PatientVisitId { get; set; }

        [Required]
        public int MedicalServiceId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? DoctorShare { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CenterShare { get; set; }

        [StringLength(100)]
        public string Section { get; set; } = string.Empty;

        [StringLength(100)]
        public string CashierName { get; set; } = string.Empty;

        public DateTime PaymentDate { get; set; } = DateTime.Now;

        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        [ForeignKey("PatientVisitId")]
        public virtual PatientVisit PatientVisit { get; set; } = null!;

        [ForeignKey("MedicalServiceId")]
        public virtual MedicalService MedicalService { get; set; } = null!;
    }
}
