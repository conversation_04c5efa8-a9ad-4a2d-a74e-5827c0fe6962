<UserControl x:Class="HR_InvoiceArchiver.Pages.SettingsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <materialDesign:PackIcon Grid.Column="0" Kind="Settings"
                                       Width="32" Height="32"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                <StackPanel Grid.Column="1" Margin="16,0,0,0" VerticalAlignment="Center">
                    <TextBlock Text="إعدادات النظام"
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                    <TextBlock Text="إدارة وتخصيص إعدادات التطبيق"
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Opacity="0.7"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="ImportButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="ImportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Import" Margin="0,0,8,0"/>
                            <TextBlock Text="استيراد"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ExportButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="ExportButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Export" Margin="0,0,8,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ResetButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Click="ResetButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Restore" Margin="0,0,8,0"/>
                            <TextBlock Text="إعادة تعيين"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Settings Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <TabControl x:Name="SettingsTabControl"
                      Style="{StaticResource MaterialDesignTabControl}"
                      Margin="16,0,16,8">

                <!-- General Settings -->
                <TabItem Header="عام">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Settings" Margin="0,0,8,0"/>
                                <TextBlock Text="عام"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="معلومات التطبيق"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <TextBox x:Name="ApplicationNameTextBox"
                                               Grid.Row="0" Grid.Column="0"
                                               materialDesign:HintAssist.Hint="اسم التطبيق"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,16"/>

                                        <TextBox x:Name="CompanyNameTextBox"
                                               Grid.Row="0" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="اسم الشركة"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,16"/>

                                        <TextBox x:Name="CompanyAddressTextBox"
                                               Grid.Row="1" Grid.Column="0"
                                               materialDesign:HintAssist.Hint="عنوان الشركة"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,16"/>

                                        <TextBox x:Name="CompanyPhoneTextBox"
                                               Grid.Row="1" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="هاتف الشركة"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,16"/>

                                        <TextBox x:Name="CompanyEmailTextBox"
                                               Grid.Row="2" Grid.ColumnSpan="2"
                                               materialDesign:HintAssist.Hint="بريد الشركة الإلكتروني"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,0,16"/>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="إعدادات الواجهة"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <ComboBox x:Name="LanguageComboBox"
                                                Grid.Row="0" Grid.Column="0"
                                                materialDesign:HintAssist.Hint="اللغة"
                                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                Margin="0,0,8,16">
                                            <ComboBoxItem Content="العربية" Tag="ar-SA"/>
                                            <ComboBoxItem Content="English" Tag="en-US"/>
                                        </ComboBox>

                                        <ComboBox x:Name="ThemeComboBox"
                                                Grid.Row="0" Grid.Column="1"
                                                materialDesign:HintAssist.Hint="السمة"
                                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                Margin="8,0,0,16">
                                            <ComboBoxItem Content="فاتح" Tag="Light"/>
                                            <ComboBoxItem Content="داكن" Tag="Dark"/>
                                        </ComboBox>

                                        <CheckBox x:Name="EnableNotificationsCheckBox"
                                                Grid.Row="1" Grid.Column="0"
                                                Content="تفعيل الإشعارات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,8,0"/>

                                        <CheckBox x:Name="EnableSoundsCheckBox"
                                                Grid.Row="1" Grid.Column="1"
                                                Content="تفعيل الأصوات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="8,0,0,0"/>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- Database Settings -->
                <TabItem Header="قاعدة البيانات">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Database" Margin="0,0,8,0"/>
                                <TextBlock Text="قاعدة البيانات"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="إعدادات قاعدة البيانات"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Grid Grid.Row="0" Margin="0,0,0,16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBox x:Name="DatabasePathTextBox"
                                                   Grid.Column="0"
                                                   materialDesign:HintAssist.Hint="مسار قاعدة البيانات"
                                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                   IsReadOnly="True"
                                                   Margin="0,0,8,0"/>

                                            <Button x:Name="BrowseDatabaseButton"
                                                  Grid.Column="1"
                                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                                  Click="BrowseDatabaseButton_Click">
                                                <materialDesign:PackIcon Kind="FolderOpen"/>
                                            </Button>
                                        </Grid>

                                        <Grid Grid.Row="1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <CheckBox x:Name="EnableDatabaseBackupCheckBox"
                                                    Grid.Column="0"
                                                    Content="تفعيل النسخ الاحتياطي"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    Margin="0,0,8,16"/>

                                            <CheckBox x:Name="EnableDatabaseEncryptionCheckBox"
                                                    Grid.Column="1"
                                                    Content="تفعيل التشفير"
                                                    Style="{StaticResource MaterialDesignCheckBox}"
                                                    Margin="8,0,0,16"/>
                                        </Grid>

                                        <Grid Grid.Row="2">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBox x:Name="BackupIntervalTextBox"
                                                   Grid.Column="0"
                                                   materialDesign:HintAssist.Hint="فترة النسخ الاحتياطي (ساعات)"
                                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                   Margin="0,0,8,0"/>

                                            <TextBox x:Name="MaxBackupFilesTextBox"
                                                   Grid.Column="1"
                                                   materialDesign:HintAssist.Hint="الحد الأقصى لملفات النسخ"
                                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                   Margin="8,0,0,0"/>
                                        </Grid>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- Cloud Settings -->
                <TabItem Header="التخزين السحابي">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cloud" Margin="0,0,8,0"/>
                                <TextBlock Text="التخزين السحابي"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="إعدادات التخزين السحابي"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>

                                    <CheckBox x:Name="EnableCloudSyncCheckBox"
                                            Content="تفعيل المزامنة السحابية"
                                            Style="{StaticResource MaterialDesignCheckBox}"
                                            Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <ComboBox x:Name="CloudProviderComboBox"
                                                Grid.Row="0" Grid.Column="0"
                                                materialDesign:HintAssist.Hint="مزود الخدمة"
                                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                Margin="0,0,8,16">
                                            <ComboBoxItem Content="Google Drive" Tag="GoogleDrive"/>
                                            <ComboBoxItem Content="OneDrive" Tag="OneDrive"/>
                                            <ComboBoxItem Content="Dropbox" Tag="Dropbox"/>
                                        </ComboBox>

                                        <TextBox x:Name="SyncIntervalTextBox"
                                               Grid.Row="0" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="فترة المزامنة (دقائق)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,16"/>

                                        <Grid Grid.Row="1" Grid.ColumnSpan="2" Margin="0,0,0,16">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBox x:Name="CloudCredentialsPathTextBox"
                                                   Grid.Column="0"
                                                   materialDesign:HintAssist.Hint="مسار ملف بيانات الاعتماد"
                                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                   IsReadOnly="True"
                                                   Margin="0,0,8,0"/>

                                            <Button x:Name="BrowseCredentialsButton"
                                                  Grid.Column="1"
                                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                                  Click="BrowseCredentialsButton_Click">
                                                <materialDesign:PackIcon Kind="FolderOpen"/>
                                            </Button>
                                        </Grid>

                                        <CheckBox x:Name="SyncOnStartupCheckBox"
                                                Grid.Row="2" Grid.Column="0"
                                                Content="مزامنة عند بدء التشغيل"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,8,0"/>

                                        <CheckBox x:Name="SyncOnShutdownCheckBox"
                                                Grid.Row="2" Grid.Column="1"
                                                Content="مزامنة عند الإغلاق"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="8,0,0,0"/>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- Security Settings -->
                <TabItem Header="الأمان">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Security" Margin="0,0,8,0"/>
                                <TextBlock Text="الأمان"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="إعدادات الأمان"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <CheckBox x:Name="EnableAuditLogCheckBox"
                                                Grid.Row="0" Grid.Column="0"
                                                Content="تفعيل سجل المراجعة"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,8,16"/>

                                        <CheckBox x:Name="EnableDataEncryptionCheckBox"
                                                Grid.Row="0" Grid.Column="1"
                                                Content="تفعيل تشفير البيانات"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="8,0,0,16"/>

                                        <TextBox x:Name="SessionTimeoutTextBox"
                                               Grid.Row="1" Grid.Column="0"
                                               materialDesign:HintAssist.Hint="مهلة انتهاء الجلسة (دقائق)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,16"/>

                                        <TextBox x:Name="MaxLoginAttemptsTextBox"
                                               Grid.Row="1" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="الحد الأقصى لمحاولات تسجيل الدخول"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,16"/>

                                        <CheckBox x:Name="RequirePasswordOnStartupCheckBox"
                                                Grid.Row="2" Grid.Column="0"
                                                Content="طلب كلمة مرور عند بدء التشغيل"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,8,0"/>

                                        <TextBox x:Name="LockoutDurationTextBox"
                                               Grid.Row="2" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="مدة القفل (دقائق)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,0"/>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- Performance Settings -->
                <TabItem Header="الأداء">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Speedometer" Margin="0,0,8,0"/>
                                <TextBlock Text="الأداء"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="إعدادات الأداء"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <CheckBox x:Name="EnablePerformanceMonitoringCheckBox"
                                                Grid.Row="0" Grid.Column="0"
                                                Content="تفعيل مراقبة الأداء"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="0,0,8,16"/>

                                        <CheckBox x:Name="EnableCachingCheckBox"
                                                Grid.Row="0" Grid.Column="1"
                                                Content="تفعيل التخزين المؤقت"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="8,0,0,16"/>

                                        <TextBox x:Name="MaxLogEntriesTextBox"
                                               Grid.Row="1" Grid.Column="0"
                                               materialDesign:HintAssist.Hint="الحد الأقصى لإدخالات السجل"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,16"/>

                                        <TextBox x:Name="LogRetentionDaysTextBox"
                                               Grid.Row="1" Grid.Column="1"
                                               materialDesign:HintAssist.Hint="مدة الاحتفاظ بالسجلات (أيام)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="8,0,0,16"/>

                                        <TextBox x:Name="CacheExpirationTextBox"
                                               Grid.Row="2" Grid.Column="0"
                                               materialDesign:HintAssist.Hint="مدة انتهاء التخزين المؤقت (دقائق)"
                                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                               Margin="0,0,8,0"/>

                                        <CheckBox x:Name="EnableLazyLoadingCheckBox"
                                                Grid.Row="2" Grid.Column="1"
                                                Content="تفعيل التحميل الكسول"
                                                Style="{StaticResource MaterialDesignCheckBox}"
                                                Margin="8,0,0,0"/>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- System Info -->
                <TabItem Header="معلومات النظام">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Information" Margin="0,0,8,0"/>
                                <TextBlock Text="معلومات النظام"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>

                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="معلومات التطبيق"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>

                                    <Grid x:Name="SystemInfoGrid">
                                        <!-- سيتم ملء هذا الجزء برمجياً -->
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>
            </TabControl>
        </ScrollViewer>

        <!-- Action Buttons -->
        <materialDesign:Card Grid.Row="2" Margin="16,8,16,16" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button x:Name="TestConnectionButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="TestConnectionButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="TestTube" Margin="0,0,8,0"/>
                            <TextBlock Text="اختبار الاتصال"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="BackupNowButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Click="BackupNowButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Backup" Margin="0,0,8,0"/>
                            <TextBlock Text="نسخ احتياطي الآن"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="CancelButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="CancelButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Cancel" Margin="0,0,8,0"/>
                            <TextBlock Text="إلغاء"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="SaveButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Click="SaveButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" Margin="0,0,8,0"/>
                            <TextBlock Text="حفظ"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
