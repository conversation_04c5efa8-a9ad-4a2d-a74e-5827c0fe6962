using System.Collections.Generic;
using System.Windows.Controls;

namespace HR_InvoiceArchiver.Controls
{
    /// <summary>
    /// Loading Skeleton محسن لتحسين تجربة المستخدم
    /// </summary>
    public partial class LoadingSkeleton : UserControl
    {
        public LoadingSkeleton()
        {
            InitializeComponent();
            InitializeSkeletonRows();
        }

        /// <summary>
        /// تهيئة صفوف Skeleton للجدول
        /// </summary>
        private void InitializeSkeletonRows()
        {
            var skeletonData = new List<object>();
            
            // إضافة 8 صفوف skeleton
            for (int i = 0; i < 8; i++)
            {
                skeletonData.Add(new object());
            }

            SkeletonRows.ItemsSource = skeletonData;
        }

        /// <summary>
        /// تخصيص عدد الصفوف
        /// </summary>
        public void SetRowCount(int rowCount)
        {
            var skeletonData = new List<object>();
            
            for (int i = 0; i < rowCount; i++)
            {
                skeletonData.Add(new object());
            }

            SkeletonRows.ItemsSource = skeletonData;
        }

        /// <summary>
        /// إخفاء/إظهار Stats Cards
        /// </summary>
        public void ShowStatsCards(bool show)
        {
            // يمكن إضافة منطق لإخفاء/إظهار بطاقات الإحصائيات
        }
    }
}
