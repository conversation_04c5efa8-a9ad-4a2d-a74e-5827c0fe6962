@model MedicalCenterSystem.Models.MedicalService

@{
    ViewData["Title"] = "إضافة خدمة طبية جديدة";
}

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="mb-3">
                        <label asp-for="ServiceName" class="form-label"></label>
                        <input asp-for="ServiceName" class="form-control" placeholder="أدخل اسم الخدمة (مثل: كشفية، فحص، مختبر، سونار)" />
                        <span asp-validation-for="ServiceName" class="text-danger"></span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="ServiceType" class="form-label"></label>
                                <select asp-for="ServiceType" class="form-select">
                                    <option value="">-- اختر نوع الخدمة --</option>
                                    <option value="Direct">مباشر</option>
                                    <option value="Referral">تحويل</option>
                                </select>
                                <span asp-validation-for="ServiceType" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="DefaultPrice" class="form-label"></label>
                                <input asp-for="DefaultPrice" class="form-control" type="number" step="0.01" min="0" placeholder="0.00" />
                                <span asp-validation-for="DefaultPrice" class="text-danger"></span>
                                <div class="form-text">اتركه فارغاً إذا لم يكن هناك سعر افتراضي</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input asp-for="IsCenterService" class="form-check-input" type="checkbox" />
                            <label asp-for="IsCenterService" class="form-check-label">
                                خدمة تابعة للمركز
                            </label>
                        </div>
                        <span asp-validation-for="IsCenterService" class="text-danger"></span>
                        <div class="form-text">حدد هذا الخيار إذا كانت الخدمة تقدم داخل المركز</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الخدمة
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
