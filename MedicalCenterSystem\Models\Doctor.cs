using System.ComponentModel.DataAnnotations;

namespace MedicalCenterSystem.Models
{
    public class Doctor
    {
        public int DoctorId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "اسم الطبيب")]
        public string FullName { get; set; } = string.Empty;

        [StringLength(100)]
        [Display(Name = "التخصص")]
        public string Specialty { get; set; } = string.Empty;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual ICollection<DoctorService> DoctorServices { get; set; } = new List<DoctorService>();
        public virtual ICollection<PatientVisit> PatientVisits { get; set; } = new List<PatientVisit>();
    }
}
