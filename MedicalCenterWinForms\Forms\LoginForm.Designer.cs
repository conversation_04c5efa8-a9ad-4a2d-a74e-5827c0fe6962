using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Forms
{
    partial class LoginForm
    {
        private System.ComponentModel.IContainer components = null;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnSettings;
        private Button btnExit;
        private Label lblTitle;
        private Label lblUsername;
        private Label lblPassword;
        private Panel pnlMain;
        private Panel pnlHeader;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.txtUsername = new TextBox();
            this.txtPassword = new TextBox();
            this.btnLogin = new Button();
            this.btnSettings = new Button();
            this.btnExit = new Button();
            this.lblTitle = new Label();
            this.lblUsername = new Label();
            this.lblPassword = new Label();
            this.pnlMain = new Panel();
            this.pnlHeader = new Panel();
            this.pnlMain.SuspendLayout();
            this.pnlHeader.SuspendLayout();
            this.SuspendLayout();

            //
            // pnlHeader
            //
            this.pnlHeader.BackColor = MaterialDesignHelper.Colors.Primary;
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Location = new Point(0, 0);
            this.pnlHeader.Name = "pnlHeader";
            this.pnlHeader.Size = new Size(450, 100);
            this.pnlHeader.TabIndex = 0;

            // Add flat border to header
            this.pnlHeader.Paint += (sender, e) =>
            {
                var rect = this.pnlHeader.ClientRectangle;
                using var borderPen = new Pen(MaterialDesignHelper.Colors.Divider, 1);
                e.Graphics.DrawLine(borderPen, 0, rect.Height - 1, rect.Width, rect.Height - 1);
            };

            //
            // lblTitle
            //
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = ArabicFontHelper.GetArabicTitleFont(18F);
            this.lblTitle.ForeColor = MaterialDesignHelper.Colors.TextOnPrimary;
            this.lblTitle.Location = new Point(80, 35);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(290, 30);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "نظام إدارة المركز الطبي";
            this.lblTitle.TextAlign = ContentAlignment.MiddleCenter;

            // 
            // pnlMain
            // 
            this.pnlMain.BackColor = MaterialDesignHelper.Colors.Background;
            this.pnlMain.Controls.Add(this.lblUsername);
            this.pnlMain.Controls.Add(this.txtUsername);
            this.pnlMain.Controls.Add(this.lblPassword);
            this.pnlMain.Controls.Add(this.txtPassword);
            this.pnlMain.Controls.Add(this.btnLogin);
            this.pnlMain.Controls.Add(this.btnSettings);
            this.pnlMain.Controls.Add(this.btnExit);
            this.pnlMain.Dock = DockStyle.Fill;
            this.pnlMain.Location = new Point(0, 80);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Padding = new Padding(40);
            this.pnlMain.Size = new Size(450, 270);
            this.pnlMain.TabIndex = 1;

            //
            // lblUsername
            //
            this.lblUsername.AutoSize = true;
            this.lblUsername.Font = new Font("Tahoma", 11F, FontStyle.Bold);
            this.lblUsername.Location = new Point(50, 50);
            this.lblUsername.Name = "lblUsername";
            this.lblUsername.Size = new Size(98, 18);
            this.lblUsername.TabIndex = 0;
            this.lblUsername.Text = "اسم المستخدم:";

            //
            // txtUsername
            //
            this.txtUsername.Font = new Font("Tahoma", 11F);
            this.txtUsername.Location = new Point(50, 75);
            this.txtUsername.Name = "txtUsername";
            this.txtUsername.Size = new Size(350, 25);
            this.txtUsername.TabIndex = 1;
            this.txtUsername.TextAlign = HorizontalAlignment.Right;

            //
            // lblPassword
            //
            this.lblPassword.AutoSize = true;
            this.lblPassword.Font = new Font("Tahoma", 11F, FontStyle.Bold);
            this.lblPassword.Location = new Point(50, 120);
            this.lblPassword.Name = "lblPassword";
            this.lblPassword.Size = new Size(79, 18);
            this.lblPassword.TabIndex = 2;
            this.lblPassword.Text = "كلمة المرور:";

            //
            // txtPassword
            //
            this.txtPassword.Font = new Font("Tahoma", 11F);
            this.txtPassword.Location = new Point(50, 145);
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.PasswordChar = '*';
            this.txtPassword.Size = new Size(350, 25);
            this.txtPassword.TabIndex = 3;
            this.txtPassword.TextAlign = HorizontalAlignment.Right;

            //
            // btnLogin
            //
            this.btnLogin.BackColor = MaterialDesignHelper.Colors.Primary;
            this.btnLogin.FlatStyle = FlatStyle.Flat;
            this.btnLogin.FlatAppearance.BorderSize = 0;
            this.btnLogin.Font = ArabicFontHelper.GetArabicButtonFont(12F);
            this.btnLogin.ForeColor = MaterialDesignHelper.Colors.TextOnPrimary;
            this.btnLogin.Location = new Point(50, 190);
            this.btnLogin.Name = "btnLogin";
            this.btnLogin.Size = new Size(350, 48);
            this.btnLogin.TabIndex = 4;
            this.btnLogin.Text = "تسجيل الدخول";
            this.btnLogin.UseVisualStyleBackColor = false;
            this.btnLogin.Cursor = Cursors.Hand;
            this.btnLogin.Click += new EventHandler(this.btnLogin_Click);

            //
            // btnSettings
            //
            this.btnSettings.BackColor = MaterialDesignHelper.Colors.Secondary;
            this.btnSettings.FlatStyle = FlatStyle.Flat;
            this.btnSettings.FlatAppearance.BorderSize = 0;
            this.btnSettings.Font = ArabicFontHelper.GetArabicButtonFont(10F);
            this.btnSettings.ForeColor = MaterialDesignHelper.Colors.TextOnPrimary;
            this.btnSettings.Location = new Point(220, 250);
            this.btnSettings.Name = "btnSettings";
            this.btnSettings.Size = new Size(100, 36);
            this.btnSettings.TabIndex = 5;
            this.btnSettings.Text = "إعدادات";
            this.btnSettings.UseVisualStyleBackColor = false;
            this.btnSettings.Cursor = Cursors.Hand;
            this.btnSettings.Click += new EventHandler(this.btnSettings_Click);

            //
            // btnExit
            //
            this.btnExit.BackColor = MaterialDesignHelper.Colors.Error;
            this.btnExit.FlatStyle = FlatStyle.Flat;
            this.btnExit.FlatAppearance.BorderSize = 0;
            this.btnExit.Font = ArabicFontHelper.GetArabicButtonFont(10F);
            this.btnExit.ForeColor = MaterialDesignHelper.Colors.TextOnPrimary;
            this.btnExit.Location = new Point(330, 250);
            this.btnExit.Name = "btnExit";
            this.btnExit.Size = new Size(70, 36);
            this.btnExit.TabIndex = 6;
            this.btnExit.Text = "خروج";
            this.btnExit.UseVisualStyleBackColor = false;
            this.btnExit.Cursor = Cursors.Hand;
            this.btnExit.Click += new EventHandler(this.btnExit_Click);

            // 
            // LoginForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(450, 350);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlHeader);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "LoginForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "تسجيل الدخول - نظام إدارة المركز الطبي";
            this.Load += new EventHandler(this.LoginForm_Load);
            this.pnlMain.ResumeLayout(false);
            this.pnlMain.PerformLayout();
            this.pnlHeader.ResumeLayout(false);
            this.pnlHeader.PerformLayout();
            this.ResumeLayout(false);
        }
    }
}
