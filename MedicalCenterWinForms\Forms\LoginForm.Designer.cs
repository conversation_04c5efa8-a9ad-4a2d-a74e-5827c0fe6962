#nullable disable
#pragma warning disable CS0649
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Animations;
using System.Drawing.Drawing2D;

namespace MedicalCenterWinForms.Forms
{
    partial class LoginForm
    {
        private System.ComponentModel.IContainer components = null;

        // Main Layout Components
        private Panel pnlBackground;
        private Panel pnlLeftSide;
        private Panel pnlRightSide;
        private Panel pnlLoginCard;

        // Header Components
        private Panel pnlHeader;
        private Label lblWelcome;
        private Label lblSubtitle;
        private PictureBox picLogo;

        // Form Components
        private Panel pnlUsernameContainer;
        private Panel pnlPasswordContainer;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Label lblUsername;
        private Label lblPassword;
        private Panel pnlUsernameIcon;
        private Panel pnlPasswordIcon;
        private Label lblUsernameIcon;
        private Label lblPasswordIcon;
        private CheckBox chkShowPassword;
        private CheckBox chkRememberMe;

        // Action Components
        private Button btnLogin;
        private Button btnForgotPassword;
        private Panel pnlButtons;
        private Panel pnlFooter;

        // Settings and Exit
        private Button btnSettings;
        private Button btnExit;

        // Loading and Animation
        private Panel pnlLoading;
        private ProgressBar progressLogin;
        private Label lblLoading;

        // Background Elements
        private Panel pnlBackgroundPattern;
        private System.Windows.Forms.Timer animationTimer;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();

            // Initialize all components
            InitializeMainComponents();
            InitializeBackgroundComponents();
            // InitializeLoginCardComponents(); // Implemented inline
            InitializeFormComponents();
            InitializeActionComponents();
            InitializeAnimationComponents();

            // Setup layout
            SetupMainLayout();
            SetupBackgroundLayout();
            SetupLoginCardLayout();
            SetupFormLayout();
            SetupActionLayout();

            // Apply modern styling
            ApplyModernStyling();
            SetupAnimations();

            this.SuspendLayout();
            this.ResumeLayout(false);
        }

        private void InitializeMainComponents()
        {
            // Main Background Panel
            this.pnlBackground = new Panel();
            this.pnlLeftSide = new Panel();
            this.pnlRightSide = new Panel();
            this.pnlLoginCard = new Panel();
            this.pnlBackgroundPattern = new Panel();
        }

        private void InitializeBackgroundComponents()
        {
            // Logo and branding
            this.picLogo = new PictureBox();
            this.lblWelcome = new Label();
            this.lblSubtitle = new Label();
        }

        private void InitializeFormComponents()
        {
            // Form input components
            this.pnlUsernameContainer = new Panel();
            this.pnlPasswordContainer = new Panel();
            this.txtUsername = new TextBox();
            this.txtPassword = new TextBox();
            this.lblUsername = new Label();
            this.lblPassword = new Label();
            this.pnlUsernameIcon = new Panel();
            this.pnlPasswordIcon = new Panel();
            this.lblUsernameIcon = new Label();
            this.lblPasswordIcon = new Label();
            this.chkShowPassword = new CheckBox();
            this.chkRememberMe = new CheckBox();
        }

        private void InitializeActionComponents()
        {
            // Action buttons and components
            this.btnLogin = new Button();
            this.btnForgotPassword = new Button();
            this.pnlButtons = new Panel();
            this.pnlFooter = new Panel();
            this.btnSettings = new Button();
            this.btnExit = new Button();
        }

        private void InitializeAnimationComponents()
        {
            // Loading and animation components
            this.pnlLoading = new Panel();
            this.progressLogin = new ProgressBar();
            this.lblLoading = new Label();
            this.animationTimer = new System.Windows.Forms.Timer(this.components);
        }

        private void SetupMainLayout()
        {
            // Main Form Setup
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Name = "LoginForm";
            this.Text = "تسجيل الدخول - نظام إدارة المركز الطبي";
            this.WindowState = FormWindowState.Normal;
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Load += new EventHandler(this.LoginForm_Load);

            // Background Panel
            this.pnlBackground.Dock = DockStyle.Fill;
            this.pnlBackground.BackColor = Color.Transparent;
            this.Controls.Add(this.pnlBackground);

            // Left Side Panel (Branding)
            this.pnlLeftSide.Dock = DockStyle.Left;
            this.pnlLeftSide.Width = 600;
            this.pnlLeftSide.BackColor = Color.Transparent;
            this.pnlBackground.Controls.Add(this.pnlLeftSide);

            // Right Side Panel (Login Form)
            this.pnlRightSide.Dock = DockStyle.Fill;
            this.pnlRightSide.BackColor = MaterialDesignHelper.Colors.Surface;
            this.pnlBackground.Controls.Add(this.pnlRightSide);
        }

        private void SetupBackgroundLayout()
        {
            // Background Pattern Panel
            this.pnlBackgroundPattern.Dock = DockStyle.Fill;
            this.pnlLeftSide.Controls.Add(this.pnlBackgroundPattern);

            // Logo Setup
            this.picLogo.Size = new Size(120, 120);
            this.picLogo.Location = new Point(240, 150);
            this.picLogo.BackColor = Color.Transparent;
            this.picLogo.SizeMode = PictureBoxSizeMode.Zoom;
            this.pnlLeftSide.Controls.Add(this.picLogo);

            // Welcome Label
            this.lblWelcome.Text = "مرحباً بك في";
            this.lblWelcome.Font = ArabicFontHelper.GetArabicTitleFont(28F, FontStyle.Bold);
            this.lblWelcome.ForeColor = Color.White;
            this.lblWelcome.AutoSize = true;
            this.lblWelcome.Location = new Point(200, 300);
            this.lblWelcome.BackColor = Color.Transparent;
            this.pnlLeftSide.Controls.Add(this.lblWelcome);

            // Subtitle Label
            this.lblSubtitle.Text = "نظام إدارة المركز الطبي المتطور";
            this.lblSubtitle.Font = ArabicFontHelper.GetArabicFont(18F);
            this.lblSubtitle.ForeColor = Color.FromArgb(220, 255, 255, 255);
            this.lblSubtitle.AutoSize = true;
            this.lblSubtitle.Location = new Point(150, 350);
            this.lblSubtitle.BackColor = Color.Transparent;
            this.pnlLeftSide.Controls.Add(this.lblSubtitle);
        }

        private void SetupLoginCardLayout()
        {
            // Login Card Panel
            this.pnlLoginCard.Size = new Size(450, 600);
            this.pnlLoginCard.Location = new Point(75, 100);
            this.pnlLoginCard.BackColor = Color.White;
            this.pnlLoginCard.Anchor = AnchorStyles.None;
            this.pnlRightSide.Controls.Add(this.pnlLoginCard);

            // Header Panel
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Height = 120;
            this.pnlHeader.BackColor = Color.Transparent;
            this.pnlLoginCard.Controls.Add(this.pnlHeader);
        }

        private void SetupFormLayout()
        {
            // Username Container
            this.pnlUsernameContainer.Location = new Point(50, 150);
            this.pnlUsernameContainer.Size = new Size(350, 60);
            this.pnlLoginCard.Controls.Add(this.pnlUsernameContainer);

            // Password Container
            this.pnlPasswordContainer.Location = new Point(50, 230);
            this.pnlPasswordContainer.Size = new Size(350, 60);
            this.pnlLoginCard.Controls.Add(this.pnlPasswordContainer);

            // Remember Me Checkbox
            this.chkRememberMe.Location = new Point(50, 310);
            this.chkRememberMe.Text = "تذكرني";
            this.chkRememberMe.Font = ArabicFontHelper.GetArabicFont(10F);
            this.chkRememberMe.RightToLeft = RightToLeft.Yes;
            this.pnlLoginCard.Controls.Add(this.chkRememberMe);
        }

        private void SetupActionLayout()
        {
            // Login Button
            this.btnLogin.Location = new Point(50, 360);
            this.btnLogin.Size = new Size(350, 55);
            this.btnLogin.Text = "تسجيل الدخول";
            this.btnLogin.Click += new EventHandler(this.btnLogin_Click);
            this.pnlLoginCard.Controls.Add(this.btnLogin);

            // Forgot Password Button
            this.btnForgotPassword.Location = new Point(50, 430);
            this.btnForgotPassword.Size = new Size(150, 35);
            this.btnForgotPassword.Text = "نسيت كلمة المرور؟";
            this.pnlLoginCard.Controls.Add(this.btnForgotPassword);

            // Settings and Exit buttons
            this.btnSettings.Location = new Point(220, 430);
            this.btnSettings.Size = new Size(80, 35);
            this.btnSettings.Text = "إعدادات";
            this.btnSettings.Click += new EventHandler(this.btnSettings_Click);
            this.pnlLoginCard.Controls.Add(this.btnSettings);

            this.btnExit.Location = new Point(320, 430);
            this.btnExit.Size = new Size(80, 35);
            this.btnExit.Text = "خروج";
            this.btnExit.Click += new EventHandler(this.btnExit_Click);
            this.pnlLoginCard.Controls.Add(this.btnExit);
        }

        private void ApplyModernStyling()
        {
            // Apply ultra-modern gradient background to left panel
            this.pnlBackgroundPattern.Paint += (sender, e) =>
            {
                var rect = this.pnlBackgroundPattern.ClientRectangle;
                using var gradientBrush = new LinearGradientBrush(
                    rect,
                    MaterialDesignHelper.Colors.GradientStart,
                    MaterialDesignHelper.Colors.GradientEnd,
                    LinearGradientMode.ForwardDiagonal);

                // Add color blend for more sophisticated gradient
                var colorBlend = new ColorBlend();
                colorBlend.Colors = new Color[] {
                    MaterialDesignHelper.Colors.GradientStart,
                    MaterialDesignHelper.Colors.GradientMiddle,
                    MaterialDesignHelper.Colors.GradientEnd
                };
                colorBlend.Positions = new float[] { 0.0f, 0.5f, 1.0f };
                gradientBrush.InterpolationColors = colorBlend;

                e.Graphics.FillRectangle(gradientBrush, rect);

                // Add subtle pattern overlay
                DrawModernPattern(e.Graphics, rect);
            };

            // Apply modern card styling to login panel
            this.pnlLoginCard.Paint += (sender, e) =>
            {
                var rect = this.pnlLoginCard.ClientRectangle;
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // Create rounded rectangle path
                using var cardPath = MaterialDesignHelper.CreateRoundedRectanglePath(rect, 20);

                // Draw shadow
                var shadowRect = new Rectangle(rect.X + 8, rect.Y + 8, rect.Width, rect.Height);
                using var shadowPath = MaterialDesignHelper.CreateRoundedRectanglePath(shadowRect, 20);
                using var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0));
                e.Graphics.FillPath(shadowBrush, shadowPath);

                // Draw card background
                using var cardBrush = new SolidBrush(Color.White);
                e.Graphics.FillPath(cardBrush, cardPath);

                // Draw subtle border
                using var borderPen = new Pen(MaterialDesignHelper.Colors.BorderLight, 1);
                e.Graphics.DrawPath(borderPen, cardPath);
            };

            // Style input containers
            StyleInputContainer(this.pnlUsernameContainer, this.txtUsername, this.lblUsername,
                               this.lblUsernameIcon, "👤", "اسم المستخدم");
            StyleInputContainer(this.pnlPasswordContainer, this.txtPassword, this.lblPassword,
                               this.lblPasswordIcon, "🔒", "كلمة المرور");

            // Style buttons with modern design
            StyleModernButton(this.btnLogin, MaterialDesignHelper.Colors.Primary, true);
            StyleModernButton(this.btnForgotPassword, Color.Transparent, false);
            StyleModernButton(this.btnSettings, MaterialDesignHelper.Colors.Secondary, false);
            StyleModernButton(this.btnExit, MaterialDesignHelper.Colors.Error, false);
        }

        private void SetupAnimations()
        {
            // Setup entrance animations
            this.pnlLoginCard.Location = new Point(75, 200); // Start lower

            // Animation timer setup
            this.animationTimer.Interval = 16; // ~60 FPS
            this.animationTimer.Tick += AnimationTimer_Tick;

            // Start entrance animation when form loads
            this.Load += (s, e) => StartEntranceAnimation();
        }

        private void DrawModernPattern(Graphics graphics, Rectangle rect)
        {
            // Draw subtle geometric pattern
            using var patternPen = new Pen(Color.FromArgb(20, 255, 255, 255), 1);

            // Draw diagonal lines pattern
            for (int i = 0; i < rect.Width + rect.Height; i += 60)
            {
                graphics.DrawLine(patternPen,
                    new Point(i, 0),
                    new Point(i - rect.Height, rect.Height));
            }

            // Draw circles pattern
            using var circlePen = new Pen(Color.FromArgb(10, 255, 255, 255), 2);
            for (int x = 100; x < rect.Width; x += 200)
            {
                for (int y = 100; y < rect.Height; y += 200)
                {
                    graphics.DrawEllipse(circlePen, x - 30, y - 30, 60, 60);
                }
            }
        }

        private void StyleInputContainer(Panel container, TextBox textBox, Label label,
                                       Label iconLabel, string icon, string labelText)
        {
            // Container styling
            container.BackColor = Color.Transparent;
            container.Paint += (sender, e) =>
            {
                var rect = container.ClientRectangle;
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                // Draw rounded background
                using var bgPath = MaterialDesignHelper.CreateRoundedRectanglePath(rect, 12);
                using var bgBrush = new SolidBrush(MaterialDesignHelper.Colors.SurfaceElevated);
                e.Graphics.FillPath(bgBrush, bgPath);

                // Draw border
                var borderColor = textBox.Focused ?
                    MaterialDesignHelper.Colors.Primary :
                    MaterialDesignHelper.Colors.Border;
                using var borderPen = new Pen(borderColor, textBox.Focused ? 2 : 1);
                e.Graphics.DrawPath(borderPen, bgPath);
            };

            // Icon setup
            iconLabel.Text = icon;
            iconLabel.Font = new Font("Segoe UI Emoji", 16F);
            iconLabel.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
            iconLabel.Size = new Size(40, 40);
            iconLabel.Location = new Point(10, 10);
            iconLabel.TextAlign = ContentAlignment.MiddleCenter;
            container.Controls.Add(iconLabel);

            // Label setup
            label.Text = labelText;
            label.Font = ArabicFontHelper.GetArabicFont(10F);
            label.ForeColor = MaterialDesignHelper.Colors.TextSecondary;
            label.Location = new Point(60, 5);
            label.AutoSize = true;
            container.Controls.Add(label);

            // TextBox setup
            textBox.BorderStyle = BorderStyle.None;
            textBox.Font = ArabicFontHelper.GetArabicFont(12F);
            textBox.Location = new Point(60, 25);
            textBox.Size = new Size(280, 25);
            textBox.BackColor = Color.Transparent;
            textBox.ForeColor = MaterialDesignHelper.Colors.TextPrimary;
            textBox.RightToLeft = RightToLeft.Yes;

            // Add focus effects
            textBox.Enter += (s, e) => container.Invalidate();
            textBox.Leave += (s, e) => container.Invalidate();

            container.Controls.Add(textBox);
        }

        private void StyleModernButton(Button button, Color backgroundColor, bool isPrimary)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.BackColor = backgroundColor;
            button.ForeColor = isPrimary ? Color.White : MaterialDesignHelper.Colors.TextPrimary;
            button.Font = ArabicFontHelper.GetArabicButtonFont(isPrimary ? 14F : 11F,
                         isPrimary ? FontStyle.Bold : FontStyle.Regular);
            button.Cursor = Cursors.Hand;
            button.UseVisualStyleBackColor = false;

            // Custom painting for modern look
            button.Paint += (sender, e) =>
            {
                var rect = button.ClientRectangle;
                e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

                if (isPrimary)
                {
                    // Primary button with gradient
                    using var gradientBrush = new LinearGradientBrush(
                        rect, backgroundColor,
                        MaterialDesignHelper.DarkenColor(backgroundColor, 0.1f),
                        LinearGradientMode.Vertical);
                    using var buttonPath = MaterialDesignHelper.CreateRoundedRectanglePath(rect, 12);
                    e.Graphics.FillPath(gradientBrush, buttonPath);
                }
                else if (backgroundColor == Color.Transparent)
                {
                    // Text button - no background
                }
                else
                {
                    // Secondary button
                    using var buttonBrush = new SolidBrush(backgroundColor);
                    using var buttonPath = MaterialDesignHelper.CreateRoundedRectanglePath(rect, 8);
                    e.Graphics.FillPath(buttonBrush, buttonPath);
                }

                // Draw text
                var textRect = new Rectangle(0, 0, rect.Width, rect.Height);
                TextRenderer.DrawText(e.Graphics, button.Text, button.Font, textRect,
                    button.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
            };

            // Hover effects
            var originalColor = backgroundColor;
            button.MouseEnter += (s, e) =>
            {
                button.BackColor = MaterialDesignHelper.LightenColor(originalColor, 0.1f);
            };
            button.MouseLeave += (s, e) =>
            {
                button.BackColor = originalColor;
            };
        }

        // Animation methods are implemented in the main form class
    }
}
