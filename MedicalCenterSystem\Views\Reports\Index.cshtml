@{
    ViewData["Title"] = "التقارير";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">@ViewData["Title"]</h2>
        </div>
    </div>

    <div class="row">
        <!-- Center Revenue Report -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> تقرير وارد المركز</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">تقرير شامل لوارد المركز من جميع الأقسام والخدمات</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> المدفوعات الرئيسية</li>
                        <li><i class="fas fa-check text-success"></i> التحويلات حسب القسم</li>
                        <li><i class="fas fa-check text-success"></i> إجمالي الوارد</li>
                        <li><i class="fas fa-check text-success"></i> توزيع الحصص</li>
                    </ul>
                    <a asp-action="CenterRevenue" class="btn btn-primary">
                        <i class="fas fa-eye"></i> عرض التقرير
                    </a>
                </div>
            </div>
        </div>

        <!-- Doctor Referrals Report -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-user-md"></i> تقرير تحويلات الأطباء</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">تقرير مفصل لتحويلات كل طبيب والخدمات المحولة</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> التحويلات حسب الطبيب</li>
                        <li><i class="fas fa-check text-success"></i> الخدمات المحولة</li>
                        <li><i class="fas fa-check text-success"></i> حصة الطبيب</li>
                        <li><i class="fas fa-check text-success"></i> إحصائيات مفصلة</li>
                    </ul>
                    <a asp-action="DoctorReferrals" class="btn btn-success">
                        <i class="fas fa-eye"></i> عرض التقرير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Daily Report -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-day"></i> التقرير اليومي</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">تقرير شامل لجميع أنشطة اليوم</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> المراجعين اليوميين</li>
                        <li><i class="fas fa-check text-success"></i> المدفوعات اليومية</li>
                        <li><i class="fas fa-check text-success"></i> التحويلات اليومية</li>
                        <li><i class="fas fa-check text-success"></i> ملخص الوارد</li>
                    </ul>
                    <a asp-action="DailyReport" class="btn btn-info">
                        <i class="fas fa-eye"></i> عرض التقرير
                    </a>
                </div>
            </div>
        </div>

        <!-- Patient Details Report -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-user"></i> تقرير تفصيلي للمراجع</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">تقرير مفصل لمراجع معين وجميع مدفوعاته</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> معلومات المراجع</li>
                        <li><i class="fas fa-check text-success"></i> المدفوعات الرئيسية</li>
                        <li><i class="fas fa-check text-success"></i> التحويلات</li>
                        <li><i class="fas fa-check text-success"></i> إجمالي التكلفة</li>
                    </ul>
                    <a asp-action="PatientDetails" class="btn btn-warning">
                        <i class="fas fa-eye"></i> عرض التقرير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات التقارير</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>ميزات التقارير:</h6>
                            <ul>
                                <li>تقارير قابلة للتخصيص حسب التاريخ</li>
                                <li>إحصائيات مفصلة ومرئية</li>
                                <li>تصدير البيانات (قريباً)</li>
                                <li>تقارير في الوقت الفعلي</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>نصائح الاستخدام:</h6>
                            <ul>
                                <li>استخدم فلاتر التاريخ للحصول على نتائج دقيقة</li>
                                <li>راجع التقارير اليومية بانتظام</li>
                                <li>تابع تحويلات الأطباء لتحسين الأداء</li>
                                <li>استخدم تقرير المراجع للمتابعة الفردية</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
}
