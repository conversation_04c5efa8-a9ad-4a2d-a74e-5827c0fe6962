using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MedicalCenterSystem.Models
{
    public class MainPayment
    {
        public int MainPaymentId { get; set; }

        [Required]
        public int PatientVisitId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "أجرة الكشف")]
        public decimal ConsultationFee { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "أجرة الفحص")]
        public decimal ExamFee { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "حصة الطبيب")]
        public decimal DoctorShare { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "حصة المركز")]
        public decimal CenterShare { get; set; }

        [StringLength(100)]
        [Display(Name = "اسم الكاشير")]
        public string CashierName { get; set; } = string.Empty;

        [Display(Name = "ملاحظات")]
        public string Notes { get; set; } = string.Empty;

        [Display(Name = "تاريخ الدفع")]
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("PatientVisitId")]
        public virtual PatientVisit PatientVisit { get; set; } = null!;
    }
}
