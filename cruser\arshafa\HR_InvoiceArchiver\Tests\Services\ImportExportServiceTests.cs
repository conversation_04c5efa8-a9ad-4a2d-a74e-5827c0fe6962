using Xunit;
using FluentAssertions;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Data;
using HR_InvoiceArchiver.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.IO;
using System.Threading.Tasks;
using Moq;

namespace HR_InvoiceArchiver.Tests.Services
{
    public class ImportExportServiceTests : IDisposable
    {
        private readonly DatabaseContext _context;
        private readonly ImportExportService _importExportService;
        private readonly Mock<ILoggingService> _mockLoggingService;
        private readonly Mock<ISecurityService> _mockSecurityService;
        private readonly string _testDirectory;

        public ImportExportServiceTests()
        {
            // إعداد قاعدة بيانات في الذاكرة للاختبار
            var options = new DbContextOptionsBuilder<DatabaseContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new DatabaseContext(options);
            _mockLoggingService = new Mock<ILoggingService>();
            _mockSecurityService = new Mock<ISecurityService>();

            // إعداد مجلد اختبار مؤقت
            _testDirectory = Path.Combine(Path.GetTempPath(), "HR_InvoiceArchiver_ImportExport_Tests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);

            _importExportService = new ImportExportService(
                _context,
                _mockLoggingService.Object,
                _mockSecurityService.Object);

            // إضافة بيانات تجريبية
            SeedTestData();
        }

        private void SeedTestData()
        {
            // إضافة موردين تجريبيين
            var suppliers = new[]
            {
                new HR_InvoiceArchiver.Models.Supplier { Name = "مورد 1", Email = "<EMAIL>", IsActive = true },
                new HR_InvoiceArchiver.Models.Supplier { Name = "مورد 2", Email = "<EMAIL>", IsActive = true }
            };
            _context.Suppliers.AddRange(suppliers);

            // إضافة فواتير تجريبية
            var invoices = new[]
            {
                new HR_InvoiceArchiver.Models.Invoice 
                { 
                    InvoiceNumber = "INV001", 
                    InvoiceDate = DateTime.Now.AddDays(-10),
                    Amount = 1000,
                    SupplierId = 1,
                    Status = HR_InvoiceArchiver.Models.InvoiceStatus.Paid
                },
                new HR_InvoiceArchiver.Models.Invoice 
                { 
                    InvoiceNumber = "INV002", 
                    InvoiceDate = DateTime.Now.AddDays(-5),
                    Amount = 2000,
                    SupplierId = 2,
                    Status = HR_InvoiceArchiver.Models.InvoiceStatus.Pending
                }
            };
            _context.Invoices.AddRange(invoices);

            // إضافة مدفوعات تجريبية
            var payments = new[]
            {
                new HR_InvoiceArchiver.Models.Payment
                {
                    InvoiceId = 1,
                    Amount = 1000,
                    PaymentDate = DateTime.Now.AddDays(-8),
                    Method = PaymentMethod.Cash
                }
            };
            _context.Payments.AddRange(payments);

            _context.SaveChanges();
        }

        [Fact]
        public async Task ExportInvoicesAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var options = new ExportOptions
            {
                Format = ExportFormat.Excel,
                FilePath = Path.Combine(_testDirectory, "invoices_test.xlsx")
            };

            // Act
            var result = await _importExportService.ExportInvoicesAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.RecordsExported.Should().BeGreaterThan(0);
            result.FilePath.Should().NotBeNullOrEmpty();
            result.Duration.Should().BeGreaterThan(TimeSpan.Zero);
        }

        [Fact]
        public async Task ExportSuppliersAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var options = new ExportOptions
            {
                Format = ExportFormat.CSV,
                FilePath = Path.Combine(_testDirectory, "suppliers_test.csv")
            };

            // Act
            var result = await _importExportService.ExportSuppliersAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.RecordsExported.Should().BeGreaterThan(0);
            result.FilePath.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task ExportPaymentsAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var options = new ExportOptions
            {
                Format = ExportFormat.JSON,
                FilePath = Path.Combine(_testDirectory, "payments_test.json")
            };

            // Act
            var result = await _importExportService.ExportPaymentsAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.RecordsExported.Should().BeGreaterThan(0);
            result.FilePath.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task ExportAllDataAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var options = new ExportOptions
            {
                Format = ExportFormat.JSON,
                FilePath = Path.Combine(_testDirectory, "all_data_test.json")
            };

            // Act
            var result = await _importExportService.ExportAllDataAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.RecordsExported.Should().BeGreaterThan(0);
            result.FilePath.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task ImportInvoicesAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var testFilePath = Path.Combine(_testDirectory, "test_import.xlsx");
            await File.WriteAllTextAsync(testFilePath, "Test content"); // محاكاة ملف

            var options = new ImportOptions
            {
                FilePath = testFilePath,
                Format = ImportFormat.Excel,
                Mode = ImportMode.Insert
            };

            // Act
            var result = await _importExportService.ImportInvoicesAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Duration.Should().BeGreaterThan(TimeSpan.Zero);
        }

        [Fact]
        public async Task ImportSuppliersAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var testFilePath = Path.Combine(_testDirectory, "test_suppliers.csv");
            await File.WriteAllTextAsync(testFilePath, "Test content");

            var options = new ImportOptions
            {
                FilePath = testFilePath,
                Format = ImportFormat.CSV,
                Mode = ImportMode.Upsert
            };

            // Act
            var result = await _importExportService.ImportSuppliersAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
        }

        [Fact]
        public async Task ImportPaymentsAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var testFilePath = Path.Combine(_testDirectory, "test_payments.json");
            await File.WriteAllTextAsync(testFilePath, "{}");

            var options = new ImportOptions
            {
                FilePath = testFilePath,
                Format = ImportFormat.JSON,
                Mode = ImportMode.Insert
            };

            // Act
            var result = await _importExportService.ImportPaymentsAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
        }

        [Fact]
        public async Task ImportAllDataAsync_WithValidOptions_ShouldReturnSuccessResult()
        {
            // Arrange
            var testFilePath = Path.Combine(_testDirectory, "test_all_data.json");
            await File.WriteAllTextAsync(testFilePath, "{}");

            var options = new ImportOptions
            {
                FilePath = testFilePath,
                Format = ImportFormat.JSON,
                Mode = ImportMode.Upsert
            };

            // Act
            var result = await _importExportService.ImportAllDataAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.RecordsImported.Should().BeGreaterOrEqualTo(0);
        }

        [Fact]
        public async Task ValidateImportFileAsync_WithValidFile_ShouldReturnValidResult()
        {
            // Arrange
            var testFilePath = Path.Combine(_testDirectory, "valid_file.xlsx");
            await File.WriteAllTextAsync(testFilePath, "Test content");

            // Act
            var result = await _importExportService.ValidateImportFileAsync(testFilePath, ImportFormat.Excel);

            // Assert
            result.Should().NotBeNull();
            result.IsValid.Should().BeTrue();
            result.TotalRows.Should().BeGreaterThan(0);
            result.DetectedColumns.Should().NotBeEmpty();
        }

        [Fact]
        public async Task ValidateImportFileAsync_WithNonExistentFile_ShouldReturnInvalidResult()
        {
            // Arrange
            var nonExistentPath = Path.Combine(_testDirectory, "non_existent.xlsx");

            // Act
            var result = await _importExportService.ValidateImportFileAsync(nonExistentPath, ImportFormat.Excel);

            // Assert
            result.Should().NotBeNull();
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain("الملف غير موجود");
        }

        [Fact]
        public async Task PreviewImportDataAsync_WithValidFile_ShouldReturnPreviewData()
        {
            // Arrange
            var testFilePath = Path.Combine(_testDirectory, "preview_file.csv");
            await File.WriteAllTextAsync(testFilePath, "Column1,Column2\nValue1,Value2");

            // Act
            var result = await _importExportService.PreviewImportDataAsync(testFilePath, ImportFormat.CSV, 5);

            // Assert
            result.Should().NotBeNull();
            result.Columns.Should().NotBeEmpty();
            result.SampleData.Should().NotBeEmpty();
            result.TotalRows.Should().BeGreaterThan(0);
            result.Validation.Should().NotBeNull();
        }

        [Fact]
        public async Task CreateImportTemplateAsync_WithValidParameters_ShouldReturnTemplatePath()
        {
            // Act
            var templatePath = await _importExportService.CreateImportTemplateAsync(DataType.Invoices, ExportFormat.Excel);

            // Assert
            templatePath.Should().NotBeNullOrEmpty();
            File.Exists(templatePath).Should().BeTrue();
        }

        [Fact]
        public async Task GetImportExportHistoryAsync_ShouldReturnHistoryList()
        {
            // Act
            var history = await _importExportService.GetImportExportHistoryAsync(30);

            // Assert
            history.Should().NotBeNull();
            history.Should().BeOfType<System.Collections.Generic.List<ImportExportHistory>>();
        }

        [Fact]
        public async Task CleanupOldExportFilesAsync_ShouldReturnDeletedCount()
        {
            // Arrange
            // إنشاء ملف قديم للاختبار
            var oldFilePath = Path.Combine(_testDirectory, "old_file.xlsx");
            await File.WriteAllTextAsync(oldFilePath, "Old file content");
            
            // تعديل تاريخ الإنشاء ليكون قديماً
            File.SetCreationTime(oldFilePath, DateTime.Now.AddDays(-35));

            // Act
            var deletedCount = await _importExportService.CleanupOldExportFilesAsync(30);

            // Assert
            deletedCount.Should().BeGreaterOrEqualTo(0);
        }

        [Theory]
        [InlineData(ExportFormat.Excel)]
        [InlineData(ExportFormat.CSV)]
        [InlineData(ExportFormat.JSON)]
        [InlineData(ExportFormat.XML)]
        public async Task ExportInvoicesAsync_WithDifferentFormats_ShouldSucceed(ExportFormat format)
        {
            // Arrange
            var extension = format switch
            {
                ExportFormat.Excel => "xlsx",
                ExportFormat.CSV => "csv",
                ExportFormat.JSON => "json",
                ExportFormat.XML => "xml",
                _ => "txt"
            };

            var options = new ExportOptions
            {
                Format = format,
                FilePath = Path.Combine(_testDirectory, $"invoices_test.{extension}")
            };

            // Act
            var result = await _importExportService.ExportInvoicesAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Format.Should().Be(format);
        }

        [Fact]
        public async Task ExportInvoicesAsync_WithDateRange_ShouldFilterCorrectly()
        {
            // Arrange
            var options = new ExportOptions
            {
                Format = ExportFormat.JSON,
                FilePath = Path.Combine(_testDirectory, "filtered_invoices.json"),
                StartDate = DateTime.Now.AddDays(-15),
                EndDate = DateTime.Now.AddDays(-1)
            };

            // Act
            var result = await _importExportService.ExportInvoicesAsync(options);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            // يجب أن تكون النتيجة مفلترة حسب التاريخ
        }

        public void Dispose()
        {
            _context?.Dispose();
            
            // تنظيف مجلد الاختبار
            if (Directory.Exists(_testDirectory))
            {
                try
                {
                    Directory.Delete(_testDirectory, true);
                }
                catch
                {
                    // تجاهل أخطاء التنظيف
                }
            }
        }
    }
}
