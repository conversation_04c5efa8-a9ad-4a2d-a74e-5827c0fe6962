using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using LiveCharts;
using LiveCharts.Configurations;
using LiveCharts.Wpf;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    public partial class DashboardPage : UserControl, INavigationAware, INotifyPropertyChanged
    {
        private readonly IInvoiceService _invoiceService;
        private readonly INavigationService _navigationService;
        private readonly IToastService _toastService;
        private readonly ISupplierService _supplierService;
        private readonly IDashboardService _dashboardService;
        private DispatcherTimer? _refreshTimer;
        private DispatcherTimer? _animationTimer;

        // Chart properties
        public SeriesCollection SeriesCollection { get; set; } = new SeriesCollection();
        public string[] Labels { get; set; } = Array.Empty<string>();
        public Func<double, string> YFormatter { get; set; } = value => $"{value:N0} د.ع";

        // Dashboard state properties
        private bool _isLoading;
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
                // Ensure UI update happens on UI thread
                Dispatcher.BeginInvoke(new Action(UpdateLoadingState));
            }
        }

        private string _currentActionTab = "invoices";
        public string CurrentActionTab
        {
            get => _currentActionTab;
            set
            {
                _currentActionTab = value;
                OnPropertyChanged();
                // Ensure UI update happens on UI thread
                Dispatcher.BeginInvoke(new Action(UpdateActionTabVisibility));
            }
        }

        private ChartType _currentChartType = ChartType.Line;
        public ChartType CurrentChartType
        {
            get => _currentChartType;
            set
            {
                _currentChartType = value;
                OnPropertyChanged();
                // Ensure UI update happens on UI thread
                Dispatcher.BeginInvoke(new Action(UpdateChartType));
            }
        }

        private ChartPeriod _currentChartPeriod = ChartPeriod.SixMonths;
        public ChartPeriod CurrentChartPeriod
        {
            get => _currentChartPeriod;
            set
            {
                _currentChartPeriod = value;
                OnPropertyChanged();
                // Ensure async operation happens safely on UI thread
                Dispatcher.BeginInvoke(new Action(async () =>
                {
                    try
                    {
                        await RefreshChartDataAsync();
                    }
                    catch (Exception ex)
                    {
                        _toastService.ShowError("خطأ في الرسم البياني", ex.Message);
                    }
                }));
            }
        }

        // Data caching - محسن للأداء
        private IEnumerable<Invoice>? _cachedInvoices;
        private DashboardStatistics? _cachedStatistics;
        private DateTime _lastDataRefresh;
        private DateTime _lastStatisticsRefresh;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _statisticsCacheExpiry = TimeSpan.FromMinutes(2); // إحصائيات أسرع

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            // Ensure PropertyChanged events are raised on the UI thread
            if (Dispatcher.CheckAccess())
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
            else
            {
                Dispatcher.Invoke(() => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName)));
            }
        }

        public DashboardPage(
            IInvoiceService invoiceService,
            INavigationService navigationService,
            IToastService toastService,
            ISupplierService supplierService,
            IDashboardService dashboardService)
        {
            InitializeComponent();

            _invoiceService = invoiceService;
            _navigationService = navigationService;
            _toastService = toastService;
            _supplierService = supplierService;
            _dashboardService = dashboardService;

            // Initialize cached invoices
            _cachedInvoices = new List<Invoice>();

            // Initialize timers
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(5) // Auto-refresh every 5 minutes
            };
            _refreshTimer.Tick += RefreshTimer_Tick;

            _animationTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(50)
            };
            _animationTimer.Tick += AnimationTimer_Tick;

            // Initialize chart
            InitializeChart();

            // Initialize UI state
            InitializeUIState();

            // Set data context
            DataContext = this;

            Loaded += DashboardPage_Loaded;
            Unloaded += DashboardPage_Unloaded;
        }

        private void InitializeUIState()
        {
            // Ensure UI initialization happens on UI thread
            Dispatcher.BeginInvoke(new Action(() =>
            {
                // Set initial action tab
                CurrentActionTab = "invoices";

                // Initialize chart period combo box
                if (ChartPeriodComboBox != null)
                {
                    ChartPeriodComboBox.SelectedIndex = 0; // Default to 6 months
                }

                // Initialize filter panels as collapsed
                if (RecentInvoicesFilterPanel != null)
                {
                    RecentInvoicesFilterPanel.Visibility = Visibility.Collapsed;
                }
            }));
        }

        public void OnNavigatedTo(object parameter)
        {
            // Refresh data when navigating to dashboard - ensure it runs on UI thread
            Dispatcher.BeginInvoke(new Action(async () =>
            {
                try
                {
                    await LoadDashboardDataAsync();
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ", $"فشل في تحميل لوحة التحكم: {ex.Message}");
                }
            }));

            // Start auto-refresh timer
            _refreshTimer?.Start();
        }

        public void OnNavigatedFrom()
        {
            // Stop timers when leaving dashboard
            _refreshTimer?.Stop();
            _animationTimer?.Stop();
        }

        private void DashboardPage_Unloaded(object sender, RoutedEventArgs e)
        {
            // Cleanup resources - محسن لتجنب تسريب الذاكرة
            try
            {
                _refreshTimer?.Stop();
                _animationTimer?.Stop();

                // تنظيف Cache لتوفير الذاكرة
                _cachedInvoices = null;
                _cachedStatistics = null;

                // إلغاء الاشتراك في الأحداث
                if (_refreshTimer != null)
                {
                    _refreshTimer.Tick -= RefreshTimer_Tick;
                    _refreshTimer = null;
                }

                if (_animationTimer != null)
                {
                    _animationTimer.Tick -= AnimationTimer_Tick;
                    _animationTimer = null;
                }

                // تنظيف Chart resources
                SeriesCollection?.Clear();

                // إجبار Garbage Collection للذاكرة المحررة
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during cleanup: {ex.Message}");
            }
        }

        private async void RefreshTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                // تحسين: تحديث ذكي بناءً على حالة Cache
                var now = DateTime.Now;

                // تحديث الإحصائيات فقط إذا انتهت صلاحيتها (بطريقة آمنة)
                if (_cachedStatistics == null || now - _lastStatisticsRefresh > _statisticsCacheExpiry)
                {
                    try
                    {
                        _cachedStatistics = await _dashboardService.GetDashboardStatisticsAsync();
                        _lastStatisticsRefresh = now;

                        // تحديث UI على UI thread فقط
                        if (_cachedStatistics != null)
                        {
                            UpdateStatisticsSync(_cachedStatistics);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in statistics refresh: {ex.Message}");
                    }
                }

                // تحديث البيانات الكاملة فقط إذا انتهت صلاحية Cache الرئيسي
                if (now - _lastDataRefresh > _cacheExpiry)
                {
                    await LoadDashboardDataAsync(showLoadingIndicator: false);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in refresh timer: {ex.Message}");
                // لا نعرض خطأ للمستخدم في التحديث التلقائي
            }
        }

        private void AnimationTimer_Tick(object? sender, EventArgs e)
        {
            // Handle any continuous animations here
            // This can be used for progress bar animations, etc.
        }

        private async void DashboardPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadDashboardDataAsync(showLoadingIndicator: true);

                // Start welcome animation
                await PlayWelcomeAnimationAsync();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", $"فشل في تحميل لوحة التحكم: {ex.Message}");
            }
        }

        private async Task LoadDashboardDataAsync(bool showLoadingIndicator = true)
        {
            try
            {
                if (showLoadingIndicator)
                {
                    IsLoading = true;
                }

                // Check cache first - محسن للأداء
                if (_cachedInvoices != null && DateTime.Now - _lastDataRefresh < _cacheExpiry)
                {
                    // استخدام البيانات المخزنة مؤقتاً
                    await UpdateDashboardUI(_cachedInvoices);

                    // تحديث الإحصائيات فقط إذا انتهت صلاحيتها (بطريقة آمنة)
                    if (_cachedStatistics == null || DateTime.Now - _lastStatisticsRefresh >= _statisticsCacheExpiry)
                    {
                        try
                        {
                            _cachedStatistics = await _dashboardService.GetDashboardStatisticsAsync();
                            _lastStatisticsRefresh = DateTime.Now;

                            // تحديث UI على UI thread فقط
                            if (_cachedStatistics != null)
                            {
                                UpdateStatisticsSync(_cachedStatistics);
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error refreshing statistics: {ex.Message}");
                        }
                    }
                    return;
                }

                // Fetch fresh data using optimized method
                var invoices = await _invoiceService.GetAllInvoicesBasicAsync();
                _cachedInvoices = invoices;
                _lastDataRefresh = DateTime.Now;

                // إعادة تعيين cache الإحصائيات
                _cachedStatistics = null;

                await UpdateDashboardUI(invoices);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل البيانات", $"حدث خطأ: {ex.Message}");
            }
            finally
            {
                if (showLoadingIndicator)
                {
                    IsLoading = false;
                }
            }
        }

        private async Task UpdateDashboardUI(IEnumerable<Invoice> invoices)
        {
            // تحديث UI بطريقة آمنة - كل شيء على UI thread
            await Dispatcher.InvokeAsync(async () =>
            {
                try
                {
                    var invoicesList = invoices.ToList();

                    // Update basic statistics with animation
                    await UpdateStatisticsAsync(invoicesList);

                    // Update status distribution with progress bars
                    await UpdateStatusDistributionAsync(invoicesList);

                    // Update welcome message
                    await UpdateWelcomeMessageAsync(invoicesList);

                    // Load recent invoices
                    await LoadRecentInvoicesAsync(invoicesList);

                    // Load alerts with counts
                    await LoadAlertsAsync(invoicesList);

                    // Update chart
                    await LoadChartDataAsync(invoicesList);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error updating dashboard UI: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// تحديث الإحصائيات بطريقة متزامنة محسنة
        /// </summary>
        private void UpdateStatisticsSync(DashboardStatistics statistics)
        {
            try
            {
                // تحديث النصوص مباشرة بدون animation للسرعة
                if (TotalInvoicesText != null)
                    TotalInvoicesText.Text = statistics.TotalInvoices.ToString("N0");

                if (TotalAmountText != null)
                    TotalAmountText.Text = $"{statistics.TotalAmount:N0} د.ع";

                if (PaidAmountText != null)
                    PaidAmountText.Text = $"{statistics.PaidAmount:N0} د.ع";

                if (OutstandingAmountText != null)
                    OutstandingAmountText.Text = $"{statistics.OutstandingAmount:N0} د.ع";

                if (OverdueInvoicesText != null)
                    OverdueInvoicesText.Text = statistics.OverdueInvoices.ToString("N0");

                // تحديث Progress bars
                UpdateProgressBarsSync(statistics);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating statistics: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث شرائط التقدم بطريقة متزامنة
        /// </summary>
        private void UpdateProgressBarsSync(DashboardStatistics statistics)
        {
            try
            {
                if (TotalAmountProgress != null)
                    TotalAmountProgress.Value = statistics.TotalAmount > 0 ? 100.0 : 0;

                if (PaidAmountProgress != null)
                {
                    var paidProgress = statistics.TotalAmount > 0 ? (double)(statistics.PaidAmount / statistics.TotalAmount) * 100 : 0;
                    PaidAmountProgress.Value = paidProgress;
                }

                if (OutstandingAmountProgress != null)
                {
                    var outstandingProgress = statistics.TotalAmount > 0 ? (double)(statistics.OutstandingAmount / statistics.TotalAmount) * 100 : 0;
                    OutstandingAmountProgress.Value = outstandingProgress;
                }

                if (PaymentRateProgress != null)
                {
                    var paymentRate = statistics.TotalInvoices > 0 ? (double)statistics.PaidInvoices / statistics.TotalInvoices * 100 : 0;
                    PaymentRateProgress.Value = paymentRate;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating progress bars: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث توزيع الحالات بطريقة متزامنة
        /// </summary>
        private void UpdateStatusDistributionSync(List<Invoice> invoices)
        {
            try
            {
                // حساب التوزيع محلياً لتجنب استعلامات إضافية
                var paidCount = invoices.Count(i => i.Status == InvoiceStatus.Paid);
                var unpaidCount = invoices.Count(i => i.Status == InvoiceStatus.Unpaid);
                var partialCount = invoices.Count(i => i.Status == InvoiceStatus.PartiallyPaid);
                var overdueCount = invoices.Count(i => i.IsOverdue);

                // تحديث UI elements إذا كانت موجودة
                // يمكن إضافة المزيد من التحديثات هنا حسب الحاجة
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating status distribution: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث رسالة الترحيب بطريقة متزامنة
        /// </summary>
        private void UpdateWelcomeMessageSync(List<Invoice> invoices)
        {
            try
            {
                var overdueCount = invoices.Count(i => i.IsOverdue);
                var recentCount = invoices.Count(i => i.InvoiceDate >= DateTime.Now.AddDays(-7));

                // تحديث رسالة الترحيب بناءً على البيانات
                // يمكن إضافة المزيد من المنطق هنا
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating welcome message: {ex.Message}");
            }
        }

        private async Task UpdateStatisticsAsync(IEnumerable<Invoice> invoices)
        {
            try
            {
                // Use the enhanced dashboard service for statistics
                var statistics = await _dashboardService.GetDashboardStatisticsAsync();

                // Animate statistics updates with enhanced data
                await AnimateStatisticUpdate(TotalInvoicesText, statistics.TotalInvoices.ToString("N0"));
                await AnimateStatisticUpdate(TotalAmountText, $"{statistics.TotalAmount:N0} د.ع");
                await AnimateStatisticUpdate(PaidAmountText, $"{statistics.PaidAmount:N0} د.ع");
                await AnimateStatisticUpdate(OutstandingAmountText, $"{statistics.OutstandingAmount:N0} د.ع");
                await AnimateStatisticUpdate(OverdueInvoicesText, statistics.OverdueInvoices.ToString("N0"));

                // Additional statistics can be added here when UI elements are available

                // Update progress bars with enhanced calculations
                if (TotalAmountProgress != null)
                {
                    var totalProgress = statistics.TotalAmount > 0 ? 100.0 : 0;
                    await AnimateProgressBar(TotalAmountProgress, totalProgress);
                }

                if (PaidAmountProgress != null)
                {
                    var paidProgress = statistics.TotalAmount > 0 ? (double)(statistics.PaidAmount / statistics.TotalAmount) * 100 : 0;
                    await AnimateProgressBar(PaidAmountProgress, paidProgress);
                }

                if (OutstandingAmountProgress != null)
                {
                    var outstandingProgress = statistics.TotalAmount > 0 ? (double)(statistics.OutstandingAmount / statistics.TotalAmount) * 100 : 0;
                    await AnimateProgressBar(OutstandingAmountProgress, outstandingProgress);
                }

                // Update payment rate progress bar
                if (PaymentRateProgress != null)
                {
                    await AnimateProgressBar(PaymentRateProgress, (double)statistics.PaymentRate);
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحديث الإحصائيات", ex.Message);
            }
        }

        private async Task UpdateStatusDistributionAsync(IEnumerable<Invoice> invoices)
        {
            var totalInvoices = invoices.Count();
            var unpaidCount = invoices.Count(i => i.Status == InvoiceStatus.Unpaid);
            var partiallyPaidCount = invoices.Count(i => i.Status == InvoiceStatus.PartiallyPaid);
            var paidCount = invoices.Count(i => i.Status == InvoiceStatus.Paid);

            // Update counts with animation
            await AnimateStatisticUpdate(UnpaidCountText, unpaidCount.ToString());
            await AnimateStatisticUpdate(PartiallyPaidCountText, partiallyPaidCount.ToString());
            await AnimateStatisticUpdate(PaidCountText, paidCount.ToString());

            // Update progress bars for status distribution
            if (totalInvoices > 0)
            {
                var unpaidProgress = (double)unpaidCount / totalInvoices * 100;
                var partiallyPaidProgress = (double)partiallyPaidCount / totalInvoices * 100;
                var paidProgress = (double)paidCount / totalInvoices * 100;

                await AnimateProgressBar(UnpaidProgressBar, unpaidProgress);
                await AnimateProgressBar(PartiallyPaidProgressBar, partiallyPaidProgress);
                await AnimateProgressBar(PaidProgressBar, paidProgress);
            }

            // Calculate and update payment rate
            var paymentRate = totalInvoices > 0 ? (double)paidCount / totalInvoices * 100 : 0;
            await AnimateProgressBar(PaymentRateProgress, paymentRate);
            await AnimateStatisticUpdate(PaymentRateText, $"{paymentRate:F1}%");
        }

        private async Task UpdateWelcomeMessageAsync(IEnumerable<Invoice> invoices)
        {
            var totalInvoices = invoices.Count();
            var totalAmount = invoices.Sum(i => i.Amount);

            await Dispatcher.InvokeAsync(() =>
            {
                if (WelcomeSubtitle != null)
                {
                    WelcomeSubtitle.Text = $"لديك {totalInvoices} فاتورة بإجمالي {totalAmount:N0} دينار عراقي";
                }
            });

            // Quick stats can be updated here when UI elements are available
        }

        private void InitializeChart()
        {
            SeriesCollection = new SeriesCollection();
            YFormatter = value => $"{value:N0} د.ع";
        }

        // Animation Methods
        private async Task PlayWelcomeAnimationAsync()
        {
            // Welcome animation can be implemented when UI elements are available
            await Task.Delay(200);

            // Statistics cards animation can be implemented when UI elements are available
        }

        private async Task AnimateStatisticUpdate(TextBlock textBlock, string newValue)
        {
            if (textBlock == null) return;

            await Dispatcher.InvokeAsync(async () =>
            {
                var fadeOut = new DoubleAnimation(1, 0.3, TimeSpan.FromMilliseconds(200));
                textBlock.BeginAnimation(UIElement.OpacityProperty, fadeOut);

                await Task.Delay(200);

                textBlock.Text = newValue;

                var fadeIn = new DoubleAnimation(0.3, 1, TimeSpan.FromMilliseconds(300));
                textBlock.BeginAnimation(UIElement.OpacityProperty, fadeIn);
            });
        }

        private async Task AnimateProgressBar(ProgressBar progressBar, double newValue)
        {
            if (progressBar == null) return;

            await Dispatcher.InvokeAsync(async () =>
            {
                var animation = new DoubleAnimation(progressBar.Value, newValue, TimeSpan.FromMilliseconds(800))
                {
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                };

                progressBar.BeginAnimation(ProgressBar.ValueProperty, animation);
                await Task.Delay(100);
            });
        }

        private void UpdateLoadingState()
        {
            // Ensure UI updates happen on the UI thread
            Dispatcher.Invoke(() =>
            {
                if (LoadingOverlay != null)
                {
                    LoadingOverlay.Visibility = IsLoading ? Visibility.Visible : Visibility.Collapsed;
                }

                if (ChartLoadingIndicator != null)
                {
                    ChartLoadingIndicator.Visibility = IsLoading ? Visibility.Visible : Visibility.Collapsed;
                }
            });
        }

        private void UpdateActionTabVisibility()
        {
            // Ensure UI updates happen on the UI thread
            Dispatcher.Invoke(() =>
            {
                if (InvoiceActionsPanel != null)
                    InvoiceActionsPanel.Visibility = CurrentActionTab == "invoices" ? Visibility.Visible : Visibility.Collapsed;

                if (PaymentActionsPanel != null)
                    PaymentActionsPanel.Visibility = CurrentActionTab == "payments" ? Visibility.Visible : Visibility.Collapsed;

                if (SystemActionsPanel != null)
                    SystemActionsPanel.Visibility = CurrentActionTab == "system" ? Visibility.Visible : Visibility.Collapsed;
            });
        }

        private void UpdateChartType()
        {
            if (_cachedInvoices != null)
            {
                _ = LoadChartDataAsync(_cachedInvoices);
            }
        }

        private async Task RefreshChartDataAsync()
        {
            if (_cachedInvoices != null)
            {
                await LoadChartDataAsync(_cachedInvoices);
            }
        }

        private async Task LoadChartDataAsync(IEnumerable<Invoice> invoices)
        {
            try
            {
                // Use the enhanced dashboard service for chart data
                var monthlyTrends = await _dashboardService.GetMonthlyTrendsAsync(GetMonthsFromChartPeriod());

                // Ensure chart updates happen on the UI thread
                await Dispatcher.InvokeAsync(async () =>
                {
                    SeriesCollection.Clear();

                    switch (CurrentChartType)
                    {
                        case ChartType.Line:
                            await LoadLineChartAsync(monthlyTrends);
                            break;
                        case ChartType.Column:
                            await LoadColumnChartAsync(monthlyTrends);
                            break;
                        case ChartType.Area:
                            await LoadAreaChartAsync(monthlyTrends);
                            break;
                        default:
                            await LoadLineChartAsync(monthlyTrends);
                            break;
                    }
                });

                Labels = monthlyTrends.Select(m => $"{m.Month} {m.Year}").ToArray();

                // Update chart binding if chart control exists
                if (MonthlyChart != null)
                {
                    MonthlyChart.Series = SeriesCollection;
                    if (MonthlyChart.AxisX?.Count > 0)
                        MonthlyChart.AxisX[0].Labels = Labels;
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل بيانات المخطط", ex.Message);
            }
        }

        private Task LoadLineChartAsync(IEnumerable<MonthlyTrend> trends)
        {
            var totalAmountSeries = new LineSeries
            {
                Title = "إجمالي الفواتير",
                Values = new ChartValues<double>(trends.Select(m => (double)m.TotalAmount)),
                PointGeometry = DefaultGeometries.Circle,
                PointGeometrySize = 8,
                Stroke = new SolidColorBrush(Color.FromRgb(103, 58, 183)),
                Fill = Brushes.Transparent
            };

            var paidAmountSeries = new LineSeries
            {
                Title = "المبالغ المسددة",
                Values = new ChartValues<double>(trends.Select(m => (double)m.PaidAmount)),
                PointGeometry = DefaultGeometries.Square,
                PointGeometrySize = 8,
                Stroke = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                Fill = Brushes.Transparent
            };

            SeriesCollection.Add(totalAmountSeries);
            SeriesCollection.Add(paidAmountSeries);
            return Task.CompletedTask;
        }

        private Task LoadColumnChartAsync(IEnumerable<MonthlyTrend> trends)
        {
            var totalAmountSeries = new ColumnSeries
            {
                Title = "إجمالي الفواتير",
                Values = new ChartValues<double>(trends.Select(m => (double)m.TotalAmount)),
                Fill = new SolidColorBrush(Color.FromArgb(180, 103, 58, 183))
            };

            var paidAmountSeries = new ColumnSeries
            {
                Title = "المبالغ المسددة",
                Values = new ChartValues<double>(trends.Select(m => (double)m.PaidAmount)),
                Fill = new SolidColorBrush(Color.FromArgb(180, 76, 175, 80))
            };

            SeriesCollection.Add(totalAmountSeries);
            SeriesCollection.Add(paidAmountSeries);
            return Task.CompletedTask;
        }

        private Task LoadAreaChartAsync(IEnumerable<MonthlyTrend> trends)
        {
            var totalAmountSeries = new StackedAreaSeries
            {
                Title = "إجمالي الفواتير",
                Values = new ChartValues<double>(trends.Select(m => (double)m.TotalAmount)),
                Fill = new SolidColorBrush(Color.FromArgb(120, 103, 58, 183)),
                Stroke = new SolidColorBrush(Color.FromRgb(103, 58, 183))
            };

            var paidAmountSeries = new StackedAreaSeries
            {
                Title = "المبالغ المسددة",
                Values = new ChartValues<double>(trends.Select(m => (double)m.PaidAmount)),
                Fill = new SolidColorBrush(Color.FromArgb(120, 76, 175, 80)),
                Stroke = new SolidColorBrush(Color.FromRgb(76, 175, 80))
            };

            SeriesCollection.Add(totalAmountSeries);
            SeriesCollection.Add(paidAmountSeries);
            return Task.CompletedTask;
        }

        private int GetMonthsFromChartPeriod()
        {
            return CurrentChartPeriod switch
            {
                ChartPeriod.SixMonths => 6,
                ChartPeriod.TwelveMonths => 12,
                ChartPeriod.ThisYear => DateTime.Now.Month,
                ChartPeriod.LastYear => 12,
                _ => 6
            };
        }

        private async Task LoadRecentInvoicesAsync(IEnumerable<Invoice> invoices)
        {
            try
            {
                var recentInvoices = invoices
                    .OrderByDescending(i => i.InvoiceDate)
                    .Take(5)
                    .Select(i => new
                    {
                        i.InvoiceNumber,
                        i.Supplier,
                        i.InvoiceDate,
                        i.Amount,
                        i.Status,
                        StatusText = GetStatusText(i.Status),
                        StatusColor = GetStatusColor(i.Status)
                    })
                    .ToList();

                // Ensure UI update happens on the UI thread
                await Dispatcher.InvokeAsync(() =>
                {
                    if (RecentInvoicesItemsControl != null)
                    {
                        RecentInvoicesItemsControl.ItemsSource = recentInvoices;
                    }
                });
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل الفواتير الحديثة", ex.Message);
            }
        }

        private async Task LoadAlertsAsync(IEnumerable<Invoice> invoices)
        {
            try
            {
                // Use the enhanced dashboard service for alerts
                var alerts = await _dashboardService.GetDashboardAlertsAsync();

                // Ensure UI updates happen on the UI thread
                await Dispatcher.InvokeAsync(() =>
                {
                    if (AlertsStackPanel != null)
                    {
                        AlertsStackPanel.Children.Clear();

                        // Add alerts to UI
                        foreach (var alert in alerts.Take(5))
                        {
                            AlertsStackPanel.Children.Add(CreateAlertControl(alert));
                        }

                        // Add "no alerts" message if empty
                        if (!alerts.Any())
                        {
                            var noAlertsControl = new StackPanel
                            {
                                HorizontalAlignment = HorizontalAlignment.Center,
                                VerticalAlignment = VerticalAlignment.Center,
                                Margin = new Thickness(0, 20, 0, 0)
                            };

                            var icon = new MaterialDesignThemes.Wpf.PackIcon
                            {
                                Kind = MaterialDesignThemes.Wpf.PackIconKind.CheckAll,
                                Width = 32,
                                Height = 32,
                                HorizontalAlignment = HorizontalAlignment.Center,
                                Foreground = new SolidColorBrush(Colors.Green)
                            };

                            var text = new TextBlock
                            {
                                Text = "لا توجد تنبيهات",
                                HorizontalAlignment = HorizontalAlignment.Center,
                                Margin = new Thickness(0, 8, 0, 0),
                                Style = (Style)FindResource("MaterialDesignBody1TextBlock")
                            };

                            noAlertsControl.Children.Add(icon);
                            noAlertsControl.Children.Add(text);
                            AlertsStackPanel.Children.Add(noAlertsControl);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل التنبيهات", ex.Message);
            }
        }

        private Border CreateAlertControl(HR_InvoiceArchiver.Services.AlertItem alert)
        {
            var border = new Border
            {
                Background = GetAlertBackground(alert.Type),
                CornerRadius = new CornerRadius(4),
                Margin = new Thickness(0, 0, 0, 8),
                Padding = new Thickness(12)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            var icon = new MaterialDesignThemes.Wpf.PackIcon
            {
                Kind = GetAlertIcon(alert.Icon),
                Width = 20,
                Height = 20,
                VerticalAlignment = VerticalAlignment.Top,
                Foreground = GetAlertForeground(alert.Type),
                Margin = new Thickness(0, 0, 8, 0)
            };
            Grid.SetColumn(icon, 0);

            var stackPanel = new StackPanel();
            Grid.SetColumn(stackPanel, 1);

            var titleText = new TextBlock
            {
                Text = alert.Title,
                FontWeight = FontWeights.Medium,
                Foreground = GetAlertForeground(alert.Type)
            };

            var messageText = new TextBlock
            {
                Text = alert.Message,
                FontSize = 12,
                Opacity = 0.8,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 2, 0, 0),
                Foreground = GetAlertForeground(alert.Type)
            };

            stackPanel.Children.Add(titleText);
            stackPanel.Children.Add(messageText);

            grid.Children.Add(icon);
            grid.Children.Add(stackPanel);
            border.Child = grid;

            return border;
        }

        // Enhanced Navigation Event Handlers
        private void AddInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(InvoicesPage), "add");
        }

        private void ViewInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(InvoicesPage), "");
        }

        private void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(PaymentsPage), "add");
        }

        private void AddMultiPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(PaymentsPage), "multi");
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(SearchPage), "");
        }

        private void SuppliersButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(SuppliersPage), "");
        }

        private void ReportsButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("التقارير", "جاري فتح صفحة التقارير...");
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("الإعدادات", "جاري فتح صفحة الإعدادات...");
        }

        private async void BackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _toastService.ShowInfo("نسخ احتياطي", "جاري إنشاء النسخة الاحتياطية...");

                // TODO: Implement backup functionality
                await Task.Delay(2000); // Simulate backup process

                _toastService.ShowSuccess("نسخ احتياطي", "تم إنشاء النسخة الاحتياطية بنجاح");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في النسخ الاحتياطي", $"حدث خطأ: {ex.Message}");
            }
        }

        private void ViewAllInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(InvoicesPage), "");
        }

        // New Event Handlers for Enhanced Features
        private void ActionTab_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string tabName)
            {
                CurrentActionTab = tabName;

                // Update tab button styles
                UpdateActionTabStyles(button);
            }
        }

        private void UpdateActionTabStyles(Button activeButton)
        {
            Dispatcher.Invoke(() =>
            {
                var tabs = new[] { InvoiceActionsTab, PaymentActionsTab, SystemActionsTab };
                foreach (var tab in tabs)
                {
                    if (tab != null)
                    {
                        if (tab == activeButton)
                        {
                            tab.Background = new SolidColorBrush(Color.FromArgb(30, 103, 58, 183));
                            tab.Foreground = new SolidColorBrush(Color.FromRgb(103, 58, 183));
                        }
                        else
                        {
                            tab.Background = Brushes.Transparent;
                            tab.Foreground = new SolidColorBrush(Colors.Gray);
                        }
                    }
                }
            });
        }

        private void ChartTypeButton_Click(object sender, RoutedEventArgs e)
        {
            // Cycle through chart types
            CurrentChartType = CurrentChartType switch
            {
                ChartType.Line => ChartType.Column,
                ChartType.Column => ChartType.Area,
                ChartType.Area => ChartType.Line,
                _ => ChartType.Line
            };

            _toastService.ShowInfo("نوع المخطط", $"تم تغيير نوع المخطط إلى {GetChartTypeDisplayName(CurrentChartType)}");
        }

        private void ExportChartButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Implement chart export functionality
                _toastService.ShowInfo("تصدير المخطط", "جاري تصدير المخطط...");
                _toastService.ShowSuccess("تصدير المخطط", "تم تصدير المخطط بنجاح");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التصدير", $"حدث خطأ: {ex.Message}");
            }
        }

        private async void ChartPeriodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedIndex >= 0)
            {
                CurrentChartPeriod = (ChartPeriod)comboBox.SelectedIndex;
                await RefreshChartDataAsync();
            }
        }

        private async void ChartOptions_Changed(object sender, RoutedEventArgs e)
        {
            await RefreshChartDataAsync();
        }

        private void FilterRecentButton_Click(object sender, RoutedEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                if (RecentInvoicesFilterPanel != null)
                {
                    RecentInvoicesFilterPanel.Visibility =
                        RecentInvoicesFilterPanel.Visibility == Visibility.Visible
                            ? Visibility.Collapsed
                            : Visibility.Visible;
                }
            });
        }

        private async void RecentStatusFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (_cachedInvoices != null)
            {
                await LoadRecentInvoicesAsync(_cachedInvoices);
            }
        }

        private async void RecentPeriodFilter_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (_cachedInvoices != null)
            {
                await LoadRecentInvoicesAsync(_cachedInvoices);
            }
        }

        private void RecentInvoiceItem_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (sender is Border border && border.DataContext != null)
            {
                // TODO: Navigate to invoice details
                _toastService.ShowInfo("فاتورة", "جاري فتح تفاصيل الفاتورة...");
            }
        }

        private void AlertSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("إعدادات التنبيهات", "جاري فتح إعدادات التنبيهات...");
            // TODO: Open alert settings dialog
        }

        private async void ClearAlertsButton_Click(object sender, RoutedEventArgs e)
        {
            await Dispatcher.InvokeAsync(async () =>
            {
                if (AlertsStackPanel != null)
                {
                    AlertsStackPanel.Children.Clear();
                    _toastService.ShowSuccess("التنبيهات", "تم مسح جميع التنبيهات");

                    // Reload alerts
                    if (_cachedInvoices != null)
                    {
                        await LoadAlertsAsync(_cachedInvoices);
                    }
                }
            });
        }

        // Additional Action Handlers
        private void ViewPaymentsButton_Click(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo(typeof(PaymentsPage), "");
        }

        private void PaymentReportsButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("تقارير المدفوعات", "جاري فتح تقارير المدفوعات...");
        }

        private void ReconciliationButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("تسوية الحسابات", "جاري فتح صفحة تسوية الحسابات...");
            // TODO: Navigate to reconciliation page
        }

        private void PaymentRemindersButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("تذكيرات الدفع", "جاري فتح إدارة تذكيرات الدفع...");
            // TODO: Navigate to payment reminders page
        }

        private void ExportPaymentsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _toastService.ShowInfo("تصدير المدفوعات", "جاري تصدير بيانات المدفوعات...");
                // TODO: Implement payments export functionality
                _toastService.ShowSuccess("تصدير المدفوعات", "تم تصدير بيانات المدفوعات بنجاح");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التصدير", $"حدث خطأ: {ex.Message}");
            }
        }

        private void ImportInvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _toastService.ShowInfo("استيراد الفواتير", "جاري فتح معالج استيراد الفواتير...");
                // TODO: Open import wizard
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في الاستيراد", $"حدث خطأ: {ex.Message}");
            }
        }

        private void RestoreButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _toastService.ShowInfo("استعادة البيانات", "جاري فتح معالج استعادة البيانات...");
                // TODO: Open restore wizard
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في الاستعادة", $"حدث خطأ: {ex.Message}");
            }
        }

        private void UserManagementButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("إدارة المستخدمين", "جاري فتح صفحة إدارة المستخدمين...");
            // TODO: Navigate to user management page
        }

        private void SystemLogsButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("سجلات النظام", "جاري فتح سجلات النظام...");
            // TODO: Navigate to system logs page
        }

        private void AboutButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("حول البرنامج", "جاري فتح معلومات البرنامج...");
            // TODO: Show about dialog
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDashboardDataAsync(showLoadingIndicator: true);
            _toastService.ShowSuccess("تحديث", "تم تحديث بيانات لوحة التحكم");
        }

        // Helper Methods
        private string GetStatusText(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Unpaid => "غير مسددة",
                InvoiceStatus.PartiallyPaid => "تسديد جزئي",
                InvoiceStatus.Paid => "مسددة",
                _ => "غير محدد"
            };
        }

        private string GetChartTypeDisplayName(ChartType chartType)
        {
            return chartType switch
            {
                ChartType.Line => "خطي",
                ChartType.Column => "أعمدة",
                ChartType.Area => "منطقة",
                _ => "خطي"
            };
        }

        private Brush GetStatusColor(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Unpaid => new SolidColorBrush(Color.FromRgb(244, 67, 54)), // Red
                InvoiceStatus.PartiallyPaid => new SolidColorBrush(Color.FromRgb(255, 152, 0)), // Orange
                InvoiceStatus.Paid => new SolidColorBrush(Color.FromRgb(76, 175, 80)), // Green
                _ => new SolidColorBrush(Colors.Gray)
            };
        }

        private Brush GetAlertBackground(HR_InvoiceArchiver.Services.AlertType type)
        {
            return type switch
            {
                HR_InvoiceArchiver.Services.AlertType.Success => new SolidColorBrush(Color.FromArgb(20, 76, 175, 80)),
                HR_InvoiceArchiver.Services.AlertType.Warning => new SolidColorBrush(Color.FromArgb(20, 255, 152, 0)),
                HR_InvoiceArchiver.Services.AlertType.Error => new SolidColorBrush(Color.FromArgb(20, 244, 67, 54)),
                HR_InvoiceArchiver.Services.AlertType.Info => new SolidColorBrush(Color.FromArgb(20, 33, 150, 243)),
                _ => new SolidColorBrush(Color.FromArgb(20, 158, 158, 158))
            };
        }

        private Brush GetAlertForeground(HR_InvoiceArchiver.Services.AlertType type)
        {
            return type switch
            {
                HR_InvoiceArchiver.Services.AlertType.Success => new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                HR_InvoiceArchiver.Services.AlertType.Warning => new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                HR_InvoiceArchiver.Services.AlertType.Error => new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                HR_InvoiceArchiver.Services.AlertType.Info => new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                _ => new SolidColorBrush(Colors.Gray)
            };
        }

        private MaterialDesignThemes.Wpf.PackIconKind GetAlertIcon(string iconName)
        {
            return iconName switch
            {
                "CheckCircle" => MaterialDesignThemes.Wpf.PackIconKind.CheckCircle,
                "AlertCircle" => MaterialDesignThemes.Wpf.PackIconKind.AlertCircle,
                "ClockAlert" => MaterialDesignThemes.Wpf.PackIconKind.ClockAlert,
                "Information" => MaterialDesignThemes.Wpf.PackIconKind.Information,
                _ => MaterialDesignThemes.Wpf.PackIconKind.Information
            };
        }

        // Event Handlers for Statistics Cards
        private void TotalInvoicesCard_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _navigationService.NavigateTo(typeof(InvoicesPage), "جميع الفواتير");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التنقل", ex.Message);
            }
        }

        private void TotalAmountCard_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _navigationService.NavigateTo(typeof(InvoicesPage), "إجمالي المبالغ");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التنقل", ex.Message);
            }
        }

        private void PaidAmountCard_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _navigationService.NavigateTo(typeof(InvoicesPage), "المبالغ المسددة");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التنقل", ex.Message);
            }
        }

        private void OutstandingAmountCard_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _navigationService.NavigateTo(typeof(InvoicesPage), "المبالغ المستحقة");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التنقل", ex.Message);
            }
        }

        private void OverdueInvoicesCard_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _navigationService.NavigateTo(typeof(InvoicesPage), "الفواتير المتأخرة");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التنقل", ex.Message);
            }
        }
    }

    // Helper Classes and Enums

    public enum ChartType
    {
        Line,
        Column,
        Area
    }

    public enum ChartPeriod
    {
        SixMonths = 0,
        TwelveMonths = 1,
        ThisYear = 2,
        LastYear = 3
    }

    public class MonthlyData
    {
        public string Month { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public int InvoiceCount { get; set; }
        public decimal OutstandingAmount => TotalAmount - PaidAmount;
        public double PaymentRate => TotalAmount > 0 ? (double)(PaidAmount / TotalAmount) * 100 : 0;
    }



    public class RecentInvoiceViewModel
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public string SupplierName { get; set; } = string.Empty;
        public DateTime InvoiceDate { get; set; }
        public decimal Amount { get; set; }
        public InvoiceStatus Status { get; set; }
        public string StatusText { get; set; } = string.Empty;
        public Brush StatusColor { get; set; } = Brushes.Gray;
        public bool IsOverdue { get; set; }
        public int DaysOverdue { get; set; }
    }
}
