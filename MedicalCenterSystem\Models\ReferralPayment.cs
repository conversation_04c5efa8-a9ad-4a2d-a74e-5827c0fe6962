using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MedicalCenterSystem.Models
{
    public class ReferralPayment
    {
        public int ReferralPaymentId { get; set; }

        [Required]
        public int PatientVisitId { get; set; }

        [Required]
        public int MedicalServiceId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ")]
        public decimal Amount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "حصة الطبيب")]
        public decimal? DoctorShare { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "حصة المركز")]
        public decimal CenterShare { get; set; }

        [StringLength(100)]
        [Display(Name = "القسم")]
        public string Section { get; set; } = string.Empty;

        [StringLength(100)]
        [Display(Name = "اسم الكاشير")]
        public string CashierName { get; set; } = string.Empty;

        [Display(Name = "تاريخ الدفع")]
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [Display(Name = "ملاحظات")]
        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        [ForeignKey("PatientVisitId")]
        public virtual PatientVisit PatientVisit { get; set; } = null!;

        [ForeignKey("MedicalServiceId")]
        public virtual MedicalService MedicalService { get; set; } = null!;
    }
}
