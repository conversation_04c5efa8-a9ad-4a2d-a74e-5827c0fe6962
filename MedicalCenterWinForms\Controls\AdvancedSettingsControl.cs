using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Threading.Tasks;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class AdvancedSettingsControl : BaseUserControl
    {
        private SettingsService _settingsService;

        // UI Components
        private TabControl mainTabControl;
        private Panel headerPanel;
        private Button btnSave;
        private Button btnReset;
        private Button btnExport;
        private Button btnImport;
        private Label lblLastSaved;

        // Tab Pages
        private TabPage tabGeneral;
        private TabPage tabFinancial;
        private TabPage tabSecurity;
        private TabPage tabNotifications;
        private TabPage tabBackup;
        private TabPage tabReports;
        private TabPage tabSystem;

        public AdvancedSettingsControl() : base()
        {
            InitializeComponent();
            InitializeServices();
            LoadSettings();
        }

        public AdvancedSettingsControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            InitializeServices();
            LoadSettings();
        }

        private void InitializeServices()
        {
            _settingsService = new SettingsService(DatabaseService);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Set control properties
            this.Size = new Size(1200, 800);
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Font = ArabicFontHelper.GetArabicFont(10F);
            this.RightToLeft = RightToLeft.Yes;

            CreateHeaderPanel();
            CreateMainTabControl();
            CreateTabPages();

            this.ResumeLayout(false);
        }

        private void CreateHeaderPanel()
        {
            headerPanel = new Panel
            {
                Location = new Point(10, 10),
                Size = new Size(1180, 60),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblTitle = new Label
            {
                Text = "⚙️ الإعدادات المتقدمة",
                Location = new Point(20, 15),
                Size = new Size(200, 30),
                Font = ArabicFontHelper.GetArabicFont(14F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.Primary
            };

            btnSave = ModernMedicalTheme.Components.CreateAdvancedButton(
                "💾 حفظ", ModernMedicalTheme.Components.ButtonStyle.Success);
            btnSave.Location = new Point(1000, 15);
            btnSave.Size = new Size(80, 30);
            btnSave.Click += BtnSave_Click;

            btnReset = ModernMedicalTheme.Components.CreateAdvancedButton(
                "🔄 إعادة تعيين", ModernMedicalTheme.Components.ButtonStyle.Secondary);
            btnReset.Location = new Point(910, 15);
            btnReset.Size = new Size(80, 30);
            btnReset.Click += BtnReset_Click;

            btnExport = ModernMedicalTheme.Components.CreateAdvancedButton(
                "📤 تصدير", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnExport.Location = new Point(820, 15);
            btnExport.Size = new Size(80, 30);
            btnExport.Click += BtnExport_Click;

            btnImport = ModernMedicalTheme.Components.CreateAdvancedButton(
                "📥 استيراد", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnImport.Location = new Point(730, 15);
            btnImport.Size = new Size(80, 30);
            btnImport.Click += BtnImport_Click;

            lblLastSaved = new Label
            {
                Text = "آخر حفظ: لم يتم الحفظ بعد",
                Location = new Point(400, 20),
                Size = new Size(200, 20),
                Font = ArabicFontHelper.GetArabicFont(8F),
                ForeColor = MaterialDesignHelper.Colors.TextSecondary
            };

            headerPanel.Controls.AddRange(new Control[]
            {
                lblTitle, btnSave, btnReset, btnExport, btnImport, lblLastSaved
            });

            this.Controls.Add(headerPanel);
        }

        private void CreateMainTabControl()
        {
            mainTabControl = new TabControl
            {
                Location = new Point(10, 80),
                Size = new Size(1180, 710),
                Font = ArabicFontHelper.GetArabicFont(10F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                Appearance = TabAppearance.FlatButtons,
                SizeMode = TabSizeMode.Fixed,
                ItemSize = new Size(120, 30)
            };

            // Apply modern styling
            mainTabControl.DrawMode = TabDrawMode.OwnerDrawFixed;
            mainTabControl.DrawItem += MainTabControl_DrawItem;

            this.Controls.Add(mainTabControl);
        }

        private void CreateTabPages()
        {
            // General Settings Tab
            tabGeneral = new TabPage("الإعدادات العامة");
            CreateGeneralSettingsTab();
            mainTabControl.TabPages.Add(tabGeneral);

            // Financial Settings Tab
            tabFinancial = new TabPage("الإعدادات المالية");
            CreateFinancialSettingsTab();
            mainTabControl.TabPages.Add(tabFinancial);

            // Security Settings Tab
            tabSecurity = new TabPage("إعدادات الأمان");
            CreateSecuritySettingsTab();
            mainTabControl.TabPages.Add(tabSecurity);

            // Notifications Settings Tab
            tabNotifications = new TabPage("إعدادات التنبيهات");
            CreateNotificationsSettingsTab();
            mainTabControl.TabPages.Add(tabNotifications);

            // Backup Settings Tab
            tabBackup = new TabPage("إعدادات النسخ الاحتياطي");
            CreateBackupSettingsTab();
            mainTabControl.TabPages.Add(tabBackup);

            // Reports Settings Tab
            tabReports = new TabPage("إعدادات التقارير");
            CreateReportsSettingsTab();
            mainTabControl.TabPages.Add(tabReports);

            // System Settings Tab
            tabSystem = new TabPage("إعدادات النظام");
            CreateSystemSettingsTab();
            mainTabControl.TabPages.Add(tabSystem);
        }

        private void CreateGeneralSettingsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Center Name
            var lblCenterName = new Label
            {
                Text = "اسم المركز الطبي:",
                Location = new Point(20, 20),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var txtCenterName = ModernMedicalTheme.Components.CreateAdvancedTextBox("اسم المركز");
            txtCenterName.Location = new Point(150, 20);
            txtCenterName.Size = new Size(300, 25);
            txtCenterName.Name = "CenterName";

            // Center Address
            var lblCenterAddress = new Label
            {
                Text = "عنوان المركز:",
                Location = new Point(20, 60),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var txtCenterAddress = ModernMedicalTheme.Components.CreateAdvancedTextBox("العنوان");
            txtCenterAddress.Location = new Point(150, 60);
            txtCenterAddress.Size = new Size(400, 25);
            txtCenterAddress.Name = "CenterAddress";

            // Phone Number
            var lblPhone = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(20, 100),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var txtPhone = ModernMedicalTheme.Components.CreateAdvancedTextBox("رقم الهاتف");
            txtPhone.Location = new Point(150, 100);
            txtPhone.Size = new Size(200, 25);
            txtPhone.Name = "Phone";

            // Email
            var lblEmail = new Label
            {
                Text = "البريد الإلكتروني:",
                Location = new Point(20, 140),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var txtEmail = ModernMedicalTheme.Components.CreateAdvancedTextBox("البريد الإلكتروني");
            txtEmail.Location = new Point(150, 140);
            txtEmail.Size = new Size(300, 25);
            txtEmail.Name = "Email";

            // Working Hours
            var lblWorkingHours = new Label
            {
                Text = "ساعات العمل:",
                Location = new Point(20, 180),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var txtWorkingHours = ModernMedicalTheme.Components.CreateAdvancedTextBox("من 8:00 ص إلى 10:00 م");
            txtWorkingHours.Location = new Point(150, 180);
            txtWorkingHours.Size = new Size(300, 25);
            txtWorkingHours.Name = "WorkingHours";

            // Language Settings
            var lblLanguage = new Label
            {
                Text = "لغة النظام:",
                Location = new Point(20, 220),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var cmbLanguage = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbLanguage.Location = new Point(150, 220);
            cmbLanguage.Size = new Size(150, 25);
            cmbLanguage.Items.AddRange(new object[] { "العربية", "English" });
            cmbLanguage.SelectedIndex = 0;
            cmbLanguage.Name = "Language";

            // Theme Settings
            var lblTheme = new Label
            {
                Text = "نمط الواجهة:",
                Location = new Point(20, 260),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var cmbTheme = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbTheme.Location = new Point(150, 260);
            cmbTheme.Size = new Size(150, 25);
            cmbTheme.Items.AddRange(new object[] { "فاتح", "داكن", "تلقائي" });
            cmbTheme.SelectedIndex = 0;
            cmbTheme.Name = "Theme";

            // Auto Save
            var chkAutoSave = new CheckBox
            {
                Text = "حفظ تلقائي كل 5 دقائق",
                Location = new Point(20, 300),
                Size = new Size(200, 25),
                Font = ArabicFontHelper.GetArabicFont(10F),
                Checked = true,
                Name = "AutoSave"
            };

            // Show Tooltips
            var chkShowTooltips = new CheckBox
            {
                Text = "إظهار التلميحات",
                Location = new Point(20, 330),
                Size = new Size(200, 25),
                Font = ArabicFontHelper.GetArabicFont(10F),
                Checked = true,
                Name = "ShowTooltips"
            };

            panel.Controls.AddRange(new Control[]
            {
                lblCenterName, txtCenterName, lblCenterAddress, txtCenterAddress,
                lblPhone, txtPhone, lblEmail, txtEmail, lblWorkingHours, txtWorkingHours,
                lblLanguage, cmbLanguage, lblTheme, cmbTheme, chkAutoSave, chkShowTooltips
            });

            tabGeneral.Controls.Add(panel);
        }

        private void CreateFinancialSettingsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Currency Settings
            var lblCurrency = new Label
            {
                Text = "العملة الافتراضية:",
                Location = new Point(20, 20),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var cmbCurrency = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbCurrency.Location = new Point(150, 20);
            cmbCurrency.Size = new Size(150, 25);
            cmbCurrency.Items.AddRange(new object[] { "ريال سعودي", "دولار أمريكي", "يورو" });
            cmbCurrency.SelectedIndex = 0;
            cmbCurrency.Name = "Currency";

            // Tax Settings
            var lblTaxRate = new Label
            {
                Text = "معدل الضريبة (%):",
                Location = new Point(20, 60),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var numTaxRate = new NumericUpDown
            {
                Location = new Point(150, 60),
                Size = new Size(100, 25),
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 2,
                Value = 15,
                Name = "TaxRate"
            };

            // Payment Methods
            var lblPaymentMethods = new Label
            {
                Text = "طرق الدفع المتاحة:",
                Location = new Point(20, 100),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var chkCash = new CheckBox
            {
                Text = "نقدي",
                Location = new Point(20, 130),
                Size = new Size(80, 25),
                Checked = true,
                Name = "PaymentCash"
            };

            var chkCard = new CheckBox
            {
                Text = "بطاقة ائتمان",
                Location = new Point(110, 130),
                Size = new Size(120, 25),
                Checked = true,
                Name = "PaymentCard"
            };

            var chkTransfer = new CheckBox
            {
                Text = "تحويل بنكي",
                Location = new Point(240, 130),
                Size = new Size(120, 25),
                Checked = true,
                Name = "PaymentTransfer"
            };

            panel.Controls.AddRange(new Control[]
            {
                lblCurrency, cmbCurrency, lblTaxRate, numTaxRate,
                lblPaymentMethods, chkCash, chkCard, chkTransfer
            });

            tabFinancial.Controls.Add(panel);
        }

        private void CreateSecuritySettingsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Password Policy
            var lblPasswordPolicy = new Label
            {
                Text = "سياسة كلمات المرور:",
                Location = new Point(20, 20),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var chkRequireUppercase = new CheckBox
            {
                Text = "يتطلب أحرف كبيرة",
                Location = new Point(20, 50),
                Size = new Size(200, 25),
                Checked = true,
                Name = "RequireUppercase"
            };

            var chkRequireNumbers = new CheckBox
            {
                Text = "يتطلب أرقام",
                Location = new Point(20, 80),
                Size = new Size(200, 25),
                Checked = true,
                Name = "RequireNumbers"
            };

            var chkRequireSpecialChars = new CheckBox
            {
                Text = "يتطلب رموز خاصة",
                Location = new Point(20, 110),
                Size = new Size(200, 25),
                Checked = false,
                Name = "RequireSpecialChars"
            };

            // Session Settings
            var lblSessionTimeout = new Label
            {
                Text = "انتهاء الجلسة (دقيقة):",
                Location = new Point(20, 150),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var numSessionTimeout = new NumericUpDown
            {
                Location = new Point(180, 150),
                Size = new Size(100, 25),
                Minimum = 5,
                Maximum = 480,
                Value = 30,
                Name = "SessionTimeout"
            };

            // Audit Settings
            var chkEnableAudit = new CheckBox
            {
                Text = "تفعيل سجل المراجعة",
                Location = new Point(20, 190),
                Size = new Size(200, 25),
                Checked = true,
                Name = "EnableAudit"
            };

            panel.Controls.AddRange(new Control[]
            {
                lblPasswordPolicy, chkRequireUppercase, chkRequireNumbers, chkRequireSpecialChars,
                lblSessionTimeout, numSessionTimeout, chkEnableAudit
            });

            tabSecurity.Controls.Add(panel);
        }

        private void CreateNotificationsSettingsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Email Notifications
            var lblEmailNotifications = new Label
            {
                Text = "تنبيهات البريد الإلكتروني:",
                Location = new Point(20, 20),
                Size = new Size(180, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var chkEmailPayments = new CheckBox
            {
                Text = "تنبيه عند استلام دفعة",
                Location = new Point(20, 50),
                Size = new Size(200, 25),
                Checked = true,
                Name = "EmailPayments"
            };

            var chkEmailReports = new CheckBox
            {
                Text = "تنبيه التقارير اليومية",
                Location = new Point(20, 80),
                Size = new Size(200, 25),
                Checked = false,
                Name = "EmailReports"
            };

            // System Notifications
            var lblSystemNotifications = new Label
            {
                Text = "تنبيهات النظام:",
                Location = new Point(20, 120),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var chkSystemAlerts = new CheckBox
            {
                Text = "تنبيهات النظام",
                Location = new Point(20, 150),
                Size = new Size(200, 25),
                Checked = true,
                Name = "SystemAlerts"
            };

            var chkSoundNotifications = new CheckBox
            {
                Text = "التنبيهات الصوتية",
                Location = new Point(20, 180),
                Size = new Size(200, 25),
                Checked = true,
                Name = "SoundNotifications"
            };

            panel.Controls.AddRange(new Control[]
            {
                lblEmailNotifications, chkEmailPayments, chkEmailReports,
                lblSystemNotifications, chkSystemAlerts, chkSoundNotifications
            });

            tabNotifications.Controls.Add(panel);
        }

        private void CreateBackupSettingsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Auto Backup
            var chkAutoBackup = new CheckBox
            {
                Text = "نسخ احتياطي تلقائي",
                Location = new Point(20, 20),
                Size = new Size(200, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold),
                Checked = true,
                Name = "AutoBackup"
            };

            // Backup Frequency
            var lblBackupFrequency = new Label
            {
                Text = "تكرار النسخ الاحتياطي:",
                Location = new Point(20, 60),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var cmbBackupFrequency = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbBackupFrequency.Location = new Point(180, 60);
            cmbBackupFrequency.Size = new Size(150, 25);
            cmbBackupFrequency.Items.AddRange(new object[] { "يومي", "أسبوعي", "شهري" });
            cmbBackupFrequency.SelectedIndex = 0;
            cmbBackupFrequency.Name = "BackupFrequency";

            // Backup Location
            var lblBackupLocation = new Label
            {
                Text = "مجلد النسخ الاحتياطي:",
                Location = new Point(20, 100),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var txtBackupLocation = ModernMedicalTheme.Components.CreateAdvancedTextBox("C:\\Backups\\MedicalCenter");
            txtBackupLocation.Location = new Point(180, 100);
            txtBackupLocation.Size = new Size(300, 25);
            txtBackupLocation.Name = "BackupLocation";

            var btnBrowseBackup = ModernMedicalTheme.Components.CreateAdvancedButton(
                "تصفح", ModernMedicalTheme.Components.ButtonStyle.Ghost);
            btnBrowseBackup.Location = new Point(490, 100);
            btnBrowseBackup.Size = new Size(60, 25);

            // Retention Policy
            var lblRetentionDays = new Label
            {
                Text = "الاحتفاظ بالنسخ (أيام):",
                Location = new Point(20, 140),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var numRetentionDays = new NumericUpDown
            {
                Location = new Point(180, 140),
                Size = new Size(100, 25),
                Minimum = 1,
                Maximum = 365,
                Value = 30,
                Name = "RetentionDays"
            };

            panel.Controls.AddRange(new Control[]
            {
                chkAutoBackup, lblBackupFrequency, cmbBackupFrequency,
                lblBackupLocation, txtBackupLocation, btnBrowseBackup,
                lblRetentionDays, numRetentionDays
            });

            tabBackup.Controls.Add(panel);
        }

        private void CreateReportsSettingsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Default Report Format
            var lblReportFormat = new Label
            {
                Text = "تنسيق التقرير الافتراضي:",
                Location = new Point(20, 20),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var cmbReportFormat = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbReportFormat.Location = new Point(180, 20);
            cmbReportFormat.Size = new Size(150, 25);
            cmbReportFormat.Items.AddRange(new object[] { "PDF", "Excel", "CSV", "Word" });
            cmbReportFormat.SelectedIndex = 0;
            cmbReportFormat.Name = "ReportFormat";

            // Auto Generate Reports
            var chkAutoGenerateReports = new CheckBox
            {
                Text = "إنشاء تقارير تلقائية",
                Location = new Point(20, 60),
                Size = new Size(200, 25),
                Checked = true,
                Name = "AutoGenerateReports"
            };

            // Report Schedule
            var lblReportSchedule = new Label
            {
                Text = "جدولة التقارير:",
                Location = new Point(20, 100),
                Size = new Size(120, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var cmbReportSchedule = ModernMedicalTheme.Components.CreateAdvancedComboBox();
            cmbReportSchedule.Location = new Point(150, 100);
            cmbReportSchedule.Size = new Size(150, 25);
            cmbReportSchedule.Items.AddRange(new object[] { "يومي", "أسبوعي", "شهري" });
            cmbReportSchedule.SelectedIndex = 2;
            cmbReportSchedule.Name = "ReportSchedule";

            panel.Controls.AddRange(new Control[]
            {
                lblReportFormat, cmbReportFormat, chkAutoGenerateReports,
                lblReportSchedule, cmbReportSchedule
            });

            tabReports.Controls.Add(panel);
        }

        private void CreateSystemSettingsTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Database Settings
            var lblDatabaseSettings = new Label
            {
                Text = "إعدادات قاعدة البيانات:",
                Location = new Point(20, 20),
                Size = new Size(180, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var btnTestConnection = ModernMedicalTheme.Components.CreateAdvancedButton(
                "اختبار الاتصال", ModernMedicalTheme.Components.ButtonStyle.Primary);
            btnTestConnection.Location = new Point(20, 50);
            btnTestConnection.Size = new Size(120, 30);
            btnTestConnection.Click += BtnTestConnection_Click;

            var btnOptimizeDatabase = ModernMedicalTheme.Components.CreateAdvancedButton(
                "تحسين قاعدة البيانات", ModernMedicalTheme.Components.ButtonStyle.Secondary);
            btnOptimizeDatabase.Location = new Point(150, 50);
            btnOptimizeDatabase.Size = new Size(140, 30);
            btnOptimizeDatabase.Click += BtnOptimizeDatabase_Click;

            // Performance Settings
            var lblPerformanceSettings = new Label
            {
                Text = "إعدادات الأداء:",
                Location = new Point(20, 100),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var chkEnableCaching = new CheckBox
            {
                Text = "تفعيل التخزين المؤقت",
                Location = new Point(20, 130),
                Size = new Size(200, 25),
                Checked = true,
                Name = "EnableCaching"
            };

            var chkEnableLogging = new CheckBox
            {
                Text = "تفعيل سجل الأحداث",
                Location = new Point(20, 160),
                Size = new Size(200, 25),
                Checked = true,
                Name = "EnableLogging"
            };

            // System Information
            var lblSystemInfo = new Label
            {
                Text = "معلومات النظام:",
                Location = new Point(20, 200),
                Size = new Size(150, 25),
                Font = ArabicFontHelper.GetArabicFont(10F, FontStyle.Bold)
            };

            var lblVersion = new Label
            {
                Text = "الإصدار: 1.0.0",
                Location = new Point(20, 230),
                Size = new Size(200, 20),
                Font = ArabicFontHelper.GetArabicFont(9F)
            };

            var lblLastUpdate = new Label
            {
                Text = "آخر تحديث: " + DateTime.Now.ToString("yyyy-MM-dd"),
                Location = new Point(20, 250),
                Size = new Size(200, 20),
                Font = ArabicFontHelper.GetArabicFont(9F)
            };

            panel.Controls.AddRange(new Control[]
            {
                lblDatabaseSettings, btnTestConnection, btnOptimizeDatabase,
                lblPerformanceSettings, chkEnableCaching, chkEnableLogging,
                lblSystemInfo, lblVersion, lblLastUpdate
            });

            tabSystem.Controls.Add(panel);
        }

        // Event Handlers
        private void MainTabControl_DrawItem(object sender, DrawItemEventArgs e)
        {
            var tabControl = sender as TabControl;
            var tabPage = tabControl.TabPages[e.Index];
            var tabRect = tabControl.GetTabRect(e.Index);

            // Background
            var backColor = e.Index == tabControl.SelectedIndex ?
                MaterialDesignHelper.Colors.Primary : Color.White;
            var textColor = e.Index == tabControl.SelectedIndex ?
                Color.White : MaterialDesignHelper.Colors.TextPrimary;

            using (var brush = new SolidBrush(backColor))
            {
                e.Graphics.FillRectangle(brush, tabRect);
            }

            // Text
            var textRect = new Rectangle(tabRect.X, tabRect.Y + 5, tabRect.Width, tabRect.Height - 5);
            TextRenderer.DrawText(e.Graphics, tabPage.Text, ArabicFontHelper.GetArabicFont(9F),
                textRect, textColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                SetLoadingState(true);
                await SaveAllSettings();
                lblLastSaved.Text = "آخر حفظ: " + DateTime.Now.ToString("HH:mm:ss");
                ShowSuccess("تم حفظ الإعدادات بنجاح");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async void BtnReset_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
                "تأكيد إعادة التعيين",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    SetLoadingState(true);
                    await ResetAllSettings();
                    await LoadSettings();
                    ShowSuccess("تم إعادة تعيين الإعدادات بنجاح");
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في إعادة تعيين الإعدادات: {ex.Message}");
                }
                finally
                {
                    SetLoadingState(false);
                }
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            MessageBox.Show("ميزة تصدير الإعدادات ستكون متاحة قريباً", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnImport_Click(object sender, EventArgs e)
        {
            MessageBox.Show("ميزة استيراد الإعدادات ستكون متاحة قريباً", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async void BtnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                SetLoadingState(true);
                var isConnected = await _settingsService.TestDatabaseConnection();
                if (isConnected)
                {
                    ShowSuccess("تم الاتصال بقاعدة البيانات بنجاح");
                }
                else
                {
                    ShowError("فشل في الاتصال بقاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في اختبار الاتصال: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private async void BtnOptimizeDatabase_Click(object sender, EventArgs e)
        {
            try
            {
                SetLoadingState(true);
                await _settingsService.OptimizeDatabase();
                ShowSuccess("تم تحسين قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحسين قاعدة البيانات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        // Helper Methods
        private async Task LoadSettings()
        {
            try
            {
                var settings = await _settingsService.GetAllSettings();
                ApplySettingsToControls(settings);
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الإعدادات: {ex.Message}");
            }
        }

        private async Task SaveAllSettings()
        {
            var settings = ExtractSettingsFromControls();
            await _settingsService.SaveAllSettings(settings);
        }

        private async Task ResetAllSettings()
        {
            await _settingsService.ResetToDefaults();
        }

        private void ApplySettingsToControls(Dictionary<string, object> settings)
        {
            foreach (var setting in settings)
            {
                var control = FindControlByName(setting.Key);
                if (control != null)
                {
                    SetControlValue(control, setting.Value);
                }
            }
        }

        private Dictionary<string, object> ExtractSettingsFromControls()
        {
            var settings = new Dictionary<string, object>();
            ExtractSettingsFromPanel(tabGeneral, settings);
            ExtractSettingsFromPanel(tabFinancial, settings);
            ExtractSettingsFromPanel(tabSecurity, settings);
            ExtractSettingsFromPanel(tabNotifications, settings);
            ExtractSettingsFromPanel(tabBackup, settings);
            ExtractSettingsFromPanel(tabReports, settings);
            ExtractSettingsFromPanel(tabSystem, settings);
            return settings;
        }

        private void ExtractSettingsFromPanel(Control parent, Dictionary<string, object> settings)
        {
            foreach (Control control in parent.Controls)
            {
                if (control.HasChildren)
                {
                    ExtractSettingsFromPanel(control, settings);
                }
                else if (!string.IsNullOrEmpty(control.Name))
                {
                    var value = GetControlValue(control);
                    if (value != null)
                    {
                        settings[control.Name] = value;
                    }
                }
            }
        }

        private Control FindControlByName(string name)
        {
            return FindControlByName(this, name);
        }

        private Control FindControlByName(Control parent, string name)
        {
            if (parent.Name == name) return parent;

            foreach (Control child in parent.Controls)
            {
                var found = FindControlByName(child, name);
                if (found != null) return found;
            }

            return null;
        }

        private object GetControlValue(Control control)
        {
            return control switch
            {
                TextBox textBox => textBox.Text,
                ComboBox comboBox => comboBox.SelectedItem?.ToString(),
                CheckBox checkBox => checkBox.Checked,
                NumericUpDown numericUpDown => numericUpDown.Value,
                _ => null
            };
        }

        private void SetControlValue(Control control, object value)
        {
            switch (control)
            {
                case TextBox textBox:
                    textBox.Text = value?.ToString() ?? "";
                    break;
                case ComboBox comboBox:
                    var item = value?.ToString();
                    if (!string.IsNullOrEmpty(item) && comboBox.Items.Contains(item))
                        comboBox.SelectedItem = item;
                    break;
                case CheckBox checkBox:
                    if (value is bool boolValue)
                        checkBox.Checked = boolValue;
                    break;
                case NumericUpDown numericUpDown:
                    if (value is decimal decimalValue)
                        numericUpDown.Value = Math.Max(numericUpDown.Minimum, Math.Min(numericUpDown.Maximum, decimalValue));
                    break;
            }
        }
    }
}
