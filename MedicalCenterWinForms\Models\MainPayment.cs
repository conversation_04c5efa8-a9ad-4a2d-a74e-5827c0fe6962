using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MedicalCenterWinForms.Models
{
    public class MainPayment
    {
        public int MainPaymentId { get; set; }

        [Required]
        public int PatientVisitId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ConsultationFee { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ExamFee { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DoctorShare { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CenterShare { get; set; }

        [StringLength(100)]
        public string CashierName { get; set; } = string.Empty;

        public string Notes { get; set; } = string.Empty;

        public DateTime PaymentDate { get; set; } = DateTime.Now;

        // Navigation Properties
        [ForeignKey("PatientVisitId")]
        public virtual PatientVisit PatientVisit { get; set; } = null!;
    }
}
