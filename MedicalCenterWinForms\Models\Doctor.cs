using System.ComponentModel.DataAnnotations;

namespace MedicalCenterWinForms.Models
{
    public class Doctor
    {
        public int DoctorId { get; set; }

        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        [StringLength(100)]
        public string Specialty { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual ICollection<DoctorService> DoctorServices { get; set; } = new List<DoctorService>();
        public virtual ICollection<PatientVisit> PatientVisits { get; set; } = new List<PatientVisit>();
    }
}
