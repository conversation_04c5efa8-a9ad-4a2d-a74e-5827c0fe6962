using System.Drawing;
using System.Drawing.Drawing2D;

namespace MedicalCenterWinForms.Animations
{
    /// <summary>
    /// Modern Animation System for Ultra Modern Medical UI
    /// Provides smooth animations and transitions for enhanced user experience
    /// </summary>
    public static class ModernAnimations
    {
        // Animation Configuration - Simplified
        public static class Config
        {
            public static int DefaultDuration { get; set; } = 200; // milliseconds - shorter for better performance
            public static EasingType DefaultEasing { get; set; } = EasingType.EaseOutCubic;
            public static bool EnableAnimations { get; set; } = false; // Disabled by default to avoid issues
        }

        // Easing Types for Natural Motion
        public enum EasingType
        {
            Linear,
            EaseInQuad,
            EaseOutQuad,
            EaseInOutQuad,
            EaseInCubic,
            EaseOutCubic,
            EaseInOutCubic,
            EaseInQuart,
            EaseOutQuart,
            EaseInOutQuart
        }

        // Animation Types
        public enum AnimationType
        {
            FadeIn,
            FadeOut,
            SlideInFromRight,
            SlideInFromLeft,
            SlideInFromTop,
            SlideIn<PERSON>romBottom,
            ScaleIn,
            ScaleOut,
            Bounce,
            Pulse
        }

        // Fade Animation
        public static async Task FadeIn(Control control, int duration = 300)
        {
            if (!Config.EnableAnimations)
            {
                control.Visible = true;
                return;
            }

            control.Visible = true;
            var startOpacity = 0.0f;
            var endOpacity = 1.0f;
            var startTime = DateTime.Now;

            while ((DateTime.Now - startTime).TotalMilliseconds < duration)
            {
                var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                var progress = Math.Min(1.0, elapsed / duration);
                var easedProgress = ApplyEasing(progress, EasingType.EaseOutCubic);
                
                var currentOpacity = startOpacity + (endOpacity - startOpacity) * easedProgress;
                SetControlOpacity(control, (float)currentOpacity);
                
                await Task.Delay(16); // ~60 FPS
            }

            SetControlOpacity(control, endOpacity);
        }

        public static async Task FadeOut(Control control, int duration = 300)
        {
            if (!Config.EnableAnimations)
            {
                control.Visible = false;
                return;
            }

            var startOpacity = 1.0f;
            var endOpacity = 0.0f;
            var startTime = DateTime.Now;

            while ((DateTime.Now - startTime).TotalMilliseconds < duration)
            {
                var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                var progress = Math.Min(1.0, elapsed / duration);
                var easedProgress = ApplyEasing(progress, EasingType.EaseOutCubic);
                
                var currentOpacity = startOpacity + (endOpacity - startOpacity) * easedProgress;
                SetControlOpacity(control, (float)currentOpacity);
                
                await Task.Delay(16);
            }

            SetControlOpacity(control, endOpacity);
            control.Visible = false;
        }

        // Slide Animations
        public static async Task SlideIn(Control control, AnimationType direction, int duration = 300)
        {
            if (!Config.EnableAnimations)
            {
                control.Visible = true;
                return;
            }

            control.Visible = true;
            var originalLocation = control.Location;
            Point startLocation;

            switch (direction)
            {
                case AnimationType.SlideInFromRight:
                    startLocation = new Point(control.Parent.Width, originalLocation.Y);
                    break;
                case AnimationType.SlideInFromLeft:
                    startLocation = new Point(-control.Width, originalLocation.Y);
                    break;
                case AnimationType.SlideInFromTop:
                    startLocation = new Point(originalLocation.X, -control.Height);
                    break;
                case AnimationType.SlideInFromBottom:
                    startLocation = new Point(originalLocation.X, control.Parent.Height);
                    break;
                default:
                    startLocation = originalLocation;
                    break;
            }

            control.Location = startLocation;
            var startTime = DateTime.Now;

            while ((DateTime.Now - startTime).TotalMilliseconds < duration)
            {
                var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                var progress = Math.Min(1.0, elapsed / duration);
                var easedProgress = ApplyEasing(progress, EasingType.EaseOutCubic);

                var currentX = (int)(startLocation.X + (originalLocation.X - startLocation.X) * easedProgress);
                var currentY = (int)(startLocation.Y + (originalLocation.Y - startLocation.Y) * easedProgress);
                
                control.Location = new Point(currentX, currentY);
                await Task.Delay(16);
            }

            control.Location = originalLocation;
        }

        // Scale Animation
        public static async Task ScaleIn(Control control, int duration = 300)
        {
            if (!Config.EnableAnimations)
            {
                control.Visible = true;
                return;
            }

            control.Visible = true;
            var originalSize = control.Size;
            var originalLocation = control.Location;
            var startScale = 0.0f;
            var endScale = 1.0f;
            var startTime = DateTime.Now;

            while ((DateTime.Now - startTime).TotalMilliseconds < duration)
            {
                var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                var progress = Math.Min(1.0, elapsed / duration);
                var easedProgress = ApplyEasing(progress, EasingType.EaseOutCubic);
                
                var currentScale = startScale + (endScale - startScale) * easedProgress;
                
                var newWidth = (int)(originalSize.Width * currentScale);
                var newHeight = (int)(originalSize.Height * currentScale);
                var newX = originalLocation.X + (originalSize.Width - newWidth) / 2;
                var newY = originalLocation.Y + (originalSize.Height - newHeight) / 2;
                
                control.Size = new Size(newWidth, newHeight);
                control.Location = new Point(newX, newY);
                
                await Task.Delay(16);
            }

            control.Size = originalSize;
            control.Location = originalLocation;
        }

        // Pulse Animation for Buttons
        public static async Task Pulse(Control control, int duration = 600)
        {
            if (!Config.EnableAnimations) return;

            var originalSize = control.Size;
            var pulseSize = new Size(
                (int)(originalSize.Width * 1.05f),
                (int)(originalSize.Height * 1.05f)
            );
            
            var originalLocation = control.Location;
            var pulseLocation = new Point(
                originalLocation.X - (pulseSize.Width - originalSize.Width) / 2,
                originalLocation.Y - (pulseSize.Height - originalSize.Height) / 2
            );

            var halfDuration = duration / 2;
            var startTime = DateTime.Now;

            // Scale up
            while ((DateTime.Now - startTime).TotalMilliseconds < halfDuration)
            {
                var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                var progress = Math.Min(1.0, elapsed / halfDuration);
                var easedProgress = ApplyEasing(progress, EasingType.EaseOutQuad);

                var currentWidth = (int)(originalSize.Width + (pulseSize.Width - originalSize.Width) * easedProgress);
                var currentHeight = (int)(originalSize.Height + (pulseSize.Height - originalSize.Height) * easedProgress);
                var currentX = (int)(originalLocation.X + (pulseLocation.X - originalLocation.X) * easedProgress);
                var currentY = (int)(originalLocation.Y + (pulseLocation.Y - originalLocation.Y) * easedProgress);

                control.Size = new Size(currentWidth, currentHeight);
                control.Location = new Point(currentX, currentY);
                
                await Task.Delay(16);
            }

            startTime = DateTime.Now;

            // Scale down
            while ((DateTime.Now - startTime).TotalMilliseconds < halfDuration)
            {
                var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                var progress = Math.Min(1.0, elapsed / halfDuration);
                var easedProgress = ApplyEasing(progress, EasingType.EaseOutQuad);

                var currentWidth = (int)(pulseSize.Width + (originalSize.Width - pulseSize.Width) * easedProgress);
                var currentHeight = (int)(pulseSize.Height + (originalSize.Height - pulseSize.Height) * easedProgress);
                var currentX = (int)(pulseLocation.X + (originalLocation.X - pulseLocation.X) * easedProgress);
                var currentY = (int)(pulseLocation.Y + (originalLocation.Y - pulseLocation.Y) * easedProgress);

                control.Size = new Size(currentWidth, currentHeight);
                control.Location = new Point(currentX, currentY);
                
                await Task.Delay(16);
            }

            control.Size = originalSize;
            control.Location = originalLocation;
        }

        // Bounce Animation
        public static async Task Bounce(Control control, int duration = 800)
        {
            if (!Config.EnableAnimations) return;

            var originalLocation = control.Location;
            var bounceHeight = 20;
            var startTime = DateTime.Now;

            while ((DateTime.Now - startTime).TotalMilliseconds < duration)
            {
                var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                var progress = elapsed / duration;
                
                // Create bounce effect using sine wave
                var bounceOffset = (int)(Math.Sin(progress * Math.PI * 4) * bounceHeight * (1 - progress));
                
                control.Location = new Point(originalLocation.X, originalLocation.Y - bounceOffset);
                await Task.Delay(16);
            }

            control.Location = originalLocation;
        }

        // Easing Functions
        private static double ApplyEasing(double progress, EasingType easingType)
        {
            return easingType switch
            {
                EasingType.Linear => progress,
                EasingType.EaseInQuad => progress * progress,
                EasingType.EaseOutQuad => 1 - (1 - progress) * (1 - progress),
                EasingType.EaseInOutQuad => progress < 0.5 ? 2 * progress * progress : 1 - Math.Pow(-2 * progress + 2, 2) / 2,
                EasingType.EaseInCubic => progress * progress * progress,
                EasingType.EaseOutCubic => 1 - Math.Pow(1 - progress, 3),
                EasingType.EaseInOutCubic => progress < 0.5 ? 4 * progress * progress * progress : 1 - Math.Pow(-2 * progress + 2, 3) / 2,
                EasingType.EaseInQuart => progress * progress * progress * progress,
                EasingType.EaseOutQuart => 1 - Math.Pow(1 - progress, 4),
                EasingType.EaseInOutQuart => progress < 0.5 ? 8 * progress * progress * progress * progress : 1 - Math.Pow(-2 * progress + 2, 4) / 2,
                _ => progress
            };
        }

        // Helper method to set control opacity - Simplified and safe
        private static void SetControlOpacity(Control control, float opacity)
        {
            if (control == null) return;

            try
            {
                if (opacity <= 0.1f)
                {
                    control.Visible = false;
                }
                else
                {
                    control.Visible = true;
                }
            }
            catch
            {
                // Ignore any errors to prevent crashes
            }
        }

        // Utility Methods
        public static async Task AnimateControl(Control control, AnimationType animationType, int duration = 300)
        {
            switch (animationType)
            {
                case AnimationType.FadeIn:
                    await FadeIn(control, duration);
                    break;
                case AnimationType.FadeOut:
                    await FadeOut(control, duration);
                    break;
                case AnimationType.SlideInFromRight:
                case AnimationType.SlideInFromLeft:
                case AnimationType.SlideInFromTop:
                case AnimationType.SlideInFromBottom:
                    await SlideIn(control, animationType, duration);
                    break;
                case AnimationType.ScaleIn:
                    await ScaleIn(control, duration);
                    break;
                case AnimationType.Bounce:
                    await Bounce(control, duration);
                    break;
                case AnimationType.Pulse:
                    await Pulse(control, duration);
                    break;
            }
        }

        // Stagger animations for multiple controls
        public static async Task StaggeredAnimation(IEnumerable<Control> controls, AnimationType animationType, int staggerDelay = 100)
        {
            var tasks = new List<Task>();
            var delay = 0;

            foreach (var control in controls)
            {
                var currentDelay = delay;
                tasks.Add(Task.Run(async () =>
                {
                    await Task.Delay(currentDelay);
                    await AnimateControl(control, animationType);
                }));
                delay += staggerDelay;
            }

            await Task.WhenAll(tasks);
        }
    }
}
