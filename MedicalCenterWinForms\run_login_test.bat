@echo off
echo 🚀 Starting Modern Medical Login Form Test...
echo.
echo 📋 System Information:
echo    - .NET Version: 8.0
echo    - UI Framework: WinForms with Material Design
echo    - Animation System: ModernAnimations
echo    - Database: Entity Framework Core
echo.
echo 🎨 Features Being Tested:
echo    ✅ Ultra-modern dual-panel design
echo    ✅ Gradient background with geometric patterns
echo    ✅ Floating login card with shadows
echo    ✅ Smooth entrance animations
echo    ✅ Interactive input fields with icons
echo    ✅ Loading animations and progress indicators
echo    ✅ Success/error animations
echo    ✅ Password visibility toggle
echo    ✅ Drag functionality
echo    ✅ Keyboard shortcuts
echo.
echo 🔐 Test Credentials:
echo    Admin: admin / admin123
echo    Reception: reception / reception123
echo    Cashier: cashier / cashier123
echo.
echo 🎯 Press any key to launch the login form...
pause > nul

echo 🚀 Launching Modern Login Form...
dotnet run --project . --launch-profile TestLogin

echo.
echo ✅ Test completed. Check console output for details.
pause
