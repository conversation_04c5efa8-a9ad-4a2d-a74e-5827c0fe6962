using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Controls
{
    public partial class EnhancedSuccessNotification : UserControl
    {
        public event EventHandler? NotificationClosed;
        public event EventHandler? PrimaryActionClicked;
        public event EventHandler? SecondaryActionClicked;

        private DispatcherTimer? _autoCloseTimer;
        private DispatcherTimer? _progressTimer = null;

        // Properties
        public string Title
        {
            get => TitleText.Text;
            set => TitleText.Text = value;
        }

        public string Message
        {
            get => MessageText.Text;
            set => MessageText.Text = value;
        }

        public string Icon
        {
            get => IconText.Text;
            set => IconText.Text = value;
        }

        public TimeSpan Duration { get; set; } = TimeSpan.FromSeconds(5);

        public bool ShowActions
        {
            get => ActionPanel.Visibility == Visibility.Visible;
            set => ActionPanel.Visibility = value ? Visibility.Visible : Visibility.Collapsed;
        }

        public string PrimaryActionText
        {
            get => PrimaryActionButton.Content?.ToString() ?? "";
            set => PrimaryActionButton.Content = value;
        }

        public string SecondaryActionText
        {
            get => SecondaryActionButton.Content?.ToString() ?? "";
            set => SecondaryActionButton.Content = value;
        }

        public SuccessNotificationType NotificationType { get; set; } = SuccessNotificationType.General;

        public EnhancedSuccessNotification()
        {
            InitializeComponent();
            SetupTimestamp();
            Loaded += OnLoaded;
        }

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            ApplyNotificationStyle();
        }

        private void SetupTimestamp()
        {
            TimestampText.Text = DateTime.Now.ToString("HH:mm");
        }

        private void ApplyNotificationStyle()
        {
            var (background, iconBg, iconText) = NotificationType switch
            {
                SuccessNotificationType.Save => (Resources["SaveGradient"], "💾", "#2196F3"),
                SuccessNotificationType.Delete => (Resources["DeleteGradient"], "🗑️", "#FF5722"),
                SuccessNotificationType.Update => (Resources["UpdateGradient"], "✏️", "#FF9800"),
                SuccessNotificationType.Create => (Resources["CreateGradient"], "➕", "#9C27B0"),
                SuccessNotificationType.Import => (Resources["SuccessGradient"], "📥", "#4CAF50"),
                SuccessNotificationType.Export => (Resources["SuccessGradient"], "📤", "#4CAF50"),
                SuccessNotificationType.Sync => (Resources["SuccessGradient"], "🔄", "#4CAF50"),
                _ => (Resources["SuccessGradient"], "✅", "#4CAF50")
            };

            NotificationCard.Background = (Brush)background;
            
            if (!string.IsNullOrEmpty(Icon))
            {
                IconText.Text = Icon;
            }
            else
            {
                IconText.Text = iconText;
            }
        }

        public void Show()
        {
            ApplyNotificationStyle();
            
            // Start slide-in animation
            var slideIn = (Storyboard)Resources["SlideInAnimation"];
            slideIn.Begin();

            // Start pulse animation for icon
            var pulse = (Storyboard)Resources["PulseAnimation"];
            pulse.Begin();

            // Setup auto-close timer
            SetupAutoCloseTimer();

            // Setup progress animation
            SetupProgressAnimation();
        }

        private void SetupAutoCloseTimer()
        {
            _autoCloseTimer = new DispatcherTimer
            {
                Interval = Duration
            };
            _autoCloseTimer.Tick += (s, e) =>
            {
                _autoCloseTimer.Stop();
                Hide();
            };
            _autoCloseTimer.Start();
        }

        private void SetupProgressAnimation()
        {
            var progressAnimation = (Storyboard)Resources["ProgressAnimation"];
            var animation = (DoubleAnimation)progressAnimation.Children[0];
            animation.Duration = Duration;
            progressAnimation.Begin();
        }

        public void Hide()
        {
            _autoCloseTimer?.Stop();
            _progressTimer?.Stop();

            var slideOut = (Storyboard)Resources["SlideOutAnimation"];
            slideOut.Begin();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Hide();
        }

        private void PrimaryActionButton_Click(object sender, RoutedEventArgs e)
        {
            PrimaryActionClicked?.Invoke(this, EventArgs.Empty);
        }

        private void SecondaryActionButton_Click(object sender, RoutedEventArgs e)
        {
            SecondaryActionClicked?.Invoke(this, EventArgs.Empty);
        }

        private void SlideOutAnimation_Completed(object sender, EventArgs e)
        {
            NotificationClosed?.Invoke(this, EventArgs.Empty);
        }

        // Static factory methods for common scenarios
        public static EnhancedSuccessNotification CreateSaveNotification(string itemName, string details = "")
        {
            return new EnhancedSuccessNotification
            {
                Title = "تم الحفظ بنجاح",
                Message = $"تم حفظ {itemName} بنجاح" + (string.IsNullOrEmpty(details) ? "" : $"\n{details}"),
                NotificationType = SuccessNotificationType.Save,
                Duration = TimeSpan.FromSeconds(4)
            };
        }

        public static EnhancedSuccessNotification CreateDeleteNotification(string itemName)
        {
            return new EnhancedSuccessNotification
            {
                Title = "تم الحذف بنجاح",
                Message = $"تم حذف {itemName} نهائياً من النظام",
                NotificationType = SuccessNotificationType.Delete,
                Duration = TimeSpan.FromSeconds(3)
            };
        }

        public static EnhancedSuccessNotification CreateUpdateNotification(string itemName)
        {
            return new EnhancedSuccessNotification
            {
                Title = "تم التحديث بنجاح",
                Message = $"تم تحديث بيانات {itemName} بنجاح",
                NotificationType = SuccessNotificationType.Update,
                Duration = TimeSpan.FromSeconds(4)
            };
        }

        public static EnhancedSuccessNotification CreateCreateNotification(string itemName, bool showActions = true)
        {
            return new EnhancedSuccessNotification
            {
                Title = "تم الإنشاء بنجاح",
                Message = $"تم إنشاء {itemName} بنجاح وإضافته إلى النظام",
                NotificationType = SuccessNotificationType.Create,
                Duration = TimeSpan.FromSeconds(5),
                ShowActions = showActions,
                PrimaryActionText = "عرض التفاصيل",
                SecondaryActionText = "إضافة آخر"
            };
        }

        public static EnhancedSuccessNotification CreateCustomNotification(string title, string message, string icon = "✅", TimeSpan? duration = null)
        {
            return new EnhancedSuccessNotification
            {
                Title = title,
                Message = message,
                Icon = icon,
                Duration = duration ?? TimeSpan.FromSeconds(5)
            };
        }

        // Method to pause auto-close on hover
        protected override void OnMouseEnter(System.Windows.Input.MouseEventArgs e)
        {
            base.OnMouseEnter(e);
            _autoCloseTimer?.Stop();
        }

        protected override void OnMouseLeave(System.Windows.Input.MouseEventArgs e)
        {
            base.OnMouseLeave(e);
            if (_autoCloseTimer != null && !_autoCloseTimer.IsEnabled)
            {
                _autoCloseTimer.Start();
            }
        }
    }
}
