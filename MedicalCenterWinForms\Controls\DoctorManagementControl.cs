using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Controls
{
    public partial class DoctorManagementControl : BaseUserControl
    {
        private List<Doctor> _doctors = new List<Doctor>();

        public DoctorManagementControl() : base()
        {
            InitializeComponent();
            LoadDoctors();
        }

        public DoctorManagementControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            LoadDoctors();
        }

        private async void LoadDoctors()
        {
            try
            {
                SetLoadingState(true);
                
                using var context = DatabaseService.GetDbContext();
                _doctors = await context.Doctors.OrderBy(d => d.FullName).ToListAsync();
                
                RefreshDoctorsList();
                lblTotalCount.Text = $"إجمالي الأطباء: {_doctors.Count}";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private void RefreshDoctorsList()
        {
            dgvDoctors.DataSource = _doctors.Select(d => new
            {
                d.DoctorId,
                الاسم = d.FullName,
                التخصص = d.Specialty,
                الحالة = d.IsActive ? "نشط" : "غير نشط"
            }).ToList();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            var searchText = txtSearch.Text.ToLower();
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                RefreshDoctorsList();
            }
            else
            {
                var filtered = _doctors.Where(d => 
                    d.FullName.ToLower().Contains(searchText) || 
                    d.Specialty.ToLower().Contains(searchText))
                    .Select(d => new
                    {
                        d.DoctorId,
                        الاسم = d.FullName,
                        التخصص = d.Specialty,
                        الحالة = d.IsActive ? "نشط" : "غير نشط"
                    }).ToList();

                dgvDoctors.DataSource = filtered;
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            var editControl = new DoctorEditControl(DatabaseService);
            editControl.DoctorSaved += (s, args) => LoadDoctors();
            ShowEditDialog(editControl, "إضافة طبيب جديد");
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvDoctors.SelectedRows.Count == 0)
            {
                ShowWarning("يرجى اختيار طبيب للتعديل");
                return;
            }

            var selectedRow = dgvDoctors.SelectedRows[0];
            var doctorId = (int)selectedRow.Cells["DoctorId"].Value;
            var doctor = _doctors.FirstOrDefault(d => d.DoctorId == doctorId);

            if (doctor != null)
            {
                var editControl = new DoctorEditControl(DatabaseService, doctor);
                editControl.DoctorSaved += (s, args) => LoadDoctors();
                ShowEditDialog(editControl, "تعديل الطبيب");
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvDoctors.SelectedRows.Count == 0)
            {
                ShowWarning("يرجى اختيار طبيب للحذف");
                return;
            }

            var selectedRow = dgvDoctors.SelectedRows[0];
            var doctorId = (int)selectedRow.Cells["DoctorId"].Value;
            var doctor = _doctors.FirstOrDefault(d => d.DoctorId == doctorId);

            if (doctor != null)
            {
                if (!ShowConfirmation($"هل تريد حذف الطبيب '{doctor.FullName}'؟"))
                    return;

                try
                {
                    SetLoadingState(true);
                    
                    using var context = DatabaseService.GetDbContext();
                    var doctorToRemove = await context.Doctors.FindAsync(doctorId);
                    if (doctorToRemove != null)
                    {
                        context.Doctors.Remove(doctorToRemove);
                        await context.SaveChangesAsync();
                        
                        ShowSuccess("تم حذف الطبيب بنجاح");
                        LoadDoctors();
                    }
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف الطبيب: {ex.Message}");
                }
                finally
                {
                    SetLoadingState(false);
                }
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDoctors();
        }

        private void dgvDoctors_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void ShowEditDialog(UserControl control, string title)
        {
            var form = new Form
            {
                Text = title,
                Size = new Size(500, 400),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            control.Dock = DockStyle.Fill;
            form.Controls.Add(control);
            form.ShowDialog();
        }

        public void RefreshData()
        {
            LoadDoctors();
        }
    }

    public partial class DoctorManagementControl
    {
        private DataGridView dgvDoctors;
        private TextBox txtSearch;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private Label lblSearch;
        private Label lblTotalCount;
        private Panel pnlTop;
        private Panel pnlBottom;

        private void InitializeComponent()
        {
            this.dgvDoctors = CreateStyledDataGridView();
            this.txtSearch = CreateStyledTextBox("ابحث بالاسم أو التخصص...");
            this.lblSearch = CreateStyledLabel("بحث:", true);
            this.lblTotalCount = CreateStyledLabel("إجمالي الأطباء: 0", true);
            this.pnlTop = CreateMaterialCard(1);
            this.pnlBottom = CreateMaterialCard(1);

            this.btnAdd = CreateStyledButton("إضافة طبيب", MaterialDesignHelper.Colors.Primary, btnAdd_Click);
            this.btnEdit = CreateStyledButton("تعديل", MaterialDesignHelper.Colors.Warning, btnEdit_Click);
            this.btnDelete = CreateStyledButton("حذف", MaterialDesignHelper.Colors.Error, btnDelete_Click);
            this.btnRefresh = CreateStyledButton("تحديث", MaterialDesignHelper.Colors.Secondary, btnRefresh_Click);

            this.SuspendLayout();

            // pnlTop
            pnlTop.Controls.Add(lblSearch);
            pnlTop.Controls.Add(txtSearch);
            pnlTop.Controls.Add(btnAdd);
            pnlTop.Controls.Add(btnEdit);
            pnlTop.Controls.Add(btnDelete);
            pnlTop.Controls.Add(btnRefresh);
            pnlTop.Dock = DockStyle.Top;
            pnlTop.Height = 100;

            // Controls positioning in pnlTop
            lblSearch.Location = new Point(24, 24);
            lblSearch.Font = ArabicFontHelper.GetArabicHeaderFont(12F);
            lblSearch.ForeColor = MaterialDesignHelper.Colors.TextPrimary;

            txtSearch.Location = new Point(24, 56);
            txtSearch.Size = new Size(350, 40);
            txtSearch.TextChanged += txtSearch_TextChanged;

            btnAdd.Location = new Point(400, 24);
            btnEdit.Location = new Point(540, 24);
            btnDelete.Location = new Point(680, 24);
            btnRefresh.Location = new Point(820, 24);

            // dgvDoctors
            dgvDoctors.Dock = DockStyle.Fill;
            dgvDoctors.CellDoubleClick += dgvDoctors_CellDoubleClick;

            // pnlBottom
            pnlBottom.Controls.Add(lblTotalCount);
            pnlBottom.Dock = DockStyle.Bottom;
            pnlBottom.Height = 60;
            lblTotalCount.Location = new Point(24, 20);
            lblTotalCount.Font = ArabicFontHelper.GetArabicHeaderFont(11F);
            lblTotalCount.ForeColor = MaterialDesignHelper.Colors.TextSecondary;

            // DoctorManagementControl
            Controls.Add(dgvDoctors);
            Controls.Add(pnlTop);
            Controls.Add(pnlBottom);
            Name = "DoctorManagementControl";
            Size = new Size(1000, 600);
            BackColor = MaterialDesignHelper.Colors.Background;

            this.ResumeLayout(false);
        }
    }
}
