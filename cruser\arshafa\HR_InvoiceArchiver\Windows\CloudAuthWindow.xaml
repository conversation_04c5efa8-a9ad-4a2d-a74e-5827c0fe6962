<Window x:Class="HR_InvoiceArchiver.Windows.CloudAuthWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:controls="clr-namespace:HR_InvoiceArchiver.Controls"
        Title="??????? ??????? - Google Drive"
        Height="700" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="#F5F5F5"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,5,0,5"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20"
                materialDesign:ElevationAssist.Elevation="Dp2">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="CloudUpload"
                                       Width="32" Height="32"
                                       Foreground="#2196F3"
                                       VerticalAlignment="Center"/>
                <TextBlock Text="??????? ??????? - Google Drive"
                          FontSize="20" FontWeight="Bold"
                          Foreground="#2C3E50"
                          Margin="15,0,0,0"
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content - UserControl -->
        <controls:CloudStorageControl x:Name="CloudStorageControl"
                                     Grid.Row="1"
                                     Margin="0"/>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="White" Padding="20"
                materialDesign:ElevationAssist.Elevation="Dp2">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="?????"
                       Style="{StaticResource ModernButtonStyle}"
                       Background="#6C757D"
                       Width="120"
                       Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
