using MedicalCenterWinForms.Forms;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Animations;

namespace MedicalCenterWinForms
{
    /// <summary>
    /// Test class for the modern login form
    /// Use this to test the login form independently
    /// </summary>
    public static class TestLoginForm
    {
        [STAThread]
        public static void Main()
        {
            // Initialize application
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            // Enable animations for testing
            ModernAnimations.Config.EnableAnimations = true;
            ModernAnimations.Config.DefaultDuration = 300;
            
            // Set Arabic font support
            SetArabicFont();
            
            // Create database service
            var databaseService = new DatabaseService();
            
            // Show login form
            using var loginForm = new LoginForm(databaseService);
            
            // Add test event handlers
            loginForm.Load += (s, e) =>
            {
                Console.WriteLine("🚀 Modern Login Form Loaded Successfully!");
                Console.WriteLine("📱 Testing ultra-modern medical login interface...");
            };
            
            var result = loginForm.ShowDialog();
            
            if (result == DialogResult.OK && loginForm.CurrentUser != null)
            {
                MessageBox.Show(
                    $"✅ تم تسجيل الدخول بنجاح!\n\n" +
                    $"👤 المستخدم: {loginForm.CurrentUser.Username}\n" +
                    $"🔐 الدور: {loginForm.CurrentUser.Role}\n" +
                    $"⏰ آخر دخول: {loginForm.CurrentUser.LastLoginDate}",
                    "نجح تسجيل الدخول",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                    
                Console.WriteLine($"✅ Login successful for user: {loginForm.CurrentUser.Username}");
            }
            else
            {
                Console.WriteLine("❌ Login cancelled or failed");
            }
        }
        
        private static void SetArabicFont()
        {
            try
            {
                var arabicFont = new Font("Tahoma", 9F, FontStyle.Regular, GraphicsUnit.Point);
                Application.SetDefaultFont(arabicFont);
                Console.WriteLine("🔤 Arabic font support enabled");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Font setup warning: {ex.Message}");
                // Fallback to default font
                var fallbackFont = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
                Application.SetDefaultFont(fallbackFont);
            }
        }
    }
}
