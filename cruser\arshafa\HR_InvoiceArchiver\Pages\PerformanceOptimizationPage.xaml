<UserControl x:Class="HR_InvoiceArchiver.Pages.PerformanceOptimizationPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <materialDesign:PackIcon Grid.Column="0" Kind="Speedometer" 
                                       Width="32" Height="32" 
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                <StackPanel Grid.Column="1" Margin="16,0,0,0" VerticalAlignment="Center">
                    <TextBlock Text="تحسين الأداء" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                    <TextBlock Text="مراقبة وتحسين أداء النظام وقاعدة البيانات" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Opacity="0.7"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="RefreshButton" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="RefreshButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Margin="0,0,8,0"/>
                            <TextBlock Text="تحديث"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="OptimizeAllButton" 
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Click="OptimizeAllButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AutoFix" Margin="0,0,8,0"/>
                            <TextBlock Text="تحسين شامل"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <TabControl x:Name="PerformanceTabControl" 
                      Style="{StaticResource MaterialDesignTabControl}"
                      Margin="16,0,16,16">

                <!-- Database Performance -->
                <TabItem Header="أداء قاعدة البيانات">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Database" Margin="0,0,8,0"/>
                                <TextBlock Text="أداء قاعدة البيانات"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>
                    
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <!-- Performance Overview -->
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="نظرة عامة على الأداء" 
                                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>
                                        
                                        <Button Grid.Column="1" x:Name="AnalyzeButton"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="AnalyzeButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="ChartLine" Margin="0,0,8,0"/>
                                                <TextBlock Text="تحليل"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>

                                    <Grid x:Name="PerformanceOverviewGrid">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Database Size -->
                                        <Border Grid.Column="0" Background="{DynamicResource MaterialDesignCardBackground}"
                                              CornerRadius="8" Padding="16" Margin="0,0,8,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="Database" 
                                                                       Width="24" Height="24"
                                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                       HorizontalAlignment="Center"/>
                                                <TextBlock x:Name="DatabaseSizeTextBlock" Text="--" 
                                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                         HorizontalAlignment="Center" Margin="0,8,0,4"/>
                                                <TextBlock Text="حجم قاعدة البيانات" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- Query Performance -->
                                        <Border Grid.Column="1" Background="{DynamicResource MaterialDesignCardBackground}"
                                              CornerRadius="8" Padding="16" Margin="4,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="Timer" 
                                                                       Width="24" Height="24"
                                                                       Foreground="{DynamicResource SecondaryHueMidBrush}"
                                                                       HorizontalAlignment="Center"/>
                                                <TextBlock x:Name="QueryTimeTextBlock" Text="--" 
                                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                         HorizontalAlignment="Center" Margin="0,8,0,4"/>
                                                <TextBlock Text="متوسط زمن الاستعلام" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- Indexes -->
                                        <Border Grid.Column="2" Background="{DynamicResource MaterialDesignCardBackground}"
                                              CornerRadius="8" Padding="16" Margin="4,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon Kind="ViewList" 
                                                                       Width="24" Height="24"
                                                                       Foreground="{DynamicResource AccentColorBrush}"
                                                                       HorizontalAlignment="Center"/>
                                                <TextBlock x:Name="IndexesTextBlock" Text="--" 
                                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                         HorizontalAlignment="Center" Margin="0,8,0,4"/>
                                                <TextBlock Text="الفهارس" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- Performance Score -->
                                        <Border Grid.Column="3" Background="{DynamicResource MaterialDesignCardBackground}"
                                              CornerRadius="8" Padding="16" Margin="8,0,0,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <materialDesign:PackIcon x:Name="ScoreIcon" Kind="Star" 
                                                                       Width="24" Height="24"
                                                                       HorizontalAlignment="Center"/>
                                                <TextBlock x:Name="ScoreTextBlock" Text="--" 
                                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                         HorizontalAlignment="Center" Margin="0,8,0,4"/>
                                                <TextBlock Text="درجة الأداء" 
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                         HorizontalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Optimization Actions -->
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="إجراءات التحسين" 
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <Button Grid.Row="0" Grid.Column="0" x:Name="CreateIndexesButton"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Margin="0,0,8,8"
                                              Click="CreateIndexesButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Plus" Margin="0,0,8,0"/>
                                                <TextBlock Text="إنشاء الفهارس"/>
                                            </StackPanel>
                                        </Button>

                                        <Button Grid.Row="0" Grid.Column="1" x:Name="UpdateStatsButton"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Margin="8,0,0,8"
                                              Click="UpdateStatsButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Update" Margin="0,0,8,0"/>
                                                <TextBlock Text="تحديث الإحصائيات"/>
                                            </StackPanel>
                                        </Button>

                                        <Button Grid.Row="1" Grid.Column="0" x:Name="CleanupButton"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Margin="0,0,8,8"
                                              Click="CleanupButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Broom" Margin="0,0,8,0"/>
                                                <TextBlock Text="تنظيف قاعدة البيانات"/>
                                            </StackPanel>
                                        </Button>

                                        <Button Grid.Row="1" Grid.Column="1" x:Name="CompressButton"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Margin="8,0,0,8"
                                              Click="CompressButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Compress" Margin="0,0,8,0"/>
                                                <TextBlock Text="ضغط قاعدة البيانات"/>
                                            </StackPanel>
                                        </Button>

                                        <Button Grid.Row="2" Grid.Column="0" x:Name="OptimizeConnectionsButton"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Margin="0,0,8,0"
                                              Click="OptimizeConnectionsButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Connection" Margin="0,0,8,0"/>
                                                <TextBlock Text="تحسين الاتصالات"/>
                                            </StackPanel>
                                        </Button>

                                        <Button Grid.Row="2" Grid.Column="1" x:Name="ScheduleOptimizationButton"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Margin="8,0,0,0"
                                              Click="ScheduleOptimizationButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Schedule" Margin="0,0,8,0"/>
                                                <TextBlock Text="جدولة التحسين"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Recommendations -->
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="التوصيات" 
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>
                                    
                                    <ItemsControl x:Name="RecommendationsItemsControl">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                                      CornerRadius="4" Padding="12" Margin="0,0,0,8">
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon Kind="Lightbulb" 
                                                                               Width="16" Height="16"
                                                                               Foreground="{DynamicResource AccentColorBrush}"
                                                                               VerticalAlignment="Center"
                                                                               Margin="0,0,8,0"/>
                                                        <TextBlock Text="{Binding}" 
                                                                 VerticalAlignment="Center"
                                                                 TextWrapping="Wrap"/>
                                                    </StackPanel>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- Memory Management -->
                <TabItem Header="إدارة الذاكرة">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Memory" Margin="0,0,8,0"/>
                                <TextBlock Text="إدارة الذاكرة"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>
                    
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <!-- Memory Usage -->
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="استخدام الذاكرة" 
                                                 Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>
                                        
                                        <Button Grid.Column="1" x:Name="RefreshMemoryButton"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="RefreshMemoryButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Refresh" Margin="0,0,8,0"/>
                                                <TextBlock Text="تحديث"/>
                                            </StackPanel>
                                        </Button>
                                    </Grid>

                                    <Grid x:Name="MemoryUsageGrid">
                                        <!-- سيتم ملء هذا الجزء برمجياً -->
                                    </Grid>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Memory Actions -->
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="إجراءات الذاكرة" 
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>
                                    
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button x:Name="ClearCacheButton"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Margin="0,0,8,0"
                                              Click="ClearCacheButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Delete" Margin="0,0,8,0"/>
                                                <TextBlock Text="مسح ذاكرة التخزين المؤقت"/>
                                            </StackPanel>
                                        </Button>

                                        <Button x:Name="OptimizeCacheButton"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Margin="8,0,0,0"
                                              Click="OptimizeCacheButton_Click">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Tune" Margin="0,0,8,0"/>
                                                <TextBlock Text="تحسين ذاكرة التخزين المؤقت"/>
                                            </StackPanel>
                                        </Button>
                                    </StackPanel>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- Slow Queries -->
                <TabItem Header="الاستعلامات البطيئة">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Database" Margin="0,0,8,0"/>
                                <TextBlock Text="الاستعلامات البطيئة"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>
                    
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel>
                            <materialDesign:Card Padding="16">
                                <StackPanel>
                                    <TextBlock Text="الاستعلامات البطيئة" 
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Margin="0,0,0,16"/>
                                    
                                    <DataGrid x:Name="SlowQueriesDataGrid"
                                            Style="{StaticResource MaterialDesignDataGrid}"
                                            AutoGenerateColumns="False"
                                            IsReadOnly="True">
                                        <DataGrid.Columns>
                                            <DataGridTextColumn Header="الاستعلام" Binding="{Binding Query}" Width="*"/>
                                            <DataGridTextColumn Header="زمن التنفيذ (مللي ثانية)" Binding="{Binding ExecutionTimeMs}" Width="Auto"/>
                                            <DataGridTextColumn Header="عدد مرات التنفيذ" Binding="{Binding ExecutionCount}" Width="Auto"/>
                                            <DataGridTextColumn Header="آخر تنفيذ" Binding="{Binding LastExecuted, StringFormat=yyyy-MM-dd HH:mm}" Width="Auto"/>
                                        </DataGrid.Columns>
                                    </DataGrid>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>
            </TabControl>
        </ScrollViewer>

        <!-- Loading Indicator -->
        <Grid Grid.RowSpan="2" x:Name="LoadingGrid" 
              Background="{DynamicResource MaterialDesignPaper}" 
              Opacity="0.8" 
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="48" Height="48"
                           Margin="0,0,0,16"/>
                <TextBlock x:Name="LoadingTextBlock" 
                         Text="جاري التحليل..."
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
