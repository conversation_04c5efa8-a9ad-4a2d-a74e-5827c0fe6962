@model IEnumerable<MedicalCenterSystem.Models.DoctorService>

@{
    ViewData["Title"] = "إدارة ربط الأطباء بالخدمات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-link me-2"></i>
                        إدارة ربط الأطباء بالخدمات
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label for="doctorFilter" class="form-label">فلترة بالطبيب:</label>
                            @Html.DropDownList("doctorId", ViewBag.Doctors as SelectList, "جميع الأطباء", 
                                new { @class = "form-select", id = "doctorFilter" })
                        </div>
                        <div class="col-md-3">
                            <label for="serviceFilter" class="form-label">فلترة بالخدمة:</label>
                            @Html.DropDownList("serviceId", ViewBag.MedicalServices as SelectList, "جميع الخدمات", 
                                new { @class = "form-select", id = "serviceFilter" })
                        </div>
                        <div class="col-md-4">
                            <label for="searchInput" class="form-label">بحث:</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="بحث في الأطباء والخدمات...">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="clearFilters()">
                                <i class="fas fa-times"></i> مسح
                            </button>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <a asp-action="Create" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i> إضافة ربط جديد
                            </a>
                            <button type="button" class="btn btn-info ms-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i> تحديث
                            </button>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="doctorServicesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم الطبيب</th>
                                    <th>اسم الخدمة</th>
                                    <th>نوع الربط</th>
                                    <th>له نسبة</th>
                                    <th>النسبة %</th>
                                    <th>مبلغ مقطوع</th>
                                    <th>المبلغ المقطوع</th>
                                    <th>السعر الافتراضي</th>
                                    <th>تكلفة الخدمة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@Html.DisplayFor(modelItem => item.Doctor.FullName)</td>
                                        <td>@Html.DisplayFor(modelItem => item.MedicalService.ServiceName)</td>
                                        <td>
                                            <span class="badge bg-@(item.LinkType == "Direct" ? "primary" : "info")">
                                                @(item.LinkType == "Direct" ? "مباشر" : "تحويل")
                                            </span>
                                        </td>
                                        <td>
                                            @if (item.HasPercentage)
                                            {
                                                <i class="fas fa-check text-success"></i>
                                            }
                                            else
                                            {
                                                <i class="fas fa-times text-danger"></i>
                                            }
                                        </td>
                                        <td>@(item.Percentage?.ToString("F2") ?? "-")</td>
                                        <td>
                                            @if (item.IsFixedAmount)
                                            {
                                                <i class="fas fa-check text-success"></i>
                                            }
                                            else
                                            {
                                                <i class="fas fa-times text-danger"></i>
                                            }
                                        </td>
                                        <td>@(item.FixedAmount?.ToString("C") ?? "-")</td>
                                        <td>@(item.DoctorDefaultPrice?.ToString("C") ?? "-")</td>
                                        <td>@(item.ServiceCost?.ToString("C") ?? "-")</td>
                                        <td>
                                            <span class="badge bg-@(item.IsActive ? "success" : "secondary")">
                                                @(item.IsActive ? "نشط" : "غير نشط")
                                            </span>
                                        </td>
                                        <td>@item.CreatedDate.ToString("yyyy-MM-dd")</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.DoctorServiceId" 
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.DoctorServiceId" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete(@item.DoctorServiceId, '@item.Doctor.FullName', '@item.MedicalService.ServiceName')" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-link fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد روابط بين الأطباء والخدمات</h5>
                            <p class="text-muted">ابدأ بإضافة ربط جديد بين طبيب وخدمة طبية</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> إضافة ربط جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="deleteMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Filter functionality
        $('#doctorFilter, #serviceFilter').change(function() {
            applyFilters();
        });

        $('#searchInput').on('input', function() {
            applyFilters();
        });

        function applyFilters() {
            const doctorId = $('#doctorFilter').val();
            const serviceId = $('#serviceFilter').val();
            const search = $('#searchInput').val();
            
            const url = new URL(window.location.href);
            url.searchParams.set('doctorId', doctorId || '');
            url.searchParams.set('serviceId', serviceId || '');
            url.searchParams.set('search', search || '');
            
            window.location.href = url.toString();
        }

        function clearFilters() {
            $('#doctorFilter').val('');
            $('#serviceFilter').val('');
            $('#searchInput').val('');
            
            const url = new URL(window.location.href);
            url.search = '';
            window.location.href = url.toString();
        }

        function refreshData() {
            window.location.reload();
        }

        function confirmDelete(id, doctorName, serviceName) {
            $('#deleteMessage').text(`هل أنت متأكد من حذف ربط الطبيب "${doctorName}" بالخدمة "${serviceName}"؟`);
            $('#deleteForm').attr('action', '@Url.Action("Delete")/' + id);
            $('#deleteModal').modal('show');
        }

        // Initialize tooltips
        $(document).ready(function() {
            $('[title]').tooltip();
        });
    </script>
}

@section Styles {
    <style>
        .table th {
            background-color: #343a40;
            color: white;
            border-color: #454d55;
        }
        
        .btn-group .btn {
            margin-right: 2px;
        }
        
        .badge {
            font-size: 0.75em;
        }
        
        .card {
            border: none;
            border-radius: 10px;
        }
        
        .card-header {
            border-radius: 10px 10px 0 0;
        }
        
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
}
