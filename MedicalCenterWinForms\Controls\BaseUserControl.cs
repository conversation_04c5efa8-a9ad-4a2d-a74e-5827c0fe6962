using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Services;

namespace MedicalCenterWinForms.Controls
{
    public partial class BaseUserControl : UserControl
    {
        protected DatabaseService DatabaseService { get; private set; }
        protected bool IsLoading { get; set; }

        public BaseUserControl()
        {
            InitializeComponent();
            DatabaseService = new DatabaseService();
            SetupArabicSupport();
        }

        public BaseUserControl(DatabaseService databaseService)
        {
            InitializeComponent();
            DatabaseService = databaseService;
            SetupArabicSupport();
        }

        private void SetupArabicSupport()
        {
            this.RightToLeft = RightToLeft.Yes;
            // Note: RightToLeftLayout is not available on UserControl, only on Form
            ArabicFontHelper.ApplyArabicFont(this);
        }

        protected virtual void OnLoad()
        {
            // Override in derived classes
        }

        protected void ShowError(string message, string title = "خطأ")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        protected void ShowSuccess(string message, string title = "نجح")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected void ShowWarning(string message, string title = "تنبيه")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        protected bool ShowConfirmation(string message, string title = "تأكيد")
        {
            return MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes;
        }

        protected void SetLoadingState(bool loading)
        {
            IsLoading = loading;
            this.Enabled = !loading;
            this.Cursor = loading ? Cursors.WaitCursor : Cursors.Default;
        }

        protected Button CreateStyledButton(string text, Color backgroundColor, EventHandler clickHandler)
        {
            var button = MaterialDesignHelper.CreateMaterialButton(text, backgroundColor,
                MaterialDesignHelper.Colors.TextOnPrimary, clickHandler, 130, 44);

            // Add modern button styling
            button.Font = ArabicFontHelper.GetArabicButtonFont(12F, FontStyle.Bold);
            button.Margin = new Padding(8, 4, 8, 4);

            return button;
        }

        protected DataGridView CreateStyledDataGridView()
        {
            var dgv = MaterialDesignHelper.CreateMaterialDataGridView();

            // Enhanced modern styling
            dgv.BackgroundColor = MaterialDesignHelper.Colors.Background;
            dgv.GridColor = MaterialDesignHelper.Colors.Divider;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;

            // Modern selection colors
            dgv.DefaultCellStyle.SelectionBackColor = MaterialDesignHelper.Colors.PrimaryLight;
            dgv.DefaultCellStyle.SelectionForeColor = MaterialDesignHelper.Colors.TextPrimary;

            // Enhanced alternating row colors
            dgv.AlternatingRowsDefaultCellStyle.BackColor = MaterialDesignHelper.Colors.SurfaceElevated;
            dgv.AlternatingRowsDefaultCellStyle.SelectionBackColor = MaterialDesignHelper.Colors.PrimaryLight;

            return dgv;
        }

        protected TextBox CreateStyledTextBox(string placeholder = "")
        {
            var textBox = MaterialDesignHelper.CreateMaterialTextBox(placeholder);

            // Enhanced modern styling
            textBox.Font = ArabicFontHelper.GetArabicTextBoxFont(12F);
            textBox.Height = 44;
            textBox.Margin = new Padding(8, 4, 8, 4);

            return textBox;
        }

        protected Label CreateStyledLabel(string text, bool isBold = false)
        {
            var label = new Label
            {
                Text = text,
                Font = isBold ? ArabicFontHelper.GetArabicHeaderFont(14F, FontStyle.Bold) : ArabicFontHelper.GetArabicLabelFont(12F),
                ForeColor = isBold ? MaterialDesignHelper.Colors.MedicalPrimary : MaterialDesignHelper.Colors.TextSecondary,
                AutoSize = true,
                RightToLeft = RightToLeft.Yes,
                Margin = new Padding(8, 4, 8, 4)
            };

            return label;
        }

        protected ComboBox CreateStyledComboBox()
        {
            var comboBox = new ComboBox
            {
                Font = ArabicFontHelper.GetArabicFont(10F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };

            return comboBox;
        }

        protected Panel CreateStyledPanel(Color? backgroundColor = null)
        {
            var panel = new Panel
            {
                BackColor = backgroundColor ?? MaterialDesignHelper.Colors.Background,
                Padding = new Padding(16)
            };

            return panel;
        }

        protected Panel CreateMaterialCard(int elevation = 2)
        {
            var card = MaterialDesignHelper.CreateMaterialCard(elevation);

            // Enhanced modern card styling
            card.BackColor = MaterialDesignHelper.Colors.Surface;
            card.Margin = new Padding(16, 8, 16, 8);
            card.Padding = new Padding(24, 16, 24, 16);

            return card;
        }
    }

    public partial class BaseUserControl
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Name = "BaseUserControl";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            
            this.ResumeLayout(false);
        }
    }
}
