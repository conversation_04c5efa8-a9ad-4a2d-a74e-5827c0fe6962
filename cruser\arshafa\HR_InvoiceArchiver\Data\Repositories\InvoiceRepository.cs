using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Data.Repositories
{
    public interface IInvoiceRepository
    {
        Task<IEnumerable<Invoice>> GetAllAsync();
        Task<IEnumerable<Invoice>> GetAllBasicAsync(); // طريقة محسنة للأداء
        Task<Invoice?> GetByIdAsync(int id);
        Task<Invoice?> GetByInvoiceNumberAsync(string invoiceNumber);
        Task<IEnumerable<Invoice>> GetBySupplierIdAsync(int supplierId);
        Task<Invoice> AddAsync(Invoice invoice);
        Task<Invoice> UpdateAsync(Invoice invoice);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> ExistsByInvoiceNumberAsync(string invoiceNumber, int? excludeId = null);
        Task<IEnumerable<Invoice>> SearchAsync(string searchTerm);
        Task<IEnumerable<Invoice>> GetFilteredAsync(InvoiceFilter filter);
        Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync();
        Task<int> GetTotalCountAsync();
        Task<decimal> GetTotalAmountAsync();
        Task<decimal> GetTotalPaidAmountAsync();
        Task<int> GetCountByStatusAsync(InvoiceStatus status);
        Task<(int TotalCount, decimal TotalAmount, decimal PaidAmount)> GetStatisticsAsync(); // إحصائيات محسنة
        Task<PaginationResult<Invoice>> GetPagedAsync(PaginationCriteria criteria); // صفحات محسنة
    }

    public class InvoiceFilter
    {
        public string? InvoiceNumber { get; set; }
        public int? SupplierId { get; set; }
        public InvoiceStatus? Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateTime? DueFromDate { get; set; }
        public DateTime? DueToDate { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public string? SearchTerm { get; set; }
        public bool? IsOverdue { get; set; }
        public bool OverdueOnly { get; set; } = false;
    }

    public class InvoiceRepository : IInvoiceRepository
    {
        private readonly DatabaseContext _context;

        public InvoiceRepository(DatabaseContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Invoice>> GetAllAsync()
        {
            return await _context.Invoices
                .AsNoTracking() // Move to beginning for better performance
                .Where(i => i.IsActive)
                .Include(i => i.Supplier)
                .Include(i => i.Payments.Where(p => p.IsActive))
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<Invoice?> GetByIdAsync(int id)
        {
            return await _context.Invoices
                .Include(i => i.Supplier)
                .Include(i => i.Payments.Where(p => p.IsActive))
                .FirstOrDefaultAsync(i => i.Id == id && i.IsActive);
        }

        /// <summary>
        /// الحصول على الفواتير الأساسية بدون تحميل العلاقات (للأداء السريع)
        /// </summary>
        public async Task<IEnumerable<Invoice>> GetAllBasicAsync()
        {
            return await _context.Invoices
                .AsNoTracking()
                .Where(i => i.IsActive)
                .Select(i => new Invoice
                {
                    Id = i.Id,
                    InvoiceNumber = i.InvoiceNumber,
                    Amount = i.Amount,
                    PaidAmount = i.PaidAmount,
                    InvoiceDate = i.InvoiceDate,
                    DueDate = i.DueDate,
                    Status = i.Status,
                    SupplierId = i.SupplierId,
                    Description = i.Description,
                    // تحميل اسم المورد فقط بدون كامل الكائن
                    Supplier = new Supplier { Id = i.Supplier.Id, Name = i.Supplier.Name }
                })
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<Invoice?> GetByInvoiceNumberAsync(string invoiceNumber)
        {
            return await _context.Invoices
                .Where(i => i.IsActive && i.InvoiceNumber == invoiceNumber)
                .Include(i => i.Supplier)
                .Include(i => i.Payments.Where(p => p.IsActive))
                .FirstOrDefaultAsync();
        }

        public async Task<IEnumerable<Invoice>> GetBySupplierIdAsync(int supplierId)
        {
            return await _context.Invoices
                .Where(i => i.SupplierId == supplierId && i.IsActive)
                .Include(i => i.Supplier)
                .Include(i => i.Payments.Where(p => p.IsActive))
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<Invoice> AddAsync(Invoice invoice)
        {
            invoice.CreatedDate = DateTime.Now;
            invoice.IsActive = true;
            invoice.UpdateStatus();
            
            _context.Invoices.Add(invoice);
            await _context.SaveChangesAsync();
            return invoice;
        }

        public async Task<Invoice> UpdateAsync(Invoice invoice)
        {
            invoice.UpdatedDate = DateTime.Now;
            invoice.UpdateStatus();
            
            _context.Entry(invoice).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return invoice;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var invoice = await _context.Invoices.FindAsync(id);
            if (invoice == null) return false;

            // Soft delete
            invoice.IsActive = false;
            invoice.UpdatedDate = DateTime.Now;
            
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Invoices.AnyAsync(i => i.Id == id && i.IsActive);
        }

        public async Task<bool> ExistsByInvoiceNumberAsync(string invoiceNumber, int? excludeId = null)
        {
            var query = _context.Invoices.Where(i => i.InvoiceNumber == invoiceNumber && i.IsActive);
            
            if (excludeId.HasValue)
                query = query.Where(i => i.Id != excludeId.Value);
            
            return await query.AnyAsync();
        }

        public async Task<IEnumerable<Invoice>> SearchAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllBasicAsync(); // استخدام النسخة المحسنة

            searchTerm = searchTerm.Trim().ToLower();

            return await _context.Invoices
                .AsNoTracking() // تحسين الأداء
                .Where(i => i.IsActive &&
                           (i.InvoiceNumber.ToLower().Contains(searchTerm) ||
                            i.Supplier.Name.ToLower().Contains(searchTerm) ||
                            (i.Notes != null && i.Notes.ToLower().Contains(searchTerm))))
                .Include(i => i.Supplier)
                .Include(i => i.Payments.Where(p => p.IsActive))
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetFilteredAsync(InvoiceFilter filter)
        {
            var query = _context.Invoices.Where(i => i.IsActive);

            if (filter.SupplierId.HasValue)
                query = query.Where(i => i.SupplierId == filter.SupplierId.Value);

            if (filter.Status.HasValue)
                query = query.Where(i => i.Status == filter.Status.Value);

            if (filter.FromDate.HasValue)
                query = query.Where(i => i.InvoiceDate >= filter.FromDate.Value);

            if (filter.ToDate.HasValue)
                query = query.Where(i => i.InvoiceDate <= filter.ToDate.Value);

            if (filter.MinAmount.HasValue)
                query = query.Where(i => i.Amount >= filter.MinAmount.Value);

            if (filter.MaxAmount.HasValue)
                query = query.Where(i => i.Amount <= filter.MaxAmount.Value);

            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                var searchTerm = filter.SearchTerm.Trim().ToLower();
                query = query.Where(i => i.InvoiceNumber.ToLower().Contains(searchTerm) ||
                                        i.Supplier.Name.ToLower().Contains(searchTerm) ||
                                        (i.Notes != null && i.Notes.ToLower().Contains(searchTerm)));
            }

            if (filter.IsOverdue.HasValue && filter.IsOverdue.Value)
                query = query.Where(i => i.DueDate.HasValue && i.DueDate.Value < DateTime.Now && i.Status != InvoiceStatus.Paid);

            return await query
                .Include(i => i.Supplier)
                .Include(i => i.Payments.Where(p => p.IsActive))
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync()
        {
            return await _context.Invoices
                .Where(i => i.IsActive && 
                           i.DueDate.HasValue && 
                           i.DueDate.Value < DateTime.Now && 
                           i.Status != InvoiceStatus.Paid)
                .Include(i => i.Supplier)
                .Include(i => i.Payments.Where(p => p.IsActive))
                .OrderBy(i => i.DueDate)
                .ToListAsync();
        }

        public async Task<int> GetTotalCountAsync()
        {
            return await _context.Invoices
                .AsNoTracking()
                .CountAsync(i => i.IsActive);
        }

        public async Task<decimal> GetTotalAmountAsync()
        {
            return await _context.Invoices
                .AsNoTracking()
                .Where(i => i.IsActive)
                .SumAsync(i => i.Amount);
        }

        public async Task<decimal> GetTotalPaidAmountAsync()
        {
            return await _context.Invoices
                .AsNoTracking()
                .Where(i => i.IsActive)
                .SumAsync(i => i.PaidAmount);
        }

        /// <summary>
        /// الحصول على جميع الإحصائيات في استعلام واحد (محسن للأداء)
        /// </summary>
        public async Task<(int TotalCount, decimal TotalAmount, decimal PaidAmount)> GetStatisticsAsync()
        {
            var stats = await _context.Invoices
                .AsNoTracking()
                .Where(i => i.IsActive)
                .GroupBy(i => 1)
                .Select(g => new
                {
                    TotalCount = g.Count(),
                    TotalAmount = g.Sum(i => i.Amount),
                    PaidAmount = g.Sum(i => i.PaidAmount)
                })
                .FirstOrDefaultAsync();

            return stats != null
                ? (stats.TotalCount, stats.TotalAmount, stats.PaidAmount)
                : (0, 0, 0);
        }

        /// <summary>
        /// الحصول على الفواتير مع تقسيم الصفحات (محسن للأداء)
        /// </summary>
        public async Task<PaginationResult<Invoice>> GetPagedAsync(PaginationCriteria criteria)
        {
            var query = _context.Invoices
                .AsNoTracking()
                .Where(i => i.IsActive);

            // تطبيق البحث إذا وجد
            if (!string.IsNullOrWhiteSpace(criteria.SearchTerm))
            {
                var searchTerm = criteria.SearchTerm.Trim().ToLower();
                query = query.Where(i =>
                    i.InvoiceNumber.ToLower().Contains(searchTerm) ||
                    i.Supplier.Name.ToLower().Contains(searchTerm) ||
                    (i.Notes != null && i.Notes.ToLower().Contains(searchTerm)));
            }

            // حساب العدد الإجمالي
            var totalCount = await query.CountAsync();

            // تطبيق الترتيب
            query = criteria.SortBy?.ToLower() switch
            {
                "invoicenumber" => criteria.SortDescending
                    ? query.OrderByDescending(i => i.InvoiceNumber)
                    : query.OrderBy(i => i.InvoiceNumber),
                "amount" => criteria.SortDescending
                    ? query.OrderByDescending(i => i.Amount)
                    : query.OrderBy(i => i.Amount),
                "supplier" => criteria.SortDescending
                    ? query.OrderByDescending(i => i.Supplier.Name)
                    : query.OrderBy(i => i.Supplier.Name),
                "status" => criteria.SortDescending
                    ? query.OrderByDescending(i => i.Status)
                    : query.OrderBy(i => i.Status),
                _ => criteria.SortDescending
                    ? query.OrderByDescending(i => i.InvoiceDate)
                    : query.OrderBy(i => i.InvoiceDate)
            };

            // تطبيق التقسيم
            var items = await query
                .Include(i => i.Supplier)
                .Skip((criteria.PageNumber - 1) * criteria.PageSize)
                .Take(criteria.PageSize)
                .ToListAsync();

            return new PaginationResult<Invoice>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = criteria.PageNumber,
                PageSize = criteria.PageSize
            };
        }

        public async Task<int> GetCountByStatusAsync(InvoiceStatus status)
        {
            return await _context.Invoices.CountAsync(i => i.IsActive && i.Status == status);
        }
    }
}
