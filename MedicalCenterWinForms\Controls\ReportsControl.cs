using System;
using System.Drawing;
using System.Windows.Forms;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;
using MedicalCenterWinForms.Styles;

namespace MedicalCenterWinForms.Controls
{
    public partial class ReportsControl : BaseUserControl
    {
        // UI Components
        private Panel reportsPanel;
        private Panel previewPanel;
        private ListView reportsList;
        private RichTextBox reportPreview;
        private Button btnGenerate;
        private Button btnExport;
        private Button btnPrint;
        private Button btnAdvancedFinancialReports;
        private ComboBox cmbReportType;
        private DateTimePicker dtpFromDate;
        private DateTimePicker dtpToDate;
        private Label lblReportType;
        private Label lblDateRange;

        public ReportsControl() : base()
        {
            InitializeComponent();
            LoadReportTypes();
        }

        public ReportsControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            LoadReportTypes();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Configure main control
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Dock = DockStyle.Fill;
            this.Padding = new Padding(20);
            this.RightToLeft = RightToLeft.Yes;

            InitializeReportsPanel();
            InitializePreviewPanel();

            this.ResumeLayout(false);
        }

        private void InitializeReportsPanel()
        {
            this.reportsPanel = new Panel
            {
                Width = 400,
                Dock = DockStyle.Right,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            MaterialDesignHelper.AddShadow(reportsPanel);

            // Title
            var titleLabel = new Label
            {
                Text = "📊 إنشاء التقارير",
                Font = ArabicFontHelper.GetArabicFont(16F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Report type selection
            this.lblReportType = new Label
            {
                Text = "نوع التقرير:",
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(20, 70)
            };

            this.cmbReportType = new ComboBox
            {
                Size = new Size(350, 30),
                Location = new Point(20, 95),
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes,
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Date range selection
            this.lblDateRange = new Label
            {
                Text = "الفترة الزمنية:",
                Font = ArabicFontHelper.GetArabicFont(12F),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(20, 140)
            };

            this.dtpFromDate = new DateTimePicker
            {
                Size = new Size(165, 30),
                Location = new Point(20, 165),
                Font = ArabicFontHelper.GetArabicFont(10F),
                Format = DateTimePickerFormat.Short,
                RightToLeft = RightToLeft.Yes
            };

            this.dtpToDate = new DateTimePicker
            {
                Size = new Size(165, 30),
                Location = new Point(205, 165),
                Font = ArabicFontHelper.GetArabicFont(10F),
                Format = DateTimePickerFormat.Short,
                RightToLeft = RightToLeft.Yes
            };

            // Action buttons
            this.btnGenerate = MaterialDesignHelper.CreateModernButton("🔄 إنشاء التقرير", MaterialDesignHelper.Colors.Primary);
            this.btnExport = MaterialDesignHelper.CreateModernButton("📤 تصدير PDF", MaterialDesignHelper.Colors.Success);
            this.btnPrint = MaterialDesignHelper.CreateModernButton("🖨️ طباعة", MaterialDesignHelper.Colors.Secondary);
            this.btnAdvancedFinancialReports = ModernMedicalTheme.Components.CreateAdvancedButton(
                "📊 التقارير المالية المتقدمة", ModernMedicalTheme.Components.ButtonStyle.Primary);

            btnGenerate.Size = new Size(350, 40);
            btnExport.Size = new Size(170, 40);
            btnPrint.Size = new Size(170, 40);
            btnAdvancedFinancialReports.Size = new Size(350, 45);

            btnGenerate.Location = new Point(20, 220);
            btnAdvancedFinancialReports.Location = new Point(20, 270);
            btnExport.Location = new Point(20, 325);
            btnPrint.Location = new Point(200, 325);

            // Event handlers
            btnAdvancedFinancialReports.Click += BtnAdvancedFinancialReports_Click;

            // Recent reports list
            var recentLabel = new Label
            {
                Text = "📋 التقارير الأخيرة:",
                Font = ArabicFontHelper.GetArabicFont(14F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(20, 385)
            };

            this.reportsList = new ListView
            {
                Size = new Size(350, 150),
                Location = new Point(20, 415),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Font = ArabicFontHelper.GetArabicFont(10F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            // Add columns to reports list
            reportsList.Columns.Add("التاريخ", 100);
            reportsList.Columns.Add("نوع التقرير", 150);
            reportsList.Columns.Add("الحالة", 100);

            reportsPanel.Controls.AddRange(new Control[]
            {
                titleLabel, lblReportType, cmbReportType, lblDateRange,
                dtpFromDate, dtpToDate, btnGenerate, btnAdvancedFinancialReports,
                btnExport, btnPrint, recentLabel, reportsList
            });

            this.Controls.Add(reportsPanel);
        }

        private void InitializePreviewPanel()
        {
            this.previewPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20),
                Margin = new Padding(0, 0, 10, 0)
            };

            MaterialDesignHelper.AddShadow(previewPanel);

            // Preview title
            var previewTitle = new Label
            {
                Text = "👁️ معاينة التقرير",
                Font = ArabicFontHelper.GetArabicFont(16F, FontStyle.Bold),
                ForeColor = MaterialDesignHelper.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(20, 20)
            };

            // Report preview area
            this.reportPreview = new RichTextBox
            {
                Size = new Size(this.Width - 480, this.Height - 100),
                Location = new Point(20, 60),
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes,
                ReadOnly = true,
                BackColor = Color.White,
                BorderStyle = BorderStyle.None,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            // Default content
            reportPreview.Text = @"مرحباً بك في نظام التقارير

اختر نوع التقرير والفترة الزمنية من القائمة الجانبية، ثم اضغط على 'إنشاء التقرير' لعرض النتائج هنا.

أنواع التقارير المتاحة:
• تقرير المراجعين الشهري
• تقرير الإيرادات
• تقرير الأطباء والخدمات
• تقرير المدفوعات
• تقرير الأنشطة اليومية

يمكنك تصدير التقرير كملف PDF أو طباعته مباشرة.";

            previewPanel.Controls.AddRange(new Control[] { previewTitle, reportPreview });
            this.Controls.Add(previewPanel);
        }

        private void LoadReportTypes()
        {
            var reportTypes = new[]
            {
                "تقرير المراجعين الشهري",
                "تقرير الإيرادات الشهرية",
                "تقرير الأطباء والخدمات",
                "تقرير المدفوعات اليومية",
                "تقرير الأنشطة اليومية",
                "تقرير الخدمات الأكثر طلباً",
                "تقرير أداء الأطباء",
                "تقرير المراجعين الجدد",
                "تقرير المدفوعات المعلقة",
                "تقرير شامل للمركز"
            };

            cmbReportType.Items.AddRange(reportTypes);
            cmbReportType.SelectedIndex = 0;

            // Load recent reports
            LoadRecentReports();
        }

        private void LoadRecentReports()
        {
            var recentReports = new[]
            {
                new { Date = "2025/01/20", Type = "تقرير المراجعين", Status = "مكتمل" },
                new { Date = "2025/01/19", Type = "تقرير الإيرادات", Status = "مكتمل" },
                new { Date = "2025/01/18", Type = "تقرير الأطباء", Status = "مكتمل" },
                new { Date = "2025/01/17", Type = "تقرير المدفوعات", Status = "مكتمل" },
                new { Date = "2025/01/16", Type = "تقرير شامل", Status = "مكتمل" }
            };

            foreach (var report in recentReports)
            {
                var item = new ListViewItem(report.Date);
                item.SubItems.Add(report.Type);
                item.SubItems.Add(report.Status);
                reportsList.Items.Add(item);
            }
        }

        private void BtnAdvancedFinancialReports_Click(object sender, EventArgs e)
        {
            try
            {
                var advancedReportsForm = new Form
                {
                    Text = "التقارير المالية المتقدمة",
                    Size = new Size(1400, 900),
                    StartPosition = FormStartPosition.CenterParent,
                    WindowState = FormWindowState.Maximized,
                    RightToLeft = RightToLeft.Yes,
                    RightToLeftLayout = true,
                    Font = ArabicFontHelper.GetArabicFont(10F)
                };

                var advancedReportsControl = new AdvancedFinancialReportsControl(DatabaseService);
                advancedReportsControl.Dock = DockStyle.Fill;

                advancedReportsForm.Controls.Add(advancedReportsControl);
                advancedReportsForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التقارير المالية المتقدمة: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
