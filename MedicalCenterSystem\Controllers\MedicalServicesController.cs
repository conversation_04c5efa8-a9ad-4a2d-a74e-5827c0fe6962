using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MedicalCenterSystem.Data;
using MedicalCenterSystem.Models;

namespace MedicalCenterSystem.Controllers
{
    public class MedicalServicesController : Controller
    {
        private readonly ApplicationDbContext _context;

        public MedicalServicesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: MedicalServices
        public async Task<IActionResult> Index()
        {
            return View(await _context.MedicalServices.ToListAsync());
        }

        // GET: MedicalServices/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicalService = await _context.MedicalServices
                .FirstOrDefaultAsync(m => m.MedicalServiceId == id);
            if (medicalService == null)
            {
                return NotFound();
            }

            return View(medicalService);
        }

        // GET: MedicalServices/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: MedicalServices/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("MedicalServiceId,ServiceName,ServiceType,IsCenterService,DefaultPrice")] MedicalService medicalService)
        {
            if (ModelState.IsValid)
            {
                _context.Add(medicalService);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(medicalService);
        }

        // GET: MedicalServices/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicalService = await _context.MedicalServices.FindAsync(id);
            if (medicalService == null)
            {
                return NotFound();
            }
            return View(medicalService);
        }

        // POST: MedicalServices/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("MedicalServiceId,ServiceName,ServiceType,IsCenterService,DefaultPrice")] MedicalService medicalService)
        {
            if (id != medicalService.MedicalServiceId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(medicalService);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!MedicalServiceExists(medicalService.MedicalServiceId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(medicalService);
        }

        // GET: MedicalServices/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var medicalService = await _context.MedicalServices
                .FirstOrDefaultAsync(m => m.MedicalServiceId == id);
            if (medicalService == null)
            {
                return NotFound();
            }

            return View(medicalService);
        }

        // POST: MedicalServices/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var medicalService = await _context.MedicalServices.FindAsync(id);
            if (medicalService != null)
            {
                _context.MedicalServices.Remove(medicalService);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool MedicalServiceExists(int id)
        {
            return _context.MedicalServices.Any(e => e.MedicalServiceId == id);
        }
    }
}
