using System;
using System.Drawing;
using System.Windows.Forms;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Controls
{
    public partial class SettingsControl : UserControl
    {
        private readonly DatabaseService _databaseService;

        // UI Components
        private TabControl settingsTabControl;
        private TabPage generalTab;
        private TabPage databaseTab;
        private TabPage usersTab;
        private TabPage backupTab;

        // General Settings
        private TextBox txtCenterName;
        private TextBox txtCenterAddress;
        private TextBox txtCenterPhone;
        private ComboBox cmbLanguage;
        private ComboBox cmbTheme;

        // Database Settings
        private DatabaseSettingsControl databaseSettings;

        // User Management
        private DataGridView dgvUsers;
        private Button btnAddUser;
        private Button btnEditUser;
        private Button btnDeleteUser;

        // Backup Settings
        private Button btnBackupNow;
        private Button btnRestoreBackup;
        private CheckBox chkAutoBackup;
        private ComboBox cmbBackupFrequency;
        private TextBox txtBackupPath;

        public SettingsControl(DatabaseService databaseService)
        {
            _databaseService = databaseService;
            InitializeComponent();
            LoadSettings();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Configure main control
            this.BackColor = MaterialDesignHelper.Colors.Background;
            this.Dock = DockStyle.Fill;
            this.Padding = new Padding(20);
            this.RightToLeft = RightToLeft.Yes;

            InitializeTabControl();
            InitializeGeneralTab();
            InitializeDatabaseTab();
            InitializeUsersTab();
            InitializeBackupTab();

            this.ResumeLayout(false);
        }

        private void InitializeTabControl()
        {
            this.settingsTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = ArabicFontHelper.GetArabicFont(12F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            // Create tabs
            this.generalTab = new TabPage("⚙️ الإعدادات العامة");
            this.databaseTab = new TabPage("🗄️ إعدادات قاعدة البيانات");
            this.usersTab = new TabPage("👥 إدارة المستخدمين");
            this.backupTab = new TabPage("💾 النسخ الاحتياطي");

            settingsTabControl.TabPages.AddRange(new TabPage[] { generalTab, databaseTab, usersTab, backupTab });
            this.Controls.Add(settingsTabControl);
        }

        private void InitializeGeneralTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(30)
            };

            // Center Information Section
            var centerInfoGroup = new GroupBox
            {
                Text = "معلومات المركز",
                Size = new Size(500, 200),
                Location = new Point(30, 30),
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                RightToLeft = RightToLeft.Yes
            };

            var lblCenterName = new Label
            {
                Text = "اسم المركز:",
                Location = new Point(20, 40),
                AutoSize = true,
                Font = ArabicFontHelper.GetArabicFont(11F)
            };

            this.txtCenterName = new TextBox
            {
                Size = new Size(300, 25),
                Location = new Point(20, 65),
                Font = ArabicFontHelper.GetArabicFont(11F),
                Text = "المركز الطبي المتقدم"
            };

            var lblCenterAddress = new Label
            {
                Text = "عنوان المركز:",
                Location = new Point(20, 100),
                AutoSize = true,
                Font = ArabicFontHelper.GetArabicFont(11F)
            };

            this.txtCenterAddress = new TextBox
            {
                Size = new Size(300, 25),
                Location = new Point(20, 125),
                Font = ArabicFontHelper.GetArabicFont(11F),
                Text = "الرياض، المملكة العربية السعودية"
            };

            var lblCenterPhone = new Label
            {
                Text = "هاتف المركز:",
                Location = new Point(20, 160),
                AutoSize = true,
                Font = ArabicFontHelper.GetArabicFont(11F)
            };

            this.txtCenterPhone = new TextBox
            {
                Size = new Size(200, 25),
                Location = new Point(20, 185),
                Font = ArabicFontHelper.GetArabicFont(11F),
                Text = "+966 11 123 4567"
            };

            centerInfoGroup.Controls.AddRange(new Control[] 
            { 
                lblCenterName, txtCenterName, lblCenterAddress, txtCenterAddress, lblCenterPhone, txtCenterPhone 
            });

            // Application Settings Section
            var appSettingsGroup = new GroupBox
            {
                Text = "إعدادات التطبيق",
                Size = new Size(500, 150),
                Location = new Point(30, 250),
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                RightToLeft = RightToLeft.Yes
            };

            var lblLanguage = new Label
            {
                Text = "اللغة:",
                Location = new Point(20, 40),
                AutoSize = true,
                Font = ArabicFontHelper.GetArabicFont(11F)
            };

            this.cmbLanguage = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(20, 65),
                Font = ArabicFontHelper.GetArabicFont(11F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbLanguage.Items.AddRange(new[] { "العربية", "English" });
            cmbLanguage.SelectedIndex = 0;

            var lblTheme = new Label
            {
                Text = "المظهر:",
                Location = new Point(200, 40),
                AutoSize = true,
                Font = ArabicFontHelper.GetArabicFont(11F)
            };

            this.cmbTheme = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(200, 65),
                Font = ArabicFontHelper.GetArabicFont(11F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbTheme.Items.AddRange(new[] { "فاتح", "داكن", "تلقائي" });
            cmbTheme.SelectedIndex = 0;

            var btnSaveGeneral = MaterialDesignHelper.CreateModernButton("💾 حفظ الإعدادات", MaterialDesignHelper.Colors.Success);
            btnSaveGeneral.Location = new Point(20, 110);

            appSettingsGroup.Controls.AddRange(new Control[] 
            { 
                lblLanguage, cmbLanguage, lblTheme, cmbTheme, btnSaveGeneral 
            });

            panel.Controls.AddRange(new Control[] { centerInfoGroup, appSettingsGroup });
            generalTab.Controls.Add(panel);
        }

        private void InitializeDatabaseTab()
        {
            this.databaseSettings = new DatabaseSettingsControl(_databaseService)
            {
                Dock = DockStyle.Fill
            };

            databaseTab.Controls.Add(databaseSettings);
        }

        private void InitializeUsersTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(30)
            };

            // Users management section
            var usersGroup = new GroupBox
            {
                Text = "إدارة المستخدمين",
                Dock = DockStyle.Fill,
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                RightToLeft = RightToLeft.Yes,
                Padding = new Padding(20)
            };

            // Action buttons
            this.btnAddUser = MaterialDesignHelper.CreateModernButton("➕ إضافة مستخدم", MaterialDesignHelper.Colors.Success);
            this.btnEditUser = MaterialDesignHelper.CreateModernButton("✏️ تعديل", MaterialDesignHelper.Colors.Primary);
            this.btnDeleteUser = MaterialDesignHelper.CreateModernButton("🗑️ حذف", MaterialDesignHelper.Colors.Danger);

            btnAddUser.Location = new Point(20, 30);
            btnEditUser.Location = new Point(150, 30);
            btnDeleteUser.Location = new Point(280, 30);

            // Users DataGridView
            this.dgvUsers = new DataGridView
            {
                Size = new Size(usersGroup.Width - 40, usersGroup.Height - 100),
                Location = new Point(20, 80),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                Font = ArabicFontHelper.GetArabicFont(11F),
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            // Add columns
            dgvUsers.Columns.Add("Username", "اسم المستخدم");
            dgvUsers.Columns.Add("FullName", "الاسم الكامل");
            dgvUsers.Columns.Add("Role", "الدور");
            dgvUsers.Columns.Add("IsActive", "الحالة");
            dgvUsers.Columns.Add("LastLogin", "آخر دخول");

            usersGroup.Controls.AddRange(new Control[] { btnAddUser, btnEditUser, btnDeleteUser, dgvUsers });
            panel.Controls.Add(usersGroup);
            usersTab.Controls.Add(panel);
        }

        private void InitializeBackupTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(30)
            };

            // Backup settings section
            var backupGroup = new GroupBox
            {
                Text = "إعدادات النسخ الاحتياطي",
                Size = new Size(600, 300),
                Location = new Point(30, 30),
                Font = ArabicFontHelper.GetArabicFont(12F, FontStyle.Bold),
                RightToLeft = RightToLeft.Yes
            };

            // Auto backup settings
            this.chkAutoBackup = new CheckBox
            {
                Text = "تفعيل النسخ الاحتياطي التلقائي",
                Location = new Point(20, 40),
                AutoSize = true,
                Font = ArabicFontHelper.GetArabicFont(11F),
                Checked = true
            };

            var lblFrequency = new Label
            {
                Text = "تكرار النسخ:",
                Location = new Point(20, 80),
                AutoSize = true,
                Font = ArabicFontHelper.GetArabicFont(11F)
            };

            this.cmbBackupFrequency = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(20, 105),
                Font = ArabicFontHelper.GetArabicFont(11F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbBackupFrequency.Items.AddRange(new[] { "يومي", "أسبوعي", "شهري" });
            cmbBackupFrequency.SelectedIndex = 0;

            var lblBackupPath = new Label
            {
                Text = "مسار النسخ الاحتياطي:",
                Location = new Point(20, 140),
                AutoSize = true,
                Font = ArabicFontHelper.GetArabicFont(11F)
            };

            this.txtBackupPath = new TextBox
            {
                Size = new Size(400, 25),
                Location = new Point(20, 165),
                Font = ArabicFontHelper.GetArabicFont(11F),
                Text = @"C:\MedicalCenter\Backups"
            };

            // Action buttons
            this.btnBackupNow = MaterialDesignHelper.CreateModernButton("💾 إنشاء نسخة احتياطية الآن", MaterialDesignHelper.Colors.Primary);
            this.btnRestoreBackup = MaterialDesignHelper.CreateModernButton("📥 استعادة نسخة احتياطية", MaterialDesignHelper.Colors.Warning);

            btnBackupNow.Location = new Point(20, 210);
            btnRestoreBackup.Location = new Point(220, 210);

            backupGroup.Controls.AddRange(new Control[] 
            { 
                chkAutoBackup, lblFrequency, cmbBackupFrequency, lblBackupPath, txtBackupPath, btnBackupNow, btnRestoreBackup 
            });

            panel.Controls.Add(backupGroup);
            backupTab.Controls.Add(panel);
        }

        private void LoadSettings()
        {
            // Load current settings from database or configuration file
            // This is where you would load actual settings
        }
    }
}
