# نظام إدارة المركز الطبي - Windows Forms

نظام شامل لإدارة المراكز الطبية مبني باستخدام Windows Forms مع Entity Framework Core وSQL Server، مع دعم الشبكة الداخلية.

## الميزات الرئيسية

### 🏥 إدارة المراجعين
- تسجيل المراجعين مع ترقيم تلقائي حسب الطبيب والتاريخ
- تتبع معلومات المريض الكاملة (الاسم، العمر، المحافظة، الهاتف)
- ربط المراجع بالطبيب المختص
- تسجيل التشخيص وعدد الزيارة
- بحث متقدم في المراجعين

### 👨‍⚕️ إدارة الأطباء
- إضافة وإدارة الأطباء والتخصصات
- تفعيل/إلغاء تفعيل الأطباء
- ربط الأطباء بالخدمات الطبية (قيد التطوير)
- بحث في الأطباء بالاسم والتخصص

### 🏥 الخدمات الطبية
- إدارة الخدمات الطبية (كشفية، فحوصات، تحاليل، أشعة)
- تصنيف الخدمات (مباشرة أو تحويل)
- تحديد الأسعار الافتراضية
- تمييز خدمات المركز عن الخدمات الخارجية

### 💰 نظام المدفوعات (قيد التطوير)
- المدفوعات الرئيسية
- مدفوعات التحويلات
- حساب حصص الأطباء والمركز

### 📊 نظام التقارير (قيد التطوير)
- تقارير الوارد والتحويلات
- تقارير الأطباء
- التقارير اليومية

### 🌐 دعم الشبكة الداخلية
- إمكانية الاتصال بخادم SQL Server على الشبكة
- إعدادات مرنة للاتصال (محلي أو شبكة)
- اختبار الاتصال قبل الحفظ
- تبديل سهل بين قواعد البيانات

### 🔐 نظام المستخدمين
- تسجيل دخول آمن
- أدوار مختلفة (مدير، استقبال، كاشير)
- إدارة الصلاحيات حسب الدور
- تتبع آخر تسجيل دخول

## التقنيات المستخدمة

- **Frontend**: Windows Forms (.NET 9.0)
- **Backend**: Entity Framework Core
- **Database**: SQL Server / SQL Server Express
- **UI**: واجهة باللغة العربية مع دعم RTL
- **Network**: دعم الشبكة الداخلية

## متطلبات التشغيل

- .NET 9.0 Runtime
- SQL Server Express أو SQL Server (للشبكة)
- Windows 10/11
- ذاكرة: 512 MB RAM كحد أدنى
- مساحة القرص: 100 MB

## التثبيت والتشغيل

### التشغيل المحلي (LocalDB)
1. **تحميل المشروع**
```bash
git clone [repository-url]
cd MedicalCenterWinForms
```

2. **بناء المشروع**
```bash
dotnet build
```

3. **تشغيل التطبيق**
```bash
dotnet run
```

### إعداد الشبكة الداخلية

#### على الخادم (Server):
1. **تثبيت SQL Server Express**
   - تحميل وتثبيت SQL Server Express
   - تفعيل TCP/IP في SQL Server Configuration Manager
   - إعداد Mixed Mode Authentication

2. **إعداد قاعدة البيانات**
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE MedicalCenterDB;

-- إنشاء مستخدم للشبكة
CREATE LOGIN medical_user WITH PASSWORD = 'YourStrongPassword123!';
USE MedicalCenterDB;
CREATE USER medical_user FOR LOGIN medical_user;
ALTER ROLE db_owner ADD MEMBER medical_user;
```

3. **إعداد Firewall**
   - فتح Port 1433 في Windows Firewall
   - السماح لـ SQL Server في Firewall

#### على أجهزة العمل (Clients):
1. **تشغيل التطبيق**
2. **الضغط على "إعدادات" في شاشة تسجيل الدخول**
3. **تفعيل "استخدام قاعدة بيانات الشبكة"**
4. **إدخال معلومات الخادم:**
   - اسم الخادم: `SERVER-NAME\SQLEXPRESS`
   - اسم قاعدة البيانات: `MedicalCenterDB`
   - اسم المستخدم: `medical_user`
   - كلمة المرور: `YourStrongPassword123!`
5. **اختبار الاتصال والحفظ**

## الحسابات الافتراضية

| اسم المستخدم | كلمة المرور | الدور |
|---------------|-------------|-------|
| admin | admin123 | مدير |
| reception | reception123 | استقبال |
| cashier | cashier123 | كاشير |

## هيكل قاعدة البيانات

### الجداول الرئيسية
- **Doctors**: معلومات الأطباء
- **MedicalServices**: الخدمات الطبية
- **DoctorServices**: ربط الأطباء بالخدمات والنسب
- **PatientVisits**: المراجعين
- **MainPayments**: المدفوعات الرئيسية
- **ReferralPayments**: مدفوعات التحويلات
- **UserAccounts**: حسابات المستخدمين

## الميزات المتقدمة

### ترقيم المراجعين التلقائي
- يتم إنشاء رقم مراجع تلقائياً لكل طبيب يومياً
- يبدأ الترقيم من 1 لكل طبيب في كل يوم جديد
- ضمان عدم تكرار الأرقام

### إدارة الشبكة
- تبديل سهل بين الوضع المحلي والشبكة
- اختبار الاتصال المباشر
- حفظ إعدادات الاتصال
- رسائل خطأ واضحة

### واجهة المستخدم
- تصميم عصري ومريح للعين
- دعم كامل للغة العربية
- أزرار واضحة ومنظمة
- بحث سريع وفعال

## استكشاف الأخطاء

### مشاكل الاتصال بقاعدة البيانات
1. **تأكد من تشغيل SQL Server**
2. **تحقق من إعدادات Firewall**
3. **تأكد من صحة معلومات الاتصال**
4. **استخدم اختبار الاتصال في الإعدادات**

### مشاكل الأداء
1. **تأكد من سرعة الشبكة**
2. **تحقق من موارد الخادم**
3. **أعد تشغيل التطبيق**

## الخطط المستقبلية

- [ ] إكمال نظام المدفوعات
- [ ] إضافة نظام التقارير المتقدم
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] تصدير البيانات إلى Excel
- [ ] نظام الإشعارات
- [ ] واجهة ويب مكملة

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الدعم الفني

للحصول على الدعم:
- إنشاء Issue في المستودع
- التواصل مع فريق التطوير
- مراجعة الوثائق

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**تم تطوير هذا النظام لتسهيل إدارة المراكز الطبية وتحسين كفاءة العمل مع دعم الشبكة الداخلية.**
