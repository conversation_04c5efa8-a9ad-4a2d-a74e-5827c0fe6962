using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Controls
{
    public partial class PatientEditControl : BaseUserControl
    {
        private readonly PatientVisit? _patientVisit;
        private readonly bool _isEditMode;
        private List<Doctor> _doctors = new List<Doctor>();

        public event EventHandler? PatientSaved;

        public PatientEditControl() : base()
        {
            InitializeComponent();
            _isEditMode = false;
            LoadDoctors();
            InitializeForm();
        }

        public PatientEditControl(DatabaseService databaseService, PatientVisit? patientVisit = null) : base(databaseService)
        {
            InitializeComponent();
            _patientVisit = patientVisit;
            _isEditMode = patientVisit != null;
            LoadDoctors();
            InitializeForm();
        }

        private async void LoadDoctors()
        {
            try
            {
                using var context = DatabaseService.GetDbContext();
                _doctors = await context.Doctors.Where(d => d.IsActive).OrderBy(d => d.FullName).ToListAsync();
                
                cmbDoctor.DataSource = _doctors;
                cmbDoctor.DisplayMember = "FullName";
                cmbDoctor.ValueMember = "DoctorId";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل الأطباء: {ex.Message}");
            }
        }

        private void InitializeForm()
        {
            // Initialize visit count options
            cmbVisitCount.Items.AddRange(new[] { "أولى", "ثانية", "ثالثة", "رابعة", "متابعة" });

            if (_isEditMode && _patientVisit != null)
            {
                txtPatientName.Text = _patientVisit.PatientName;
                dtpVisitDate.Value = _patientVisit.VisitDate;
                cmbDoctor.SelectedValue = _patientVisit.DoctorId;
                numAge.Value = _patientVisit.Age;
                txtProvince.Text = _patientVisit.Province;
                txtBookingStaff.Text = _patientVisit.BookingStaff;
                cmbVisitCount.Text = _patientVisit.VisitCountLabel;
                txtPhoneNumber.Text = _patientVisit.PhoneNumber;
                txtDiagnosis.Text = _patientVisit.Diagnosis;
                numVisitNumber.Value = _patientVisit.VisitNumber;
                numVisitNumber.Enabled = false;
            }
            else
            {
                dtpVisitDate.Value = DateTime.Today;
                numVisitNumber.Enabled = false;
            }

            txtPatientName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                SetLoadingState(true);
                btnSave.Text = "جاري الحفظ...";

                using var context = DatabaseService.GetDbContext();

                if (_isEditMode && _patientVisit != null)
                {
                    var existingVisit = await context.PatientVisits.FindAsync(_patientVisit.PatientVisitId);
                    if (existingVisit != null)
                    {
                        existingVisit.PatientName = txtPatientName.Text.Trim();
                        existingVisit.VisitDate = dtpVisitDate.Value.Date;
                        existingVisit.DoctorId = (int)cmbDoctor.SelectedValue!;
                        existingVisit.Age = (int)numAge.Value;
                        existingVisit.Province = txtProvince.Text.Trim();
                        existingVisit.BookingStaff = txtBookingStaff.Text.Trim();
                        existingVisit.VisitCountLabel = cmbVisitCount.Text;
                        existingVisit.PhoneNumber = txtPhoneNumber.Text.Trim();
                        existingVisit.Diagnosis = txtDiagnosis.Text.Trim();
                    }
                }
                else
                {
                    var doctorId = (int)cmbDoctor.SelectedValue!;
                    var visitDate = dtpVisitDate.Value.Date;
                    
                    var maxVisitNumber = await context.PatientVisits
                        .Where(pv => pv.DoctorId == doctorId && pv.VisitDate.Date == visitDate)
                        .MaxAsync(pv => (int?)pv.VisitNumber) ?? 0;

                    var newVisit = new PatientVisit
                    {
                        PatientName = txtPatientName.Text.Trim(),
                        VisitDate = visitDate,
                        DoctorId = doctorId,
                        VisitNumber = maxVisitNumber + 1,
                        Age = (int)numAge.Value,
                        Province = txtProvince.Text.Trim(),
                        BookingStaff = txtBookingStaff.Text.Trim(),
                        VisitCountLabel = cmbVisitCount.Text,
                        PhoneNumber = txtPhoneNumber.Text.Trim(),
                        Diagnosis = txtDiagnosis.Text.Trim()
                    };

                    context.PatientVisits.Add(newVisit);
                }

                await context.SaveChangesAsync();

                ShowSuccess("تم حفظ البيانات بنجاح");
                PatientSaved?.Invoke(this, EventArgs.Empty);
                
                this.ParentForm?.Close();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
                btnSave.Text = "حفظ";
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.ParentForm?.Close();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtPatientName.Text))
            {
                ShowWarning("يرجى إدخال اسم المريض");
                txtPatientName.Focus();
                return false;
            }

            if (cmbDoctor.SelectedValue == null)
            {
                ShowWarning("يرجى اختيار الطبيب");
                cmbDoctor.Focus();
                return false;
            }

            if (numAge.Value <= 0)
            {
                ShowWarning("يرجى إدخال عمر صحيح");
                numAge.Focus();
                return false;
            }

            return true;
        }

        private async void cmbDoctor_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!_isEditMode && cmbDoctor.SelectedValue != null)
            {
                try
                {
                    using var context = DatabaseService.GetDbContext();
                    var doctorId = (int)cmbDoctor.SelectedValue;
                    var visitDate = dtpVisitDate.Value.Date;
                    
                    var maxVisitNumber = await context.PatientVisits
                        .Where(pv => pv.DoctorId == doctorId && pv.VisitDate.Date == visitDate)
                        .MaxAsync(pv => (int?)pv.VisitNumber) ?? 0;

                    numVisitNumber.Value = maxVisitNumber + 1;
                }
                catch
                {
                    // Ignore errors in preview
                }
            }
        }

        private void dtpVisitDate_ValueChanged(object sender, EventArgs e)
        {
            cmbDoctor_SelectedIndexChanged(sender, e);
        }
    }

    public partial class PatientEditControl
    {
        private TextBox txtPatientName;
        private ComboBox cmbDoctor;
        private DateTimePicker dtpVisitDate;
        private NumericUpDown numAge;
        private NumericUpDown numVisitNumber;
        private TextBox txtProvince;
        private TextBox txtBookingStaff;
        private ComboBox cmbVisitCount;
        private TextBox txtPhoneNumber;
        private TextBox txtDiagnosis;
        private Button btnSave;
        private Button btnCancel;
        private Panel pnlMain;
        private Panel pnlButtons;

        private void InitializeComponent()
        {
            this.txtPatientName = CreateStyledTextBox();
            this.cmbDoctor = CreateStyledComboBox();
            this.txtProvince = CreateStyledTextBox();
            this.txtBookingStaff = CreateStyledTextBox();
            this.cmbVisitCount = CreateStyledComboBox();
            this.txtPhoneNumber = CreateStyledTextBox();
            this.txtDiagnosis = CreateStyledTextBox();
            this.pnlMain = CreateStyledPanel();
            this.pnlButtons = CreateStyledPanel();

            this.dtpVisitDate = new DateTimePicker
            {
                Font = ArabicFontHelper.GetArabicFont(10F),
                Format = DateTimePickerFormat.Short,
                RightToLeft = RightToLeft.Yes
            };

            this.numAge = new NumericUpDown
            {
                Font = ArabicFontHelper.GetArabicFont(10F),
                Minimum = 1,
                Maximum = 150,
                Value = 25
            };

            this.numVisitNumber = new NumericUpDown
            {
                Font = ArabicFontHelper.GetArabicFont(10F),
                Minimum = 1,
                Maximum = 999,
                Value = 1,
                ReadOnly = true
            };

            this.btnSave = CreateStyledButton("حفظ", Color.FromArgb(46, 204, 113), btnSave_Click);
            this.btnCancel = CreateStyledButton("إلغاء", Color.FromArgb(231, 76, 60), btnCancel_Click);

            this.SuspendLayout();

            // pnlMain
            this.pnlMain.Controls.Add(CreateStyledLabel("اسم المريض:", true));
            this.pnlMain.Controls.Add(this.txtPatientName);
            this.pnlMain.Controls.Add(CreateStyledLabel("الطبيب:", true));
            this.pnlMain.Controls.Add(this.cmbDoctor);
            this.pnlMain.Controls.Add(CreateStyledLabel("تاريخ الزيارة:", true));
            this.pnlMain.Controls.Add(this.dtpVisitDate);
            this.pnlMain.Controls.Add(CreateStyledLabel("العمر:", true));
            this.pnlMain.Controls.Add(this.numAge);
            this.pnlMain.Controls.Add(CreateStyledLabel("رقم المراجع:", true));
            this.pnlMain.Controls.Add(this.numVisitNumber);
            this.pnlMain.Controls.Add(CreateStyledLabel("المحافظة:", true));
            this.pnlMain.Controls.Add(this.txtProvince);
            this.pnlMain.Controls.Add(CreateStyledLabel("موظف الحجز:", true));
            this.pnlMain.Controls.Add(this.txtBookingStaff);
            this.pnlMain.Controls.Add(CreateStyledLabel("عدد الزيارة:", true));
            this.pnlMain.Controls.Add(this.cmbVisitCount);
            this.pnlMain.Controls.Add(CreateStyledLabel("رقم الهاتف:", true));
            this.pnlMain.Controls.Add(this.txtPhoneNumber);
            this.pnlMain.Controls.Add(CreateStyledLabel("التشخيص:", true));
            this.pnlMain.Controls.Add(this.txtDiagnosis);
            this.pnlMain.Dock = DockStyle.Fill;

            // Controls positioning
            var labels = this.pnlMain.Controls.OfType<Label>().ToArray();
            var controls = new Control[] { txtPatientName, cmbDoctor, dtpVisitDate, numAge, numVisitNumber,
                                         txtProvince, txtBookingStaff, cmbVisitCount, txtPhoneNumber, txtDiagnosis };

            for (int i = 0; i < labels.Length && i < controls.Length; i++)
            {
                int row = i / 2;
                int col = i % 2;
                int x = 30 + col * 320;
                int y = 30 + row * 70;

                labels[i].Location = new Point(x, y);
                controls[i].Location = new Point(x, y + 25);
                controls[i].Size = new Size(i == 9 ? 610 : 280, controls[i] is TextBox && i == 9 ? 80 : 25);

                if (i == 9) // txtDiagnosis
                {
                    ((TextBox)controls[i]).Multiline = true;
                    ((TextBox)controls[i]).ScrollBars = ScrollBars.Vertical;
                }
            }

            // Event handlers
            this.cmbDoctor.SelectedIndexChanged += cmbDoctor_SelectedIndexChanged;
            this.dtpVisitDate.ValueChanged += dtpVisitDate_ValueChanged;

            // pnlButtons
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Height = 60;

            this.btnSave.Location = new Point(450, 10);
            this.btnCancel.Location = new Point(560, 10);

            // PatientEditControl
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlButtons);
            this.Name = "PatientEditControl";
            this.Size = new Size(680, 550);

            this.ResumeLayout(false);
        }
    }
}
