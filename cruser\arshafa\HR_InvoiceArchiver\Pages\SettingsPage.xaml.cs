using System;
using System.Windows;
using System.Windows.Controls;
using System.Threading.Tasks;
using Microsoft.Win32;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Models;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Pages
{
    public partial class SettingsPage : UserControl, INavigationAware
    {
        private readonly ISettingsService _settingsService;
        private readonly IToastService _toastService;
        private readonly ILoggingService _loggingService;
        private SettingsModel _currentSettings;
        private bool _isLoading = false;

        public SettingsPage()
        {
            InitializeComponent();

            // الحصول على الخدمات من DI Container
            var serviceProvider = App.ServiceProvider;
            _settingsService = serviceProvider.GetRequiredService<ISettingsService>();
            _toastService = serviceProvider.GetRequiredService<IToastService>();
            _loggingService = serviceProvider.GetRequiredService<ILoggingService>();

            _currentSettings = new SettingsModel();
        }

        public async void OnNavigatedTo(object parameter)
        {
            await LoadSettingsAsync();
            await LoadSystemInfoAsync();
        }

        public void OnNavigatedFrom()
        {
            // Cleanup when leaving this page
        }

        private async Task LoadSettingsAsync()
        {
            try
            {
                _isLoading = true;
                _currentSettings = await _settingsService.LoadSettingsAsync();
                PopulateUIFromSettings();
                await _loggingService.LogInformationAsync("تم تحميل صفحة الإعدادات");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "فشل في تحميل الإعدادات");
                await _loggingService.LogErrorAsync("فشل في تحميل الإعدادات", ex);
            }
            finally
            {
                _isLoading = false;
            }
        }

        private void PopulateUIFromSettings()
        {
            // General Settings
            ApplicationNameTextBox.Text = _currentSettings.ApplicationName;
            CompanyNameTextBox.Text = _currentSettings.CompanyName;
            CompanyAddressTextBox.Text = _currentSettings.CompanyAddress;
            CompanyPhoneTextBox.Text = _currentSettings.CompanyPhone;
            CompanyEmailTextBox.Text = _currentSettings.CompanyEmail;

            // UI Settings
            SetComboBoxValue(LanguageComboBox, _currentSettings.ApplicationLanguage);
            SetComboBoxValue(ThemeComboBox, _currentSettings.ApplicationTheme);
            EnableNotificationsCheckBox.IsChecked = _currentSettings.EnableNotifications;
            EnableSoundsCheckBox.IsChecked = _currentSettings.EnableSounds;

            // Database Settings
            DatabasePathTextBox.Text = _currentSettings.DatabasePath;
            EnableDatabaseBackupCheckBox.IsChecked = _currentSettings.EnableDatabaseBackup;
            EnableDatabaseEncryptionCheckBox.IsChecked = _currentSettings.EnableDatabaseEncryption;
            BackupIntervalTextBox.Text = _currentSettings.BackupIntervalHours.ToString();
            MaxBackupFilesTextBox.Text = _currentSettings.MaxBackupFiles.ToString();

            // Cloud Settings
            EnableCloudSyncCheckBox.IsChecked = _currentSettings.EnableCloudSync;
            SetComboBoxValue(CloudProviderComboBox, _currentSettings.CloudProvider);
            CloudCredentialsPathTextBox.Text = _currentSettings.CloudCredentialsPath;
            SyncIntervalTextBox.Text = _currentSettings.SyncIntervalMinutes.ToString();
            SyncOnStartupCheckBox.IsChecked = _currentSettings.SyncOnStartup;
            SyncOnShutdownCheckBox.IsChecked = _currentSettings.SyncOnShutdown;

            // Security Settings
            EnableAuditLogCheckBox.IsChecked = _currentSettings.EnableAuditLog;
            EnableDataEncryptionCheckBox.IsChecked = _currentSettings.EnableDataEncryption;
            SessionTimeoutTextBox.Text = _currentSettings.SessionTimeoutMinutes.ToString();
            MaxLoginAttemptsTextBox.Text = _currentSettings.MaxLoginAttempts.ToString();
            RequirePasswordOnStartupCheckBox.IsChecked = _currentSettings.RequirePasswordOnStartup;
            LockoutDurationTextBox.Text = _currentSettings.LockoutDurationMinutes.ToString();

            // Performance Settings
            EnablePerformanceMonitoringCheckBox.IsChecked = _currentSettings.EnablePerformanceMonitoring;
            EnableCachingCheckBox.IsChecked = _currentSettings.EnableCaching;
            MaxLogEntriesTextBox.Text = _currentSettings.MaxLogEntries.ToString();
            LogRetentionDaysTextBox.Text = _currentSettings.LogRetentionDays.ToString();
            CacheExpirationTextBox.Text = _currentSettings.CacheExpirationMinutes.ToString();
            EnableLazyLoadingCheckBox.IsChecked = _currentSettings.EnableLazyLoading;
        }

        private void SetComboBoxValue(ComboBox comboBox, string value)
        {
            foreach (ComboBoxItem item in comboBox.Items)
            {
                if (item.Tag?.ToString() == value)
                {
                    comboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private async Task LoadSystemInfoAsync()
        {
            try
            {
                var systemInfo = await _settingsService.GetSystemInfoAsync();
                PopulateSystemInfo(systemInfo);
            }
            catch (Exception ex)
            {
                await _loggingService.LogErrorAsync("فشل في تحميل معلومات النظام", ex);
            }
        }

        private void PopulateSystemInfo(SystemInfo systemInfo)
        {
            SystemInfoGrid.Children.Clear();
            SystemInfoGrid.RowDefinitions.Clear();

            var infoItems = new[]
            {
                ("إصدار التطبيق", systemInfo.ApplicationVersion),
                ("إصدار قاعدة البيانات", systemInfo.DatabaseVersion),
                ("حجم قاعدة البيانات", FormatFileSize(systemInfo.DatabaseSize)),
                ("مسار قاعدة البيانات", systemInfo.DatabasePath),
                ("إجمالي الفواتير", systemInfo.TotalInvoices.ToString()),
                ("إجمالي المدفوعات", systemInfo.TotalPayments.ToString()),
                ("إجمالي الموردين", systemInfo.TotalSuppliers.ToString()),
                ("استخدام الذاكرة", FormatFileSize(systemInfo.MemoryUsage)),
                ("مدة التشغيل", FormatTimeSpan(systemInfo.Uptime)),
                ("نظام التشغيل", systemInfo.OperatingSystem),
                ("إصدار .NET", systemInfo.DotNetVersion)
            };

            for (int i = 0; i < infoItems.Length; i++)
            {
                SystemInfoGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                var labelTextBlock = new TextBlock
                {
                    Text = infoItems[i].Item1 + ":",
                    FontWeight = FontWeights.Medium,
                    Margin = new Thickness(0, 8, 16, 8),
                    VerticalAlignment = VerticalAlignment.Center
                };
                Grid.SetRow(labelTextBlock, i);
                Grid.SetColumn(labelTextBlock, 0);

                var valueTextBlock = new TextBlock
                {
                    Text = infoItems[i].Item2,
                    Margin = new Thickness(0, 8, 0, 8),
                    VerticalAlignment = VerticalAlignment.Center
                };
                Grid.SetRow(valueTextBlock, i);
                Grid.SetColumn(valueTextBlock, 1);

                SystemInfoGrid.Children.Add(labelTextBlock);
                SystemInfoGrid.Children.Add(valueTextBlock);
            }

            // إضافة أعمدة للشبكة
            if (SystemInfoGrid.ColumnDefinitions.Count == 0)
            {
                SystemInfoGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                SystemInfoGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            }
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 بايت";

            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }

            return $"{size:0.##} {sizes[order]}";
        }

        private string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
                return $"{(int)timeSpan.TotalDays} يوم، {timeSpan.Hours} ساعة";
            else if (timeSpan.TotalHours >= 1)
                return $"{timeSpan.Hours} ساعة، {timeSpan.Minutes} دقيقة";
            else
                return $"{timeSpan.Minutes} دقيقة، {timeSpan.Seconds} ثانية";
        }

        #region Event Handlers

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isLoading) return;

            try
            {
                _isLoading = true;
                SaveButton.IsEnabled = false;

                // تحديث النموذج من واجهة المستخدم
                UpdateSettingsFromUI();

                // التحقق من صحة الإعدادات
                var validation = await _settingsService.ValidateSettingsAsync(_currentSettings);
                if (!validation.IsValid)
                {
                    _toastService.ShowError("خطأ في التحقق", string.Join("\n", validation.Errors));
                    return;
                }

                // حفظ الإعدادات
                await _settingsService.SaveSettingsAsync(_currentSettings);

                _toastService.ShowSuccess("نجح الحفظ", "تم حفظ الإعدادات بنجاح");
                await _loggingService.LogInformationAsync("تم حفظ إعدادات النظام");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "فشل في حفظ الإعدادات");
                await _loggingService.LogErrorAsync("فشل في حفظ الإعدادات", ex);
            }
            finally
            {
                _isLoading = false;
                SaveButton.IsEnabled = true;
            }
        }

        private async void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadSettingsAsync();
            _toastService.ShowInfo("تم الإلغاء", "تم إلغاء التغييرات");
        }

        private async void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\nسيتم فقدان جميع الإعدادات الحالية.",
                "تأكيد إعادة التعيين",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _settingsService.ResetToDefaultsAsync();
                    await LoadSettingsAsync();
                    _toastService.ShowSuccess("تم إعادة التعيين", "تم إعادة تعيين الإعدادات إلى القيم الافتراضية");
                    await _loggingService.LogInformationAsync("تم إعادة تعيين إعدادات النظام");
                }
                catch (Exception ex)
                {
                    _toastService.ShowError("خطأ", "فشل في إعادة تعيين الإعدادات");
                    await _loggingService.LogErrorAsync("فشل في إعادة تعيين الإعدادات", ex);
                }
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "تصدير الإعدادات",
                    Filter = "ملفات JSON (*.json)|*.json|جميع الملفات (*.*)|*.*",
                    DefaultExt = "json",
                    FileName = $"settings-export-{DateTime.Now:yyyy-MM-dd}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var exportPath = await _settingsService.ExportSettingsAsync(saveFileDialog.FileName);
                    _toastService.ShowSuccess("تم التصدير", $"تم تصدير الإعدادات إلى:\n{exportPath}");
                    await _loggingService.LogInformationAsync($"تم تصدير الإعدادات إلى: {exportPath}");
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "فشل في تصدير الإعدادات");
                await _loggingService.LogErrorAsync("فشل في تصدير الإعدادات", ex);
            }
        }

        private async void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "استيراد الإعدادات",
                    Filter = "ملفات JSON (*.json)|*.json|جميع الملفات (*.*)|*.*",
                    DefaultExt = "json"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var result = MessageBox.Show(
                        "هل أنت متأكد من استيراد الإعدادات؟\nسيتم استبدال الإعدادات الحالية.",
                        "تأكيد الاستيراد",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        var success = await _settingsService.ImportSettingsAsync(openFileDialog.FileName);
                        if (success)
                        {
                            await LoadSettingsAsync();
                            _toastService.ShowSuccess("تم الاستيراد", "تم استيراد الإعدادات بنجاح");
                            await _loggingService.LogInformationAsync($"تم استيراد الإعدادات من: {openFileDialog.FileName}");
                        }
                        else
                        {
                            _toastService.ShowError("خطأ", "فشل في استيراد الإعدادات");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "فشل في استيراد الإعدادات");
                await _loggingService.LogErrorAsync("فشل في استيراد الإعدادات", ex);
            }
        }

        private void BrowseDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار ملف قاعدة البيانات",
                Filter = "ملفات قاعدة البيانات (*.db;*.sqlite)|*.db;*.sqlite|جميع الملفات (*.*)|*.*",
                DefaultExt = "db"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                DatabasePathTextBox.Text = openFileDialog.FileName;
            }
        }

        private void BrowseCredentialsButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختيار ملف بيانات الاعتماد",
                Filter = "ملفات JSON (*.json)|*.json|جميع الملفات (*.*)|*.*",
                DefaultExt = "json"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                CloudCredentialsPathTextBox.Text = openFileDialog.FileName;
            }
        }

        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                TestConnectionButton.IsEnabled = false;
                _toastService.ShowInfo("جاري الاختبار", "جاري اختبار الاتصالات...");

                // اختبار قاعدة البيانات
                // يمكن إضافة منطق اختبار الاتصال هنا

                await Task.Delay(2000); // محاكاة اختبار الاتصال

                _toastService.ShowSuccess("نجح الاختبار", "تم اختبار جميع الاتصالات بنجاح");
                await _loggingService.LogInformationAsync("تم اختبار اتصالات النظام");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("فشل الاختبار", "فشل في اختبار بعض الاتصالات");
                await _loggingService.LogErrorAsync("فشل في اختبار اتصالات النظام", ex);
            }
            finally
            {
                TestConnectionButton.IsEnabled = true;
            }
        }

        private async void BackupNowButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                BackupNowButton.IsEnabled = false;
                _toastService.ShowInfo("جاري النسخ", "جاري إنشاء نسخة احتياطية...");

                var backupPath = await _settingsService.CreateBackupAsync();

                _toastService.ShowSuccess("تم النسخ الاحتياطي", $"تم إنشاء نسخة احتياطية في:\n{backupPath}");
                await _loggingService.LogInformationAsync($"تم إنشاء نسخة احتياطية من الإعدادات: {backupPath}");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ", "فشل في إنشاء النسخة الاحتياطية");
                await _loggingService.LogErrorAsync("فشل في إنشاء نسخة احتياطية من الإعدادات", ex);
            }
            finally
            {
                BackupNowButton.IsEnabled = true;
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateSettingsFromUI()
        {
            // General Settings
            _currentSettings.ApplicationName = ApplicationNameTextBox.Text;
            _currentSettings.CompanyName = CompanyNameTextBox.Text;
            _currentSettings.CompanyAddress = CompanyAddressTextBox.Text;
            _currentSettings.CompanyPhone = CompanyPhoneTextBox.Text;
            _currentSettings.CompanyEmail = CompanyEmailTextBox.Text;

            // UI Settings
            _currentSettings.ApplicationLanguage = GetComboBoxValue(LanguageComboBox);
            _currentSettings.ApplicationTheme = GetComboBoxValue(ThemeComboBox);
            _currentSettings.EnableNotifications = EnableNotificationsCheckBox.IsChecked ?? false;
            _currentSettings.EnableSounds = EnableSoundsCheckBox.IsChecked ?? false;

            // Database Settings
            _currentSettings.DatabasePath = DatabasePathTextBox.Text;
            _currentSettings.EnableDatabaseBackup = EnableDatabaseBackupCheckBox.IsChecked ?? false;
            _currentSettings.EnableDatabaseEncryption = EnableDatabaseEncryptionCheckBox.IsChecked ?? false;

            if (int.TryParse(BackupIntervalTextBox.Text, out int backupInterval))
                _currentSettings.BackupIntervalHours = backupInterval;

            if (int.TryParse(MaxBackupFilesTextBox.Text, out int maxBackupFiles))
                _currentSettings.MaxBackupFiles = maxBackupFiles;

            // Cloud Settings
            _currentSettings.EnableCloudSync = EnableCloudSyncCheckBox.IsChecked ?? false;
            _currentSettings.CloudProvider = GetComboBoxValue(CloudProviderComboBox);
            _currentSettings.CloudCredentialsPath = CloudCredentialsPathTextBox.Text;

            if (int.TryParse(SyncIntervalTextBox.Text, out int syncInterval))
                _currentSettings.SyncIntervalMinutes = syncInterval;

            _currentSettings.SyncOnStartup = SyncOnStartupCheckBox.IsChecked ?? false;
            _currentSettings.SyncOnShutdown = SyncOnShutdownCheckBox.IsChecked ?? false;

            // Security Settings
            _currentSettings.EnableAuditLog = EnableAuditLogCheckBox.IsChecked ?? false;
            _currentSettings.EnableDataEncryption = EnableDataEncryptionCheckBox.IsChecked ?? false;
            _currentSettings.RequirePasswordOnStartup = RequirePasswordOnStartupCheckBox.IsChecked ?? false;

            if (int.TryParse(SessionTimeoutTextBox.Text, out int sessionTimeout))
                _currentSettings.SessionTimeoutMinutes = sessionTimeout;

            if (int.TryParse(MaxLoginAttemptsTextBox.Text, out int maxLoginAttempts))
                _currentSettings.MaxLoginAttempts = maxLoginAttempts;

            if (int.TryParse(LockoutDurationTextBox.Text, out int lockoutDuration))
                _currentSettings.LockoutDurationMinutes = lockoutDuration;

            // Performance Settings
            _currentSettings.EnablePerformanceMonitoring = EnablePerformanceMonitoringCheckBox.IsChecked ?? false;
            _currentSettings.EnableCaching = EnableCachingCheckBox.IsChecked ?? false;
            _currentSettings.EnableLazyLoading = EnableLazyLoadingCheckBox.IsChecked ?? false;

            if (int.TryParse(MaxLogEntriesTextBox.Text, out int maxLogEntries))
                _currentSettings.MaxLogEntries = maxLogEntries;

            if (int.TryParse(LogRetentionDaysTextBox.Text, out int logRetentionDays))
                _currentSettings.LogRetentionDays = logRetentionDays;

            if (int.TryParse(CacheExpirationTextBox.Text, out int cacheExpiration))
                _currentSettings.CacheExpirationMinutes = cacheExpiration;
        }

        private string GetComboBoxValue(ComboBox comboBox)
        {
            return (comboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? string.Empty;
        }

        #endregion
    }
}
