@model MedicalCenterSystem.Models.PatientVisit

@{
    ViewData["Title"] = "تسجيل مراجع جديد";
}

<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="PatientName" class="form-label"></label>
                                <input asp-for="PatientName" class="form-control" placeholder="أدخل اسم المريض" />
                                <span asp-validation-for="PatientName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="DoctorId" class="form-label">الطبيب</label>
                                <select asp-for="DoctorId" class="form-select" asp-items="ViewBag.DoctorId">
                                    <option value="">-- اختر الطبيب --</option>
                                </select>
                                <span asp-validation-for="DoctorId" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="VisitDate" class="form-label"></label>
                                <input asp-for="VisitDate" class="form-control" type="date" />
                                <span asp-validation-for="VisitDate" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="Age" class="form-label"></label>
                                <input asp-for="Age" class="form-control" type="number" min="0" max="150" placeholder="العمر" />
                                <span asp-validation-for="Age" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="PhoneNumber" class="form-label"></label>
                                <input asp-for="PhoneNumber" class="form-control" placeholder="رقم الهاتف" />
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Province" class="form-label"></label>
                                <input asp-for="Province" class="form-control" placeholder="المحافظة" />
                                <span asp-validation-for="Province" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="VisitCountLabel" class="form-label"></label>
                                <select asp-for="VisitCountLabel" class="form-select">
                                    <option value="">-- اختر --</option>
                                    <option value="أولى">أولى</option>
                                    <option value="ثانية">ثانية</option>
                                    <option value="ثالثة">ثالثة</option>
                                    <option value="رابعة">رابعة</option>
                                    <option value="متابعة">متابعة</option>
                                </select>
                                <span asp-validation-for="VisitCountLabel" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="BookingStaff" class="form-label"></label>
                                <input asp-for="BookingStaff" class="form-control" placeholder="اسم موظف الحجز" />
                                <span asp-validation-for="BookingStaff" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Diagnosis" class="form-label"></label>
                                <textarea asp-for="Diagnosis" class="form-control" rows="3" placeholder="التشخيص (اختياري)"></textarea>
                                <span asp-validation-for="Diagnosis" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ المراجع
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
