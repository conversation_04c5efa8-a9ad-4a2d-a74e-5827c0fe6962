using System;
using System.IO;
using System.Threading.Tasks;
using HR_InvoiceArchiver.Data;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;

namespace HR_InvoiceArchiver.Services
{
    public interface IDatabaseMigrationService
    {
        Task MigrateAsync();
    }

    /// <summary>
    /// خدمة تطبيق تحديثات قاعدة البيانات
    /// </summary>
    public class DatabaseMigrationService : IDatabaseMigrationService
    {
        private readonly DatabaseContext _context;
        private readonly IToastService? _toastService;

        public DatabaseMigrationService(DatabaseContext context, IToastService? toastService = null)
        {
            _context = context;
            _toastService = toastService;
        }

        /// <summary>
        /// تطبيق Migration بسيط
        /// </summary>
        public async Task MigrateAsync()
        {
            try
            {
                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                await _context.Database.EnsureCreatedAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Migration error: {ex.Message}");
                // تجاهل الأخطاء - التطبيق سيعمل بدونها
            }
        }

        /// <summary>
        /// تطبيق جميع التحديثات المطلوبة
        /// </summary>
        public async Task ApplyMigrationsAsync()
        {
            try
            {
                // التأكد من وجود قاعدة البيانات
                await _context.Database.EnsureCreatedAsync();

                // التحقق من وجود أعمدة السحابة
                if (!await CloudColumnsExistAsync())
                {
                    await AddCloudSyncColumnsAsync();
                    _toastService?.ShowSuccess("تحديث قاعدة البيانات", "تم تحديث قاعدة البيانات لدعم النسخ الاحتياطي السحابي");
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في تحديث قاعدة البيانات", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود أعمدة السحابة
        /// </summary>
        private async Task<bool> CloudColumnsExistAsync()
        {
            try
            {
                var connectionString = _context.Database.GetConnectionString();
                using var connection = new SqliteConnection(connectionString);
                await connection.OpenAsync();

                // التحقق من وجود عمود CloudFileId في جدول Payments
                var command = connection.CreateCommand();
                command.CommandText = "PRAGMA table_info(Payments)";
                
                using var reader = await command.ExecuteReaderAsync();
                bool hasCloudFileId = false;
                
                while (await reader.ReadAsync())
                {
                    var columnName = reader.GetString(1); // العمود الثاني يحتوي على اسم العمود
                    if (columnName == "CloudFileId")
                    {
                        hasCloudFileId = true;
                        break;
                    }
                }

                return hasCloudFileId;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إضافة أعمدة النسخ الاحتياطي السحابي
        /// </summary>
        private async Task AddCloudSyncColumnsAsync()
        {
            var connectionString = _context.Database.GetConnectionString();
            using var connection = new SqliteConnection(connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();
            
            try
            {
                // إضافة الأعمدة لجدول Payments
                await ExecuteCommandAsync(connection, "ALTER TABLE Payments ADD COLUMN CloudFileId TEXT");
                await ExecuteCommandAsync(connection, "ALTER TABLE Payments ADD COLUMN SyncStatus INTEGER DEFAULT 0");
                await ExecuteCommandAsync(connection, "ALTER TABLE Payments ADD COLUMN LastSyncDate TEXT");

                // إضافة الأعمدة لجدول Invoices
                await ExecuteCommandAsync(connection, "ALTER TABLE Invoices ADD COLUMN CloudFileId TEXT");
                await ExecuteCommandAsync(connection, "ALTER TABLE Invoices ADD COLUMN SyncStatus INTEGER DEFAULT 0");
                await ExecuteCommandAsync(connection, "ALTER TABLE Invoices ADD COLUMN LastSyncDate TEXT");

                // تحديث البيانات الموجودة
                await ExecuteCommandAsync(connection, "UPDATE Payments SET SyncStatus = 0 WHERE SyncStatus IS NULL");
                await ExecuteCommandAsync(connection, "UPDATE Invoices SET SyncStatus = 0 WHERE SyncStatus IS NULL");

                transaction.Commit();
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// تنفيذ أمر SQL
        /// </summary>
        private async Task ExecuteCommandAsync(SqliteConnection connection, string commandText)
        {
            try
            {
                using var command = connection.CreateCommand();
                command.CommandText = commandText;
                await command.ExecuteNonQueryAsync();
            }
            catch (SqliteException ex)
            {
                // تجاهل خطأ "العمود موجود بالفعل"
                if (!ex.Message.Contains("duplicate column name"))
                {
                    throw;
                }
            }
        }

        /// <summary>
        /// التحقق من صحة قاعدة البيانات بعد التحديث
        /// </summary>
        public async Task<bool> ValidateDatabaseAsync()
        {
            try
            {
                // اختبار الاستعلام على الأعمدة الجديدة
                var testPayment = await _context.Payments
                    .Select(p => new { p.Id, p.CloudFileId, p.SyncStatus, p.LastSyncDate })
                    .FirstOrDefaultAsync();

                var testInvoice = await _context.Invoices
                    .Select(i => new { i.Id, i.CloudFileId, i.SyncStatus, i.LastSyncDate })
                    .FirstOrDefaultAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات قبل التحديث
        /// </summary>
        public async Task<bool> CreateBackupAsync()
        {
            try
            {
                var dbPath = GetDatabasePath();
                var backupPath = $"{dbPath}.backup_{DateTime.Now:yyyyMMdd_HHmmss}";

                if (File.Exists(dbPath))
                {
                    // Use async file operations for better performance
                    await Task.Run(() => File.Copy(dbPath, backupPath));
                    _toastService?.ShowInfo("نسخة احتياطية", $"تم إنشاء نسخة احتياطية: {Path.GetFileName(backupPath)}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في النسخة الاحتياطية", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// الحصول على مسار قاعدة البيانات
        /// </summary>
        private string GetDatabasePath()
        {
            return Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "HR_InvoiceArchiver", 
                "InvoiceArchiver.db");
        }

        /// <summary>
        /// تطبيق التحديثات مع إنشاء نسخة احتياطية
        /// </summary>
        public async Task ApplyMigrationsWithBackupAsync()
        {
            // إنشاء نسخة احتياطية أولاً
            await CreateBackupAsync();

            // تطبيق التحديثات
            await ApplyMigrationsAsync();

            // التحقق من صحة التحديث
            var isValid = await ValidateDatabaseAsync();
            
            if (isValid)
            {
                _toastService?.ShowSuccess("تحديث ناجح", "تم تحديث قاعدة البيانات بنجاح ودعم النسخ الاحتياطي السحابي");
            }
            else
            {
                _toastService?.ShowError("فشل التحديث", "فشل في التحقق من صحة التحديث");
                throw new Exception("فشل في التحقق من صحة تحديث قاعدة البيانات");
            }
        }
    }
}
