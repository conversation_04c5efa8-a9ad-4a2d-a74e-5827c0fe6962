<UserControl x:Class="HR_InvoiceArchiver.Pages.OffersPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:HR_InvoiceArchiver.Controls"
             FlowDirection="RightToLeft"
             Background="#F8F9FA">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HR_InvoiceArchiver;component/Styles/MaterialDesignStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource ModernCardStyle}" Margin="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🤝" FontSize="28" Margin="0,0,16,0"/>
                <StackPanel>
                    <TextBlock Text="عروض المندوبين" FontSize="26" FontWeight="Bold" Foreground="#1565C0"/>
                    <TextBlock Text="تسجيل وتحليل عروض المندوبين لاختيار أفضل عرض لكل مادة علمية" FontSize="15" Foreground="#546E7A"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <!-- Offers List & Search -->
            <materialDesign:Card Style="{StaticResource ModernCardStyle}">
                <StackPanel>
                    <!-- أزرار الإجراءات والبحث -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                        <!-- زر إضافة عرض جديد -->
                        <Button x:Name="AddOfferButton"
                              Style="{StaticResource ModernPrimaryButtonStyle}"
                              Width="140"
                              Margin="0,0,12,0"
                              Click="AddOfferButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                                <TextBlock Text="إضافة عرض جديد"/>
                            </StackPanel>
                        </Button>

                        <!-- فاصل -->
                        <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="0,0,12,0"/>

                        <!-- بحث وفلاتر -->
                        <TextBox x:Name="SearchTextBox" Width="200" Margin="0,0,12,0" materialDesign:HintAssist.Hint="بحث..."/>
                        <ComboBox x:Name="FilterScientificNameComboBox" Width="160" Margin="0,0,12,0" materialDesign:HintAssist.Hint="المادة العلمية"/>
                        <ComboBox x:Name="FilterOfficeComboBox" Width="140" Margin="0,0,12,0" materialDesign:HintAssist.Hint="المكتب العلمي"/>
                        <ComboBox x:Name="FilterRepComboBox" Width="140" Margin="0,0,12,0" materialDesign:HintAssist.Hint="المندوب"/>
                        <ComboBox x:Name="SortComboBox" Width="120" materialDesign:HintAssist.Hint="ترتيب حسب">
                            <ComboBoxItem Content="السعر الأقل"/>
                            <ComboBoxItem Content="السعر الأعلى"/>
                            <ComboBoxItem Content="أفضل عرض"/>
                        </ComboBox>
                    </StackPanel>
                    <!-- جدول العروض -->
                    <DataGrid x:Name="OffersDataGrid"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            IsReadOnly="True"
                            SelectionMode="Single"
                            GridLinesVisibility="None"
                            HeadersVisibility="Column"
                            Background="Transparent"
                            BorderThickness="0"
                            RowHeight="50"
                            FontSize="14"
                            AlternatingRowBackground="#FAFAFA"
                            MouseDoubleClick="OffersDataGrid_MouseDoubleClick">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="المكتب العلمي" Binding="{Binding ScientificOffice}" Width="*"/>
                            <DataGridTextColumn Header="المندوب" Binding="{Binding RepresentativeName}" Width="*"/>
                            <DataGridTextColumn Header="رقم المندوب" Binding="{Binding RepresentativePhone}" Width="*"/>
                            <DataGridTextColumn Header="المادة العلمية" Binding="{Binding ScientificName}" Width="*"/>
                            <DataGridTextColumn Header="المادة التجارية" Binding="{Binding TradeName}" Width="*"/>
                            <DataGridTextColumn Header="السعر" Binding="{Binding Price}" Width="*"/>
                            <DataGridTextColumn Header="البونص/الخصم" Binding="{Binding BonusOrDiscount}" Width="*"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="*"/>
                            <DataGridTemplateColumn Header="مرفق">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Content="عرض" CommandParameter="{Binding AttachmentPath}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="تاريخ الإدخال" Binding="{Binding CreatedAt, StringFormat=yyyy/MM/dd}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Offer Form Overlay -->
        <controls:OfferFormOverlay x:Name="OfferFormOverlay"
                                 Grid.RowSpan="2"
                                 FormClosed="OfferFormOverlay_FormClosed"/>
    </Grid>
</UserControl>