using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MedicalCenterSystem.Data;
using MedicalCenterSystem.Models;

namespace MedicalCenterSystem.Controllers
{
    public class ReportsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public ReportsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Reports
        public IActionResult Index()
        {
            return View();
        }

        // GET: Reports/CenterRevenue
        public async Task<IActionResult> CenterRevenue(DateTime? startDate, DateTime? endDate)
        {
            startDate ??= DateTime.Today.AddDays(-30);
            endDate ??= DateTime.Today;

            ViewBag.StartDate = startDate.Value.ToString("yyyy-MM-dd");
            ViewBag.EndDate = endDate.Value.ToString("yyyy-MM-dd");

            // Main Payments Revenue
            var mainPayments = await _context.MainPayments
                .Include(mp => mp.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Where(mp => mp.PaymentDate.Date >= startDate.Value.Date && mp.PaymentDate.Date <= endDate.Value.Date)
                .ToListAsync();

            // Referral Payments Revenue by Service
            var referralPayments = await _context.ReferralPayments
                .Include(rp => rp.MedicalService)
                .Include(rp => rp.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Where(rp => rp.PaymentDate.Date >= startDate.Value.Date && rp.PaymentDate.Date <= endDate.Value.Date)
                .ToListAsync();

            var revenueByService = referralPayments
                .GroupBy(rp => rp.MedicalService.ServiceName)
                .Select(g => new
                {
                    ServiceName = g.Key,
                    TotalAmount = g.Sum(rp => rp.Amount),
                    TotalCenterShare = g.Sum(rp => rp.CenterShare),
                    TotalDoctorShare = g.Sum(rp => rp.DoctorShare ?? 0),
                    Count = g.Count()
                })
                .OrderByDescending(x => x.TotalAmount)
                .ToList();

            ViewBag.MainPayments = mainPayments;
            ViewBag.RevenueByService = revenueByService;
            ViewBag.TotalMainRevenue = mainPayments.Sum(mp => mp.ConsultationFee + mp.ExamFee);
            ViewBag.TotalReferralRevenue = referralPayments.Sum(rp => rp.Amount);

            return View();
        }

        // GET: Reports/DoctorReferrals
        public async Task<IActionResult> DoctorReferrals(DateTime? startDate, DateTime? endDate, int? doctorId)
        {
            startDate ??= DateTime.Today.AddDays(-30);
            endDate ??= DateTime.Today;

            ViewBag.StartDate = startDate.Value.ToString("yyyy-MM-dd");
            ViewBag.EndDate = endDate.Value.ToString("yyyy-MM-dd");

            var doctors = await _context.Doctors.Where(d => d.IsActive).ToListAsync();
            ViewBag.Doctors = doctors;
            ViewBag.SelectedDoctorId = doctorId;

            var query = _context.ReferralPayments
                .Include(rp => rp.MedicalService)
                .Include(rp => rp.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Where(rp => rp.PaymentDate.Date >= startDate.Value.Date && rp.PaymentDate.Date <= endDate.Value.Date);

            if (doctorId.HasValue)
            {
                query = query.Where(rp => rp.PatientVisit.DoctorId == doctorId.Value);
            }

            var referralsByDoctor = await query
                .GroupBy(rp => new { rp.PatientVisit.Doctor.DoctorId, rp.PatientVisit.Doctor.FullName })
                .Select(g => new
                {
                    DoctorId = g.Key.DoctorId,
                    DoctorName = g.Key.FullName,
                    TotalReferrals = g.Count(),
                    TotalAmount = g.Sum(rp => rp.Amount),
                    TotalDoctorShare = g.Sum(rp => rp.DoctorShare ?? 0),
                    Services = g.GroupBy(rp => rp.MedicalService.ServiceName)
                        .Select(sg => new
                        {
                            ServiceName = sg.Key,
                            Count = sg.Count(),
                            Amount = sg.Sum(rp => rp.Amount)
                        }).ToList()
                })
                .OrderByDescending(x => x.TotalAmount)
                .ToListAsync();

            ViewBag.ReferralsByDoctor = referralsByDoctor;

            return View();
        }

        // GET: Reports/PatientDetails
        public async Task<IActionResult> PatientDetails(int? patientVisitId)
        {
            if (!patientVisitId.HasValue)
            {
                return View();
            }

            var patientVisit = await _context.PatientVisits
                .Include(pv => pv.Doctor)
                .Include(pv => pv.MainPayment)
                .Include(pv => pv.ReferralPayments)
                    .ThenInclude(rp => rp.MedicalService)
                .FirstOrDefaultAsync(pv => pv.PatientVisitId == patientVisitId.Value);

            if (patientVisit == null)
            {
                return NotFound();
            }

            return View(patientVisit);
        }

        // GET: Reports/DailyReport
        public async Task<IActionResult> DailyReport(DateTime? reportDate)
        {
            reportDate ??= DateTime.Today;
            ViewBag.ReportDate = reportDate.Value.ToString("yyyy-MM-dd");

            // Patient visits for the day
            var dailyVisits = await _context.PatientVisits
                .Include(pv => pv.Doctor)
                .Include(pv => pv.MainPayment)
                .Where(pv => pv.VisitDate.Date == reportDate.Value.Date)
                .OrderBy(pv => pv.VisitNumber)
                .ToListAsync();

            // Main payments for the day
            var dailyMainPayments = await _context.MainPayments
                .Include(mp => mp.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Where(mp => mp.PaymentDate.Date == reportDate.Value.Date)
                .ToListAsync();

            // Referral payments for the day
            var dailyReferralPayments = await _context.ReferralPayments
                .Include(rp => rp.MedicalService)
                .Include(rp => rp.PatientVisit)
                    .ThenInclude(pv => pv.Doctor)
                .Where(rp => rp.PaymentDate.Date == reportDate.Value.Date)
                .ToListAsync();

            ViewBag.DailyVisits = dailyVisits;
            ViewBag.DailyMainPayments = dailyMainPayments;
            ViewBag.DailyReferralPayments = dailyReferralPayments;

            // Summary calculations
            ViewBag.TotalVisits = dailyVisits.Count;
            ViewBag.TotalMainRevenue = dailyMainPayments.Sum(mp => mp.ConsultationFee + mp.ExamFee);
            ViewBag.TotalReferralRevenue = dailyReferralPayments.Sum(rp => rp.Amount);
            ViewBag.TotalDoctorShare = dailyMainPayments.Sum(mp => mp.DoctorShare) + dailyReferralPayments.Sum(rp => rp.DoctorShare ?? 0);
            ViewBag.TotalCenterShare = dailyMainPayments.Sum(mp => mp.CenterShare) + dailyReferralPayments.Sum(rp => rp.CenterShare);

            return View();
        }
    }
}
