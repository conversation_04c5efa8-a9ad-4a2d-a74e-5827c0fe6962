<UserControl x:Class="HR_InvoiceArchiver.Controls.Dashboard.StatisticsCardsControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="Transparent">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HR_InvoiceArchiver;component/Styles/MaterialDesignStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Simplified Card Style -->
            <Style x:Key="StatCardStyle" TargetType="materialDesign:Card">
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Padding" Value="20"/>
                <Setter Property="Background" Value="White"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#20000000" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="#30000000" BlurRadius="12" ShadowDepth="4" Direction="270"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Statistics Cards Grid -->
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Total Invoices Card -->
        <materialDesign:Card Grid.Column="0" Style="{StaticResource StatCardStyle}" 
                           x:Name="TotalInvoicesCard" Cursor="Hand"
                           Click="TotalInvoicesCard_Click">
            <StackPanel HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="FileDocument" Width="32" Height="32" 
                                       Foreground="#1976D2" Margin="0,0,0,8"/>
                <TextBlock x:Name="TotalInvoicesText" Text="0" 
                         FontSize="28" FontWeight="Bold" 
                         Foreground="#1976D2" HorizontalAlignment="Center"/>
                <TextBlock Text="إجمالي الفواتير" 
                         FontSize="12" Foreground="#666" 
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Total Amount Card -->
        <materialDesign:Card Grid.Column="1" Style="{StaticResource StatCardStyle}"
                           x:Name="TotalAmountCard" Cursor="Hand">
            <StackPanel HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="CurrencyUsd" Width="32" Height="32" 
                                       Foreground="#4CAF50" Margin="0,0,0,8"/>
                <TextBlock x:Name="TotalAmountText" Text="0 د.ع" 
                         FontSize="24" FontWeight="Bold" 
                         Foreground="#4CAF50" HorizontalAlignment="Center"/>
                <TextBlock Text="إجمالي المبلغ" 
                         FontSize="12" Foreground="#666" 
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Paid Amount Card -->
        <materialDesign:Card Grid.Column="2" Style="{StaticResource StatCardStyle}"
                           x:Name="PaidAmountCard" Cursor="Hand">
            <StackPanel HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="CheckCircle" Width="32" Height="32" 
                                       Foreground="#8BC34A" Margin="0,0,0,8"/>
                <TextBlock x:Name="PaidAmountText" Text="0 د.ع" 
                         FontSize="24" FontWeight="Bold" 
                         Foreground="#8BC34A" HorizontalAlignment="Center"/>
                <TextBlock Text="المبلغ المسدد" 
                         FontSize="12" Foreground="#666" 
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Outstanding Amount Card -->
        <materialDesign:Card Grid.Column="3" Style="{StaticResource StatCardStyle}"
                           x:Name="OutstandingAmountCard" Cursor="Hand">
            <StackPanel HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="AlertCircle" Width="32" Height="32" 
                                       Foreground="#FF9800" Margin="0,0,0,8"/>
                <TextBlock x:Name="OutstandingAmountText" Text="0 د.ع" 
                         FontSize="24" FontWeight="Bold" 
                         Foreground="#FF9800" HorizontalAlignment="Center"/>
                <TextBlock Text="المبلغ المتبقي" 
                         FontSize="12" Foreground="#666" 
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Overdue Invoices Card -->
        <materialDesign:Card Grid.Column="4" Style="{StaticResource StatCardStyle}"
                           x:Name="OverdueInvoicesCard" Cursor="Hand">
            <StackPanel HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="ClockAlert" Width="32" Height="32" 
                                       Foreground="#F44336" Margin="0,0,0,8"/>
                <TextBlock x:Name="OverdueInvoicesText" Text="0" 
                         FontSize="28" FontWeight="Bold" 
                         Foreground="#F44336" HorizontalAlignment="Center"/>
                <TextBlock Text="فواتير متأخرة" 
                         FontSize="12" Foreground="#666" 
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
