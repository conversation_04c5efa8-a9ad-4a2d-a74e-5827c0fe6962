using System;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using System.Text.Json;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;

namespace MedicalCenterWinForms.Services
{
    public class BackupService
    {
        private readonly DatabaseService _databaseService;
        private readonly SettingsService _settingsService;

        public BackupService(DatabaseService databaseService, SettingsService settingsService)
        {
            _databaseService = databaseService;
            _settingsService = settingsService;
        }

        // Create full backup
        public async Task<BackupResult> CreateFullBackup(string backupPath, string description = "")
        {
            try
            {
                var backupInfo = new BackupInfo
                {
                    BackupId = Guid.NewGuid().ToString(),
                    BackupDate = DateTime.Now,
                    BackupType = BackupType.Full,
                    Description = description,
                    Version = "1.0.0"
                };

                var tempDir = Path.Combine(Path.GetTempPath(), backupInfo.BackupId);
                Directory.CreateDirectory(tempDir);

                try
                {
                    // Export database data
                    await ExportDatabaseData(tempDir);

                    // Export settings
                    await ExportSettings(tempDir);

                    // Export system files (if any)
                    await ExportSystemFiles(tempDir);

                    // Create backup info file
                    var backupInfoJson = JsonSerializer.Serialize(backupInfo, new JsonSerializerOptions 
                    { 
                        WriteIndented = true,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });
                    await File.WriteAllTextAsync(Path.Combine(tempDir, "backup_info.json"), backupInfoJson);

                    // Create compressed backup file
                    var backupFileName = $"MedicalCenter_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
                    var fullBackupPath = Path.Combine(backupPath, backupFileName);
                    
                    ZipFile.CreateFromDirectory(tempDir, fullBackupPath);

                    // Calculate file size
                    var fileInfo = new FileInfo(fullBackupPath);
                    backupInfo.FileSize = fileInfo.Length;
                    backupInfo.FilePath = fullBackupPath;

                    return new BackupResult
                    {
                        Success = true,
                        BackupInfo = backupInfo,
                        Message = "تم إنشاء النسخة الاحتياطية بنجاح"
                    };
                }
                finally
                {
                    // Clean up temp directory
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                return new BackupResult
                {
                    Success = false,
                    Message = $"فشل في إنشاء النسخة الاحتياطية: {ex.Message}"
                };
            }
        }

        // Restore from backup
        public async Task<RestoreResult> RestoreFromBackup(string backupFilePath, bool confirmRestore = false)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                {
                    return new RestoreResult
                    {
                        Success = false,
                        Message = "ملف النسخة الاحتياطية غير موجود"
                    };
                }

                var tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
                Directory.CreateDirectory(tempDir);

                try
                {
                    // Extract backup file
                    ZipFile.ExtractToDirectory(backupFilePath, tempDir);

                    // Read backup info
                    var backupInfoPath = Path.Combine(tempDir, "backup_info.json");
                    if (!File.Exists(backupInfoPath))
                    {
                        return new RestoreResult
                        {
                            Success = false,
                            Message = "ملف معلومات النسخة الاحتياطية غير موجود"
                        };
                    }

                    var backupInfoJson = await File.ReadAllTextAsync(backupInfoPath);
                    var backupInfo = JsonSerializer.Deserialize<BackupInfo>(backupInfoJson);

                    if (backupInfo == null)
                    {
                        return new RestoreResult
                        {
                            Success = false,
                            Message = "فشل في قراءة معلومات النسخة الاحتياطية"
                        };
                    }

                    if (!confirmRestore)
                    {
                        return new RestoreResult
                        {
                            Success = false,
                            RequiresConfirmation = true,
                            BackupInfo = backupInfo,
                            Message = "تتطلب عملية الاستعادة تأكيد المستخدم"
                        };
                    }

                    // Create database backup before restore
                    var preRestoreBackup = await CreateFullBackup(
                        Path.GetDirectoryName(backupFilePath) ?? Path.GetTempPath(),
                        "نسخة احتياطية قبل الاستعادة");

                    // Restore database data
                    await RestoreDatabaseData(tempDir);

                    // Restore settings
                    await RestoreSettings(tempDir);

                    // Restore system files
                    await RestoreSystemFiles(tempDir);

                    return new RestoreResult
                    {
                        Success = true,
                        BackupInfo = backupInfo,
                        PreRestoreBackup = preRestoreBackup.BackupInfo,
                        Message = "تم استعادة النسخة الاحتياطية بنجاح"
                    };
                }
                finally
                {
                    // Clean up temp directory
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                return new RestoreResult
                {
                    Success = false,
                    Message = $"فشل في استعادة النسخة الاحتياطية: {ex.Message}"
                };
            }
        }

        // Verify backup integrity
        public async Task<BackupVerificationResult> VerifyBackup(string backupFilePath)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                {
                    return new BackupVerificationResult
                    {
                        IsValid = false,
                        Message = "ملف النسخة الاحتياطية غير موجود"
                    };
                }

                var tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
                Directory.CreateDirectory(tempDir);

                try
                {
                    // Extract and verify
                    ZipFile.ExtractToDirectory(backupFilePath, tempDir);

                    var issues = new List<string>();

                    // Check backup info
                    var backupInfoPath = Path.Combine(tempDir, "backup_info.json");
                    if (!File.Exists(backupInfoPath))
                    {
                        issues.Add("ملف معلومات النسخة الاحتياطية مفقود");
                    }
                    else
                    {
                        var backupInfoJson = await File.ReadAllTextAsync(backupInfoPath);
                        var backupInfo = JsonSerializer.Deserialize<BackupInfo>(backupInfoJson);
                        if (backupInfo == null)
                        {
                            issues.Add("فشل في قراءة معلومات النسخة الاحتياطية");
                        }
                    }

                    // Check database data
                    var databaseDataPath = Path.Combine(tempDir, "database_data.json");
                    if (!File.Exists(databaseDataPath))
                    {
                        issues.Add("ملف بيانات قاعدة البيانات مفقود");
                    }

                    // Check settings
                    var settingsPath = Path.Combine(tempDir, "settings.json");
                    if (!File.Exists(settingsPath))
                    {
                        issues.Add("ملف الإعدادات مفقود");
                    }

                    return new BackupVerificationResult
                    {
                        IsValid = issues.Count == 0,
                        Issues = issues,
                        Message = issues.Count == 0 ? "النسخة الاحتياطية صحيحة" : $"تم العثور على {issues.Count} مشكلة"
                    };
                }
                finally
                {
                    if (Directory.Exists(tempDir))
                    {
                        Directory.Delete(tempDir, true);
                    }
                }
            }
            catch (Exception ex)
            {
                return new BackupVerificationResult
                {
                    IsValid = false,
                    Message = $"خطأ في التحقق من النسخة الاحتياطية: {ex.Message}"
                };
            }
        }

        // Get backup information without extracting
        public async Task<BackupInfo?> GetBackupInfo(string backupFilePath)
        {
            try
            {
                using var archive = ZipFile.OpenRead(backupFilePath);
                var backupInfoEntry = archive.GetEntry("backup_info.json");
                
                if (backupInfoEntry == null) return null;

                using var stream = backupInfoEntry.Open();
                using var reader = new StreamReader(stream);
                var json = await reader.ReadToEndAsync();
                
                return JsonSerializer.Deserialize<BackupInfo>(json);
            }
            catch
            {
                return null;
            }
        }

        // Clean old backups based on retention policy
        public async Task<CleanupResult> CleanOldBackups(string backupDirectory, int retentionDays)
        {
            try
            {
                if (!Directory.Exists(backupDirectory))
                {
                    return new CleanupResult
                    {
                        Success = false,
                        Message = "مجلد النسخ الاحتياطية غير موجود"
                    };
                }

                var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                var backupFiles = Directory.GetFiles(backupDirectory, "*.zip")
                    .Where(f => File.GetCreationTime(f) < cutoffDate)
                    .ToList();

                var deletedCount = 0;
                var totalSize = 0L;

                foreach (var file in backupFiles)
                {
                    try
                    {
                        var fileInfo = new FileInfo(file);
                        totalSize += fileInfo.Length;
                        File.Delete(file);
                        deletedCount++;
                    }
                    catch
                    {
                        // Continue with other files if one fails
                    }
                }

                return new CleanupResult
                {
                    Success = true,
                    DeletedCount = deletedCount,
                    FreedSpace = totalSize,
                    Message = $"تم حذف {deletedCount} نسخة احتياطية قديمة"
                };
            }
            catch (Exception ex)
            {
                return new CleanupResult
                {
                    Success = false,
                    Message = $"خطأ في تنظيف النسخ الاحتياطية: {ex.Message}"
                };
            }
        }

        // Private helper methods
        private async Task ExportDatabaseData(string exportPath)
        {
            using var context = _databaseService.GetDbContext();

            var data = new
            {
                Doctors = await context.Doctors.ToListAsync(),
                MedicalServices = await context.MedicalServices.ToListAsync(),
                DoctorServices = await context.DoctorServices.ToListAsync(),
                PatientVisits = await context.PatientVisits.ToListAsync(),
                MainPayments = await context.MainPayments.ToListAsync(),
                ReferralPayments = await context.ReferralPayments.ToListAsync(),
                UserAccounts = await context.UserAccounts.ToListAsync(),
                SystemSettings = await context.SystemSettings.ToListAsync()
            };

            var json = JsonSerializer.Serialize(data, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await File.WriteAllTextAsync(Path.Combine(exportPath, "database_data.json"), json);
        }

        private async Task ExportSettings(string exportPath)
        {
            var settings = await _settingsService.GetAllSettings();
            var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await File.WriteAllTextAsync(Path.Combine(exportPath, "settings.json"), json);
        }

        private async Task ExportSystemFiles(string exportPath)
        {
            // Export configuration files, logs, etc.
            var systemFilesDir = Path.Combine(exportPath, "system_files");
            Directory.CreateDirectory(systemFilesDir);

            // Copy appsettings.json if exists
            var appSettingsPath = "appsettings.json";
            if (File.Exists(appSettingsPath))
            {
                File.Copy(appSettingsPath, Path.Combine(systemFilesDir, "appsettings.json"));
            }

            await Task.CompletedTask;
        }

        private async Task RestoreDatabaseData(string importPath)
        {
            var dataPath = Path.Combine(importPath, "database_data.json");
            if (!File.Exists(dataPath)) return;

            var json = await File.ReadAllTextAsync(dataPath);
            var data = JsonSerializer.Deserialize<DatabaseBackupData>(json);

            if (data == null) return;

            using var context = _databaseService.GetDbContext();

            // Clear existing data (be careful!)
            context.SystemSettings.RemoveRange(context.SystemSettings);
            context.ReferralPayments.RemoveRange(context.ReferralPayments);
            context.MainPayments.RemoveRange(context.MainPayments);
            context.PatientVisits.RemoveRange(context.PatientVisits);
            context.DoctorServices.RemoveRange(context.DoctorServices);
            context.UserAccounts.RemoveRange(context.UserAccounts);
            context.MedicalServices.RemoveRange(context.MedicalServices);
            context.Doctors.RemoveRange(context.Doctors);

            await context.SaveChangesAsync();

            // Restore data
            if (data.Doctors != null) context.Doctors.AddRange(data.Doctors);
            if (data.MedicalServices != null) context.MedicalServices.AddRange(data.MedicalServices);
            if (data.DoctorServices != null) context.DoctorServices.AddRange(data.DoctorServices);
            if (data.PatientVisits != null) context.PatientVisits.AddRange(data.PatientVisits);
            if (data.MainPayments != null) context.MainPayments.AddRange(data.MainPayments);
            if (data.ReferralPayments != null) context.ReferralPayments.AddRange(data.ReferralPayments);
            if (data.UserAccounts != null) context.UserAccounts.AddRange(data.UserAccounts);
            if (data.SystemSettings != null) context.SystemSettings.AddRange(data.SystemSettings);

            await context.SaveChangesAsync();
        }

        private async Task RestoreSettings(string importPath)
        {
            var settingsPath = Path.Combine(importPath, "settings.json");
            if (!File.Exists(settingsPath)) return;

            var json = await File.ReadAllTextAsync(settingsPath);
            var settings = JsonSerializer.Deserialize<Dictionary<string, object>>(json);

            if (settings != null)
            {
                await _settingsService.SaveAllSettings(settings);
            }
        }

        private async Task RestoreSystemFiles(string importPath)
        {
            var systemFilesDir = Path.Combine(importPath, "system_files");
            if (!Directory.Exists(systemFilesDir)) return;

            // Restore configuration files
            var appSettingsBackup = Path.Combine(systemFilesDir, "appsettings.json");
            if (File.Exists(appSettingsBackup))
            {
                File.Copy(appSettingsBackup, "appsettings.json", true);
            }

            await Task.CompletedTask;
        }
    }
}
