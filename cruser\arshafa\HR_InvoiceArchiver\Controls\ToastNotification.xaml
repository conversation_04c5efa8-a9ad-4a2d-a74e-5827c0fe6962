<UserControl x:Class="HR_InvoiceArchiver.Controls.ToastNotification"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <UserControl.Resources>
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                           From="400" To="0" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.3"/>
        </Storyboard>
        
        <Storyboard x:Key="SlideOutAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                           From="0" To="400" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.3"/>
        </Storyboard>
    </UserControl.Resources>
    
    <Border x:Name="ToastBorder"
            Background="{DynamicResource ToastBackground}"
            CornerRadius="8"
            Padding="16,12"
            Margin="16,8"
            MaxWidth="400"
            MinHeight="56"
            HorizontalAlignment="Right"
            VerticalAlignment="Top">
        
        <Border.Effect>
            <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="4" BlurRadius="12"/>
        </Border.Effect>
        
        <Border.RenderTransform>
            <TranslateTransform/>
        </Border.RenderTransform>
        
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- Icon -->
            <materialDesign:PackIcon x:Name="ToastIcon"
                                   Grid.Column="0"
                                   Kind="Information"
                                   Width="24"
                                   Height="24"
                                   Foreground="{DynamicResource ToastIconBrush}"
                                   VerticalAlignment="Center"
                                   Margin="0,0,12,0"/>
            
            <!-- Content -->
            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                <TextBlock x:Name="TitleTextBlock"
                         Text="عنوان التنبيه"
                         FontWeight="Medium"
                         FontSize="14"
                         Foreground="{DynamicResource ToastTextBrush}"
                         FlowDirection="RightToLeft"
                         TextWrapping="Wrap"/>
                
                <TextBlock x:Name="MessageTextBlock"
                         Text="رسالة التنبيه"
                         FontSize="12"
                         Foreground="{DynamicResource ToastTextBrush}"
                         FlowDirection="RightToLeft"
                         TextWrapping="Wrap"
                         Margin="0,4,0,0"
                         Opacity="0.8"/>
            </StackPanel>
            
            <!-- Close Button -->
            <Button x:Name="CloseButton"
                    Grid.Column="2"
                    Style="{StaticResource MaterialDesignIconButton}"
                    Width="32"
                    Height="32"
                    Padding="0"
                    Foreground="{DynamicResource ToastTextBrush}"
                    VerticalAlignment="Top"
                    Click="CloseButton_Click">
                <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
            </Button>
        </Grid>
    </Border>
</UserControl>
