using Microsoft.EntityFrameworkCore;
using MedicalCenterWinForms.Models;
using MedicalCenterWinForms.Services;
using MedicalCenterWinForms.Helpers;

namespace MedicalCenterWinForms.Controls
{
    public partial class PatientManagementControl : BaseUserControl
    {
        private List<PatientVisit> _patientVisits = new List<PatientVisit>();

        public PatientManagementControl() : base()
        {
            InitializeComponent();
            LoadPatientVisits();
        }

        public PatientManagementControl(DatabaseService databaseService) : base(databaseService)
        {
            InitializeComponent();
            LoadPatientVisits();
        }

        private async void LoadPatientVisits()
        {
            try
            {
                SetLoadingState(true);
                
                using var context = DatabaseService.GetDbContext();
                _patientVisits = await context.PatientVisits
                    .Include(pv => pv.Doctor)
                    .Include(pv => pv.MainPayment)
                    .OrderByDescending(pv => pv.VisitDate)
                    .ThenByDescending(pv => pv.VisitNumber)
                    .ToListAsync();
                
                RefreshPatientsList();
                lblTotalCount.Text = $"إجمالي المراجعين: {_patientVisits.Count}";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        private void RefreshPatientsList()
        {
            dgvPatients.DataSource = _patientVisits.Select(pv => new
            {
                pv.PatientVisitId,
                التاريخ = pv.VisitDate.ToString("yyyy-MM-dd"),
                رقم_المراجع = pv.VisitNumber,
                اسم_المريض = pv.PatientName,
                الطبيب = pv.Doctor?.FullName ?? "",
                العمر = pv.Age,
                المحافظة = pv.Province,
                الهاتف = pv.PhoneNumber,
                مدفوع = pv.MainPayment != null ? "نعم" : "لا"
            }).ToList();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            var searchText = txtSearch.Text.ToLower();
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                RefreshPatientsList();
            }
            else
            {
                var filtered = _patientVisits.Where(pv => 
                    pv.PatientName.ToLower().Contains(searchText) || 
                    (pv.Doctor?.FullName?.ToLower().Contains(searchText) ?? false) ||
                    pv.Province.ToLower().Contains(searchText) ||
                    pv.PhoneNumber.Contains(searchText))
                    .Select(pv => new
                    {
                        pv.PatientVisitId,
                        التاريخ = pv.VisitDate.ToString("yyyy-MM-dd"),
                        رقم_المراجع = pv.VisitNumber,
                        اسم_المريض = pv.PatientName,
                        الطبيب = pv.Doctor?.FullName ?? "",
                        العمر = pv.Age,
                        المحافظة = pv.Province,
                        الهاتف = pv.PhoneNumber,
                        مدفوع = pv.MainPayment != null ? "نعم" : "لا"
                    }).ToList();

                dgvPatients.DataSource = filtered;
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            var editControl = new PatientEditControl(DatabaseService);
            editControl.PatientSaved += (s, args) => LoadPatientVisits();
            ShowEditDialog(editControl, "إضافة مراجع جديد");
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvPatients.SelectedRows.Count == 0)
            {
                ShowWarning("يرجى اختيار مراجع للتعديل");
                return;
            }

            var selectedRow = dgvPatients.SelectedRows[0];
            var patientVisitId = (int)selectedRow.Cells["PatientVisitId"].Value;
            var patientVisit = _patientVisits.FirstOrDefault(pv => pv.PatientVisitId == patientVisitId);

            if (patientVisit != null)
            {
                var editControl = new PatientEditControl(DatabaseService, patientVisit);
                editControl.PatientSaved += (s, args) => LoadPatientVisits();
                ShowEditDialog(editControl, "تعديل المراجع");
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvPatients.SelectedRows.Count == 0)
            {
                ShowWarning("يرجى اختيار مراجع للحذف");
                return;
            }

            var selectedRow = dgvPatients.SelectedRows[0];
            var patientVisitId = (int)selectedRow.Cells["PatientVisitId"].Value;
            var patientVisit = _patientVisits.FirstOrDefault(pv => pv.PatientVisitId == patientVisitId);

            if (patientVisit != null)
            {
                if (!ShowConfirmation($"هل تريد حذف مراجعة '{patientVisit.PatientName}'؟"))
                    return;

                try
                {
                    SetLoadingState(true);
                    
                    using var context = DatabaseService.GetDbContext();
                    
                    // Remove related payments first
                    var mainPayment = await context.MainPayments.FirstOrDefaultAsync(mp => mp.PatientVisitId == patientVisitId);
                    if (mainPayment != null)
                    {
                        context.MainPayments.Remove(mainPayment);
                    }

                    var referralPayments = await context.ReferralPayments.Where(rp => rp.PatientVisitId == patientVisitId).ToListAsync();
                    context.ReferralPayments.RemoveRange(referralPayments);

                    // Remove patient visit
                    var visitToRemove = await context.PatientVisits.FindAsync(patientVisitId);
                    if (visitToRemove != null)
                    {
                        context.PatientVisits.Remove(visitToRemove);
                    }

                    await context.SaveChangesAsync();
                    
                    ShowSuccess("تم حذف المراجعة بنجاح");
                    LoadPatientVisits();
                }
                catch (Exception ex)
                {
                    ShowError($"خطأ في حذف المراجعة: {ex.Message}");
                }
                finally
                {
                    SetLoadingState(false);
                }
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadPatientVisits();
        }

        private void btnPayment_Click(object sender, EventArgs e)
        {
            if (dgvPatients.SelectedRows.Count == 0)
            {
                ShowWarning("يرجى اختيار مراجع لإضافة دفع");
                return;
            }

            ShowWarning("نموذج المدفوعات قيد التطوير");
        }

        private void dgvPatients_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void ShowEditDialog(UserControl control, string title)
        {
            var form = new Form
            {
                Text = title,
                Size = new Size(700, 600),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            control.Dock = DockStyle.Fill;
            form.Controls.Add(control);
            form.ShowDialog();
        }

        public void RefreshData()
        {
            LoadPatientVisits();
        }
    }

    public partial class PatientManagementControl
    {
        private DataGridView dgvPatients;
        private TextBox txtSearch;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private Button btnPayment;
        private Label lblSearch;
        private Label lblTotalCount;
        private Panel pnlTop;
        private Panel pnlBottom;

        private void InitializeComponent()
        {
            this.dgvPatients = CreateStyledDataGridView();
            this.txtSearch = CreateStyledTextBox("ابحث بالاسم أو الطبيب أو المحافظة أو الهاتف...");
            this.lblSearch = CreateStyledLabel("بحث:", true);
            this.lblTotalCount = CreateStyledLabel("إجمالي المراجعين: 0", true);
            this.pnlTop = CreateMaterialCard(1);
            this.pnlBottom = CreateMaterialCard(1);

            this.btnAdd = CreateStyledButton("إضافة مراجع", MaterialDesignHelper.Colors.Primary, btnAdd_Click);
            this.btnEdit = CreateStyledButton("تعديل", MaterialDesignHelper.Colors.Warning, btnEdit_Click);
            this.btnDelete = CreateStyledButton("حذف", MaterialDesignHelper.Colors.Error, btnDelete_Click);
            this.btnPayment = CreateStyledButton("دفع", MaterialDesignHelper.Colors.Success, btnPayment_Click);
            this.btnRefresh = CreateStyledButton("تحديث", MaterialDesignHelper.Colors.Secondary, btnRefresh_Click);

            this.SuspendLayout();

            // pnlTop
            this.pnlTop.Controls.Add(this.lblSearch);
            this.pnlTop.Controls.Add(this.txtSearch);
            this.pnlTop.Controls.Add(this.btnAdd);
            this.pnlTop.Controls.Add(this.btnEdit);
            this.pnlTop.Controls.Add(this.btnDelete);
            this.pnlTop.Controls.Add(this.btnPayment);
            this.pnlTop.Controls.Add(this.btnRefresh);
            this.pnlTop.Dock = DockStyle.Top;
            this.pnlTop.Height = 80;

            // Controls positioning in pnlTop
            this.lblSearch.Location = new Point(20, 20);
            this.txtSearch.Location = new Point(20, 45);
            this.txtSearch.Size = new Size(350, 25);
            this.txtSearch.TextChanged += txtSearch_TextChanged;

            this.btnAdd.Location = new Point(400, 20);
            this.btnEdit.Location = new Point(510, 20);
            this.btnDelete.Location = new Point(620, 20);
            this.btnPayment.Location = new Point(730, 20);
            this.btnRefresh.Location = new Point(840, 20);

            // dgvPatients
            this.dgvPatients.Dock = DockStyle.Fill;
            this.dgvPatients.CellDoubleClick += dgvPatients_CellDoubleClick;

            // pnlBottom
            this.pnlBottom.Controls.Add(this.lblTotalCount);
            this.pnlBottom.Dock = DockStyle.Bottom;
            this.pnlBottom.Height = 50;
            this.lblTotalCount.Location = new Point(20, 15);

            // PatientManagementControl
            this.Controls.Add(this.dgvPatients);
            this.Controls.Add(this.pnlTop);
            this.Controls.Add(this.pnlBottom);
            this.Name = "PatientManagementControl";
            this.Size = new Size(1000, 600);

            this.ResumeLayout(false);
        }
    }
}
