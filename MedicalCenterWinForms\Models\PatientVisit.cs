using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MedicalCenterWinForms.Models
{
    public class PatientVisit
    {
        public int PatientVisitId { get; set; }

        [Required]
        public DateTime VisitDate { get; set; } = DateTime.Today;

        [Required]
        public int DoctorId { get; set; }

        public int VisitNumber { get; set; } // يبدأ من 1 لكل دكتور يومياً

        [Required]
        [StringLength(100)]
        public string PatientName { get; set; } = string.Empty;

        [StringLength(255)]
        public string Diagnosis { get; set; } = string.Empty;

        public int Age { get; set; }

        [StringLength(100)]
        public string Province { get; set; } = string.Empty;

        [StringLength(100)]
        public string BookingStaff { get; set; } = string.Empty;

        [StringLength(50)]
        public string VisitCountLabel { get; set; } = string.Empty; // أولى، ثانية...

        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        // Navigation Properties
        [ForeignKey("DoctorId")]
        public virtual Doctor Doctor { get; set; } = null!;

        public virtual MainPayment? MainPayment { get; set; }
        public virtual ICollection<ReferralPayment> ReferralPayments { get; set; } = new List<ReferralPayment>();
    }
}
