<UserControl x:Class="HR_InvoiceArchiver.Controls.ConfirmationDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ConfirmButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="45"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="{Binding Background.Color, RelativeSource={RelativeSource TemplatedParent}}"
                                                Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.02" ScaleY="1.02"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <!-- Overlay Background -->
    <Grid Background="#80000000">
        <!-- Dialog Card -->
        <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                           Padding="40" MaxWidth="500" MinWidth="400"
                           materialDesign:ElevationAssist.Elevation="Dp8">
            <StackPanel>
                <!-- Icon and Title -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,25">
                    <Border Background="#FFEBEE" CornerRadius="50" Width="80" Height="80" Margin="0,0,20,0">
                        <materialDesign:PackIcon x:Name="DialogIcon" Kind="AlertCircle" Width="40" Height="40"
                                               Foreground="#DC3545" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock x:Name="DialogTitle" Text="تأكيد الحذف" FontSize="24" FontWeight="Bold"
                                 Foreground="#2C3E50" Margin="0,0,0,5"/>
                        <TextBlock Text="عملية لا يمكن التراجع عنها" FontSize="14" Foreground="#6C757D"/>
                    </StackPanel>
                </StackPanel>

                <!-- Message -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,25">
                    <TextBlock x:Name="DialogMessage" 
                             Text="هل أنت متأكد من رغبتك في حذف هذه المدفوعة؟ لن تتمكن من التراجع عن هذا الإجراء."
                             FontSize="16" TextWrapping="Wrap" HorizontalAlignment="Center"
                             Foreground="#495057" LineHeight="24"/>
                </Border>

                <!-- Payment Details (if provided) -->
                <Border x:Name="PaymentDetailsPanel" Background="#E3F2FD" CornerRadius="8" Padding="20" 
                        Margin="0,0,0,25" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="تفاصيل المدفوعة:" FontWeight="SemiBold" FontSize="14" 
                                 Foreground="#1976D2" Margin="0,0,0,10"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الوصل:" FontWeight="SemiBold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="ReceiptNumberText" Text="" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="المورد:" FontWeight="SemiBold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="SupplierNameText" Text="" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="المبلغ:" FontWeight="SemiBold" Margin="0,0,10,0"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" x:Name="AmountText" Text="" FontWeight="Bold" Foreground="#28A745"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="ConfirmButton"
                           Style="{StaticResource ConfirmButtonStyle}"
                           Background="#DC3545"
                           Click="ConfirmButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="نعم، احذف"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="CancelButton"
                           Style="{StaticResource ConfirmButtonStyle}"
                           Background="#6C757D"
                           Click="CancelButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Cancel" Width="18" Height="18" Margin="0,0,8,0"/>
                            <TextBlock Text="إلغاء"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
