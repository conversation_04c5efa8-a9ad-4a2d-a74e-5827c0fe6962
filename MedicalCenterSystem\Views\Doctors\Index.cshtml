@model IEnumerable<MedicalCenterSystem.Models.Doctor>

@{
    ViewData["Title"] = "الأطباء";
}

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2>@ViewData["Title"]</h2>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة طبيب جديد
            </a>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الطبيب</th>
                                <th>التخصص</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr>
                                    <td>@item.DoctorId</td>
                                    <td>@item.FullName</td>
                                    <td>@item.Specialty</td>
                                    <td>
                                        @if (item.IsActive)
                                        {
                                            <span class="badge bg-success">نشط</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">غير نشط</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a asp-action="Details" asp-route-id="@item.DoctorId" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@item.DoctorId" class="btn btn-warning btn-sm">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.DoctorId" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                @if (!Model.Any())
                {
                    <div class="text-center py-4">
                        <p class="text-muted">لا توجد أطباء مسجلين حالياً</p>
                        <a asp-action="Create" class="btn btn-primary">إضافة أول طبيب</a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
}
