@model IEnumerable<MedicalCenterSystem.Models.PatientVisit>

@{
    ViewData["Title"] = "المراجعين";
}

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2>@ViewData["Title"]</h2>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus"></i> تسجيل مراجع جديد
            </a>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم المراجع</th>
                                <th>التاريخ</th>
                                <th>اسم المريض</th>
                                <th>الطبيب</th>
                                <th>العمر</th>
                                <th>المحافظة</th>
                                <th>الهاتف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr>
                                    <td>@item.VisitNumber</td>
                                    <td>@item.VisitDate.ToString("yyyy-MM-dd")</td>
                                    <td>@item.PatientName</td>
                                    <td>@item.Doctor?.FullName</td>
                                    <td>@item.Age</td>
                                    <td>@item.Province</td>
                                    <td>@item.PhoneNumber</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a asp-action="Details" asp-route-id="@item.PatientVisitId" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@item.PatientVisitId" class="btn btn-warning btn-sm">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                            @if (item.MainPayment == null)
                                            {
                                                <a asp-controller="MainPayments" asp-action="Create" asp-route-patientVisitId="@item.PatientVisitId" class="btn btn-success btn-sm">
                                                    <i class="fas fa-money-bill"></i> دفع رئيسي
                                                </a>
                                            }
                                            <a asp-controller="ReferralPayments" asp-action="Create" asp-route-patientVisitId="@item.PatientVisitId" class="btn btn-primary btn-sm">
                                                <i class="fas fa-plus-circle"></i> تحويل
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                @if (!Model.Any())
                {
                    <div class="text-center py-4">
                        <p class="text-muted">لا توجد مراجعات مسجلة حالياً</p>
                        <a asp-action="Create" class="btn btn-primary">تسجيل أول مراجع</a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
}
