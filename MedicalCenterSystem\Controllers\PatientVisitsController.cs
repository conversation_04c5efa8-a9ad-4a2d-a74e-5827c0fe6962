using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using MedicalCenterSystem.Data;
using MedicalCenterSystem.Models;

namespace MedicalCenterSystem.Controllers
{
    public class PatientVisitsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public PatientVisitsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: PatientVisits
        public async Task<IActionResult> Index()
        {
            var patientVisits = await _context.PatientVisits
                .Include(p => p.Doctor)
                .OrderByDescending(p => p.VisitDate)
                .ThenByDescending(p => p.VisitNumber)
                .ToListAsync();
            return View(patientVisits);
        }

        // GET: PatientVisits/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var patientVisit = await _context.PatientVisits
                .Include(p => p.Doctor)
                .Include(p => p.MainPayment)
                .Include(p => p.ReferralPayments)
                    .ThenInclude(rp => rp.MedicalService)
                .FirstOrDefaultAsync(m => m.PatientVisitId == id);
            if (patientVisit == null)
            {
                return NotFound();
            }

            return View(patientVisit);
        }

        // GET: PatientVisits/Create
        public IActionResult Create()
        {
            ViewData["DoctorId"] = new SelectList(_context.Doctors.Where(d => d.IsActive), "DoctorId", "FullName");
            var model = new PatientVisit
            {
                VisitDate = DateTime.Today
            };
            return View(model);
        }

        // POST: PatientVisits/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("PatientVisitId,VisitDate,DoctorId,PatientName,Diagnosis,Age,Province,BookingStaff,VisitCountLabel,PhoneNumber")] PatientVisit patientVisit)
        {
            if (ModelState.IsValid)
            {
                // Generate visit number automatically
                var maxVisitNumber = await _context.PatientVisits
                    .Where(pv => pv.DoctorId == patientVisit.DoctorId && pv.VisitDate.Date == patientVisit.VisitDate.Date)
                    .MaxAsync(pv => (int?)pv.VisitNumber) ?? 0;

                patientVisit.VisitNumber = maxVisitNumber + 1;

                _context.Add(patientVisit);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            ViewData["DoctorId"] = new SelectList(_context.Doctors.Where(d => d.IsActive), "DoctorId", "FullName", patientVisit.DoctorId);
            return View(patientVisit);
        }

        // GET: PatientVisits/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var patientVisit = await _context.PatientVisits.FindAsync(id);
            if (patientVisit == null)
            {
                return NotFound();
            }
            ViewData["DoctorId"] = new SelectList(_context.Doctors.Where(d => d.IsActive), "DoctorId", "FullName", patientVisit.DoctorId);
            return View(patientVisit);
        }

        // POST: PatientVisits/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("PatientVisitId,VisitDate,DoctorId,VisitNumber,PatientName,Diagnosis,Age,Province,BookingStaff,VisitCountLabel,PhoneNumber")] PatientVisit patientVisit)
        {
            if (id != patientVisit.PatientVisitId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(patientVisit);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PatientVisitExists(patientVisit.PatientVisitId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["DoctorId"] = new SelectList(_context.Doctors.Where(d => d.IsActive), "DoctorId", "FullName", patientVisit.DoctorId);
            return View(patientVisit);
        }

        // GET: PatientVisits/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var patientVisit = await _context.PatientVisits
                .Include(p => p.Doctor)
                .FirstOrDefaultAsync(m => m.PatientVisitId == id);
            if (patientVisit == null)
            {
                return NotFound();
            }

            return View(patientVisit);
        }

        // POST: PatientVisits/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var patientVisit = await _context.PatientVisits.FindAsync(id);
            if (patientVisit != null)
            {
                _context.PatientVisits.Remove(patientVisit);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool PatientVisitExists(int id)
        {
            return _context.PatientVisits.Any(e => e.PatientVisitId == id);
        }
    }
}
